package com.logisteq.tms.common.util;

import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;

public final class TestUtil {
	private TestUtil() {
		throw new AssertionError();
	}
	
	public static String createUrlWithParam(String url, String param, String paramValue) {		
		return url.replace("{" + param + "}", paramValue);
	}
		
	public static MockHttpServletRequestBuilder createTestRequestBuilder(HttpMethod method, String url, Long projectId, String body) {
		MockHttpServletRequestBuilder requestBuilder = null;
		
		if (HttpMethod.POST.equals(method)) {
			requestBuilder = post(url);
		} else if (HttpMethod.GET.equals(method)) {
			requestBuilder = get(url);
		} else if (HttpMethod.PUT.equals(method)) {
			requestBuilder = put(url);
		} else if (HttpMethod.DELETE.equals(method)) {
			requestBuilder = delete(url);
		} else {
			return null;
		}
		
		requestBuilder = requestBuilder.accept(MediaType.APPLICATION_JSON)
				.contentType(MediaType.APPLICATION_JSON);
		
		if (projectId != null) {
			requestBuilder = requestBuilder.header("projectId", projectId);
		}

		if (body != null) {
			requestBuilder = requestBuilder.content(body);
		}
		return requestBuilder;
	}
	
	public static MockHttpServletRequestBuilder createTestRequestBuilder(HttpMethod method, String url, String body) {
		return createTestRequestBuilder(method, url, null, body);
	}
	
    public static void pauseSeconds(int seconds) {
        try {
            Thread.sleep(seconds * 1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
}
