package com.logisteq.tms.common.component;

import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @created 2021-12-21
 * @project tms-service
 */
public interface FileManager {

    /**
     * 멀티파트 파일을 Public으로 업로드한다.
     *
     * @param multipartFile
     * @param folderName
     * @param fileName
     * @return
     * @throws IOException
     */
    String uploadPublicObject(MultipartFile multipartFile,
                              String folderName,
                              String fileName) throws IOException;

    /**
     * 멀티파트 파일을 Private으로 업로드한다.
     *
     * @param multipartFile
     * @param folderName
     * @param fileName
     * @return
     * @throws IOException
     */
    String uploadPrivateObject(MultipartFile multipartFile,
                               String folderName,
                               String fileName) throws IOException;

    /**
     * 입력스트림을 Private으로 업로드한다.
     *
     * @param inputStream
     * @param contentType
     * @param contentSize
     * @param folderName
     * @param fileName
     * @return
     * @throws IOException
     */
    String uploadPrivateObject(InputStream inputStream,
                               String contentType,
                               long contentSize,
                               String folderName,
                               String fileName) throws IOException;

    /**
     * Public 파일을 리소스로 다운로드한다.
     *
     * @param folderName
     * @param fileName
     * @return
     * @throws IOException
     */
    Resource downloadPublicObject(String folderName,
                                  String fileName) throws IOException;

    /**
     * Public 파일을 스트림으로 다운로드한다.
     *
     * @param folderName
     * @param fileName
     * @return
     * @throws IOException
     */
    InputStream downloadPublicObjectAsStream(String folderName,
                                             String fileName) throws IOException;

    /**
     * Public 파일을 삭제한다.
     *
     * @param folderName
     * @param fileName
     * @return
     * @throws IOException
     */
    String deletePublicObject(String folderName,
                              String fileName) throws IOException;

    /**
     * Public 파일을 복사한다.
     *
     * @param sourceFolderName
     * @param sourceFileName
     * @param destinationFolderName
     * @param destinationFileName
     * @return
     * @throws IOException
     */
    String copyPublicObject(String sourceFolderName,
                             String sourceFileName,
                             String destinationFolderName,
                             String destinationFileName) throws IOException;

    /**
     * Private 파일을 삭제한다.
     *
     * @param folderName
     * @param fileName
     * @return
     * @throws IOException
     */
    String deletePrivateObject(String folderName,
                               String fileName) throws IOException;

    /**
     * Private 파일을 복사한다.
     *
     * @param sourceFolderName
     * @param sourceFileName
     * @param destinationFolderName
     * @param destinationFileName
     * @return
     * @throws IOException
     */
    String copyPrivateObject(String sourceFolderName,
                             String sourceFileName,
                             String destinationFolderName,
                             String destinationFileName) throws IOException;

    /**
     * Private 파일을 이동한다.
     *
     * @param sourceFolderName
     * @param sourceFileName
     * @param destinationFolderName
     * @param destinationFileName
     * @return
     * @throws IOException
     */
    String movePrivateObject(String sourceFolderName,
                             String sourceFileName,
                             String destinationFolderName,
                             String destinationFileName) throws IOException;

    /**
     * 파일이 저장될 url 주소를 반환한다.
     *
     * @param filePath
     * @return
     */
    String getPublicObjectUrl(String filePath);

}
