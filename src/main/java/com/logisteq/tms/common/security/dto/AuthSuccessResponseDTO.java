package com.logisteq.tms.common.security.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;

import java.util.List;

@Builder
@Schema(description = "인증 성공 응답 DTO")
public class AuthSuccessResponseDTO {

    @Builder
    @Schema(description = "경고 정보")
    public static class Warning {
        @Schema(description = "경고 코드", example = "W001")
        private String warningCode;

        @Schema(description = "경고 메시지", example = "비밀번호 만료가 임박했습니다.")
        private String warningMsg;
    }

    @Schema(description = "사용자 ID", example = "123")
    private Long id;

    @Schema(description = "사용자 이름", example = "홍길동")
    private String name;

    @Schema(description = "이메일 주소", example = "<EMAIL>")
    private String email;

    @Schema(description = "조직 ID", example = "456")
    private Long organizationId;

    @Schema(description = "딥링크 열기 여부", example = "true")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean openDeepLink;

    @Schema(description = "FMS 서비스 여부", example = "true")
    private boolean isFMSService;

    @Schema(description = "조직 코드명", example = "LOGISTICS_DEPT")
    private String orgCodeName;

    @Schema(description = "사용자 권한 목록", example = "[\"ADMIN\", \"USER\"]")
    private List<String> roles;

    @Schema(description = "읽기 전용 여부", example = "false")
    private boolean readonly;

    @Schema(description = "부서 ID 목록", example = "[1, 2, 3]")
    private List<Long> departmentIdList;

    @Schema(description = "프로필 이미지 파일 URL", example = "https://example.com/profile.jpg")
    private String profileImgFileUrl;

    @Schema(description = "전화번호", example = "010-1234-5678")
    private String phoneNumber;

    @Schema(description = "경고 목록")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Warning> warnings;

}

