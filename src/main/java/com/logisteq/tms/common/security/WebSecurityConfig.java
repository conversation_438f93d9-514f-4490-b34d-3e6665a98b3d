package com.logisteq.tms.common.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.logisteq.common.exception.CustomException;
import com.logisteq.common.util.DateTimeUtil;
import com.logisteq.common.util.HttpUtil;
import com.logisteq.tms.auth.service.AuthKeyService;
import com.logisteq.tms.common.properties.PolicySecurityProperties;
import com.logisteq.tms.common.security.dto.AuthFailureResponseDTO;
import com.logisteq.tms.common.security.dto.AuthSuccessResponseDTO;
import com.logisteq.tms.file.domain.suppl.FileCategory;
import com.logisteq.tms.file.domain.suppl.FileType;
import com.logisteq.tms.file.service.FileService;
import com.logisteq.tms.privacy.dto.PrivacyRecordDto;
import com.logisteq.tms.privacy.service.PrivacyRecordService;
import com.logisteq.tms.privacy.suppl.PrivacyRecordType;
import com.logisteq.tms.privacy.suppl.PrivacyUsageType;
import com.logisteq.tms.push.service.PushService;
import com.logisteq.tms.user.constant.RoleType;
import com.logisteq.tms.user.constant.UserType;
import com.logisteq.tms.user.domain.Organization;
import com.logisteq.tms.user.domain.User;
import com.logisteq.tms.user.service.*;
import com.logisteq.tms.web.constant.WebUrl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.*;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetailsChecker;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.logout.LogoutHandler;
import org.springframework.security.web.authentication.rememberme.RememberMeAuthenticationFilter;
import org.springframework.security.web.authentication.rememberme.TokenBasedRememberMeServices;
import org.springframework.security.web.firewall.DefaultHttpFirewall;
import org.springframework.security.web.firewall.HttpFirewall;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import javax.servlet.http.HttpSession;
import java.io.PrintWriter;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Order(1)
@Configuration
@EnableWebSecurity
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {

    private final PasswordEncoder passwordEncoder;
    private final UserAccessHistoryService userAccessHistoryService;
    private final PushService pushService;
    private final FileService fileService;
    private final UserService userService;
    private final WebUserDetailsService webUserDetailsService;
    private final AuthKeyService authKeyService;
    private final OrganizationService organizationService;
    private final DepartmentService departmentService;
    private final UserDepartmentService userDepartmentService;
    private final PrivacyRecordService privacyRecordService;
    private final ObjectMapper objectMapper;
    private final PolicySecurityProperties policySecurityProperties;

    @Autowired
    public WebSecurityConfig(final PasswordEncoder passwordEncoder,
                             final UserAccessHistoryService userAccessHistoryService,
                             final PushService pushService,
                             final FileService fileService,
                             final UserService userService,
                             final WebUserDetailsService webUserDetailsService,
                             final AuthKeyService authKeyService,
                             final OrganizationService organizationService,
                             final DepartmentService departmentService,
                             final UserDepartmentService userDepartmentService,
                             final PrivacyRecordService privacyRecordService,
                             final ObjectMapper objectMapper,
                             final PolicySecurityProperties policySecurityProperties) {

        this.passwordEncoder = passwordEncoder;
        this.userAccessHistoryService = userAccessHistoryService;
        this.pushService = pushService;
        this.fileService = fileService;
        this.userService = userService;
        this.webUserDetailsService = webUserDetailsService;
        this.authKeyService = authKeyService;
        this.organizationService = organizationService;
        this.departmentService = departmentService;
        this.userDepartmentService = userDepartmentService;
        this.privacyRecordService = privacyRecordService;
        this.objectMapper = objectMapper;
        this.policySecurityProperties = policySecurityProperties;
    }

    //https://dandev.tistory.com/entry/%EB%94%94%EB%B2%84%EA%B9%85-The-request-was-rejected-because-the-URL-contained-a-potentially-malicious-String-%EC%97%90%EB%9F%AC-%EB%94%94%EB%B2%84%EA%B9%85%ED%95%98%EB%8A%94-%EB%B2%95
    @Bean
    public HttpFirewall defaultHttpFirewall() {
        return new DefaultHttpFirewall();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();

        configuration.addAllowedOrigin("*");
        configuration.addAllowedHeader("*");
        configuration.addAllowedMethod("*");
        configuration.setAllowCredentials(true);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }

    @Override
    public void configure(WebSecurity web) throws Exception {
//        web.ignoring().antMatchers("/assets/image/**");

        web.httpFirewall(defaultHttpFirewall());
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.headers().frameOptions().sameOrigin().and() // 같은 호스트인 경우 iframe 허용
                .cors().configurationSource(corsConfigurationSource())
                .and()
                .headers(configurer -> {
                    configurer.frameOptions().disable();
                    configurer.addHeaderWriter(new SameSiteHeaderWriter());
                })
                .csrf().disable(); // Cross site request forgery
        http.sessionManagement().maximumSessions(1);

        http.authorizeRequests()
                .antMatchers("/monitor/**").permitAll() // Spring Actuator Web base-path
                .antMatchers("/assets/**", "/img/**", "/map.data", "/map/**").permitAll()
                .antMatchers("/", "/map").permitAll()
                .antMatchers("/rider-main", "/rider-map").permitAll()   // JoinsWebController.java
                .antMatchers("/lbslayout.xml").permitAll() // 지도 레이아웃 설정 파일 허용
                .antMatchers("/bo/**").hasAnyRole("ADMIN", "ORG_ADMIN")    //back office 접근 권한 체크
                // 임시로 권한 추가 [[
                .antMatchers(WebUrl.ANONYMOUS + "/**").permitAll()
                .antMatchers("/api/**").permitAll()
                .antMatchers("/openapi/**").permitAll()
                .antMatchers("/files/**").permitAll()
                .antMatchers("/swagger-ui/**", "/v3/api-docs/**").permitAll()
                .antMatchers(WebUrl.WEB + "/**").permitAll()
                .antMatchers(WebUrl.DEMO + "/**").permitAll()
                // 임시로 권한 추가 ]]
                .anyRequest()
                .authenticated()

                .and()
                .httpBasic()
                .authenticationEntryPoint((request, response, authException) -> response.sendError(HttpStatus.UNAUTHORIZED.value(), HttpStatus.UNAUTHORIZED.getReasonPhrase()))

                .and()
                .exceptionHandling()
                .accessDeniedHandler((request, response, accessDeniedException) -> log.info("Access Denied"))

                .and()
                .formLogin()
                .loginPage("/")
                .loginProcessingUrl(WebUrl.LOGIN)
                .successHandler(authenticationSuccessHandler())
                .failureHandler(authenticationFailureHandler())
                .usernameParameter(WebSecurityConstant.SPRING_SECURITY_FORM_USERNAME_KEY)
                .passwordParameter(WebSecurityConstant.SPRING_SECURITY_FORM_PASSWORD_KEY)

                .and()
                .logout()
                .logoutUrl(WebUrl.LOGOUT)
                .addLogoutHandler(logoutHandler())
                //.invalidateHttpSession(true)
                .deleteCookies("JSESSIONID")

                .and()
                .rememberMe()
                .userDetailsService(webUserDetailsService);

        http.addFilterBefore(webUserAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);
        http.addFilterAfter(webUserRememberMeAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);
    }

    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        final DaoAuthenticationProvider authenticationProvider = new DaoAuthenticationProvider();

        authenticationProvider.setUserDetailsService(webUserDetailsService);
        authenticationProvider.setPasswordEncoder(passwordEncoder);
        authenticationProvider.setPreAuthenticationChecks(preAuthenticationChecker());
        authenticationProvider.setPostAuthenticationChecks(postAuthenticationChecker());

        auth.authenticationProvider(authenticationProvider);
    }

    // AbstractUserDetailsAuthenticationProvider.authenticate()에서 패스워드 매칭 전에 호출함.
    private UserDetailsChecker preAuthenticationChecker() {

        // AuthenticationException의 하위 Exception을 반환해야 authenticationFailureHandler()에 전달된다.
        return (toCheck) -> {

            final WebUserDetails webUserDetails = (WebUserDetails) toCheck;

            final String username = webUserDetails.getUsername();
            final User user = userService.getUserByEmail(username)
                    .orElseThrow(() -> new BadCredentialsException(username + "에 해당하는 사용자 정보가 없습니다."));
            final Organization org = Optional.ofNullable(user.getOrganizationId())
                    .map(organizationService::getOrganizationById)
                    .orElse(null);

            // 조직의 유효 기간 확인
            if (Objects.nonNull(org)) {
                if (!RoleType.ROLE_ADMIN.equals(user.getRoles().get(0).getId())) {
                    // 사용 종료 시간 확인
                    if (org.getUsageEndDate() != null && org.getUsageEndDate().isBefore(LocalDate.now())) {
                        throw new DisabledException("",
                                new CustomException(HttpStatus.UNAUTHORIZED,
                                        WebSecurityConstant.LOGIN_FAIL_REASON_OVER_USAGE_DATE,
                                        "사용 기간이 만료되었습니다. 시스템 관리자에 문의하시기 바랍니다."));
                    }

                    // 사용 개시 시간 확인
                    if (org.getUsageStartDate() != null && org.getUsageStartDate().isAfter(LocalDate.now())) {
                        throw new DisabledException("",
                                new CustomException(HttpStatus.UNAUTHORIZED,
                                        WebSecurityConstant.LOGIN_FAIL_REASON_UNDER_USAGE_DATE,
                                        org.getUsageStartDate().toString() + "부터 사용하실 수 있습니다."));
                    }
                }
            }
        };
    }

    // AbstractUserDetailsAuthenticationProvider.authenticate()에서 패스워드 매칭 후에 호출함.
    private UserDetailsChecker postAuthenticationChecker() {

        // AuthenticationException의 하위 Exception을 반환해야 authenticationFailureHandler()에 전달된다.
        return (toCheck) -> {

            final WebUserDetails webUserDetails = (WebUserDetails) toCheck;

            final String username = webUserDetails.getUsername();
            final User user = userService.getUserByEmail(username)
                    .orElseThrow(() -> new BadCredentialsException(username + "에 해당하는 사용자 정보가 없습니다."));
            final Organization org = Optional.ofNullable(user.getOrganizationId())
                    .map(organizationService::getOrganizationById)
                    .orElse(null);
            final RoleType roleType = userService.getUserRoleByUser(user);
            final boolean orgHasDepartment = departmentService.existDepartmentOfOrganization(org);

            if (orgHasDepartment) {
                final List<Long> authorizedDepartmentIdList = userDepartmentService.getAuthorizedDepartmentIdListOfUser(user, org, false);
                if (CollectionUtils.isEmpty(authorizedDepartmentIdList)) {
                    throw new InsufficientAuthenticationException("",
                            new CustomException(HttpStatus.UNAUTHORIZED,
                                    WebSecurityConstant.LOGIN_FAIL_REASON_NO_AUTHORIZED_DEPARTMENT,
                                    "소속 부서가 존재하지 않습니다. 조직 관리자에 문의하시기 바랍니다."));
                }
            }

            // 2-factor 인증이 필요한 조직인 경우와 슈퍼 관리자의 경우 인증코드 확인
            if ((Objects.nonNull(org) && Boolean.TRUE.equals(org.getIsAuthcodeLoginRequired()))
                    || RoleType.ROLE_ADMIN.equals(roleType)) {

                final String authcode = webUserDetails.getAuthcode();
                final String phoneNumber = user.getPhoneNumber();

                if (StringUtils.isEmpty(phoneNumber)) {
                    throw new InsufficientAuthenticationException("",
                            new CustomException(HttpStatus.UNAUTHORIZED,
                                    WebSecurityConstant.LOGIN_FAIL_REASON_PHONE_NUMBER_REQUIRED,
                                    "2차 인증을 위한 사용자의 전화번호가 없습니다. 시스템 관리자에 문의하시기 바랍니다."));
                }

                if (StringUtils.isEmpty(authcode)) {
                    authKeyService.sendAndSaveAuthKey(user.getUserId(), UserType.WEB, phoneNumber, null);
                    throw new InsufficientAuthenticationException("",
                            new CustomException(HttpStatus.UNAUTHORIZED,
                                    WebSecurityConstant.LOGIN_FAIL_REASON_AUTH_CODE_REQUIRED,
                                    "문자로 전송된 인증번호를 입력하세요."));
                } else {
                    try {
                        authKeyService.validateAuthKey(user.getUserId(), UserType.WEB, authcode, phoneNumber);
                    } catch (Exception e) {
                        throw new InsufficientAuthenticationException("",
                                new CustomException(HttpStatus.UNAUTHORIZED,
                                        WebSecurityConstant.LOGIN_FAIL_REASON_WRONG_AUTH_CODE,
                                        e.getMessage()));
                    }
                }
            }

            // 계정 잠긴 경우
            if (user.isAccountLocked()) {
                // 계정 잠금 시간이 지났으면 잠금해지 처리
                if (user.isLockDurationOver(policySecurityProperties.getUserLockDuration())) {
                    userService.unlockAccount(user);
                } else {
                    final Duration lockDuration = user.getLockDuration(policySecurityProperties.getUserLockDuration());
                    final long lockMinutes = lockDuration.toMinutes();

                    throw new LockedException("",
                            new CustomException(HttpStatus.UNAUTHORIZED,
                                    WebSecurityConstant.LOGIN_FAIL_REASON_LOCKED_ACCOUNT,
                                    "비밀번호 " + policySecurityProperties.getUserLockLoginFailCount() + "회 이상 실패로 인해 " + lockMinutes + "분 동안 계정잠금"));
                }
            }
        };
    }

    private AuthenticationSuccessHandler authenticationSuccessHandler() {

        return (request, response, authentication) -> {

            final WebUserDetails webUserDetails = (WebUserDetails) authentication.getPrincipal();
            final User user = userService.getUserById(webUserDetails.getId());
            final Organization org = organizationService.getOrganizationByUserId(webUserDetails.getId());
            final boolean isFMSService = Boolean.TRUE.equals(Optional.ofNullable(org).map(Organization::getIsFmsService).orElse(null));
            final String orgCodeName = Optional.ofNullable(org).map(Organization::getCodeName).orElse("");

            log.info("LOGIN SUCCESS - {}:{}:{}",
                    webUserDetails.getUsername(),
                    webUserDetails.getName(),
                    webUserDetails.getId());

            CompletableFuture.runAsync(() -> {
                userAccessHistoryService.addUserAccessHistory(request, authentication);
                userService.unlockAccount(user);
            });

            final boolean isOpenDeepLink = Boolean.parseBoolean(request.getParameter("open-deep-link"));

            // 기존에 로그인 된 사용자에게 push 메세지를 던진다
            pushService.sendDuplicateLoginToWeb(webUserDetails.getId());

            final RoleType roleType = userService.getUserRoleByUser(user);

            final HttpSession httpSession = request.getSession();
            httpSession.setAttribute(WebSecurityConstant.SESSION_ATTR_LOGIN_USER_ID, Objects.toString(webUserDetails.getId(), null));
            httpSession.setAttribute(WebSecurityConstant.SESSION_ATTR_LOGIN_USER_NAME, webUserDetails.getName());
            httpSession.setAttribute(WebSecurityConstant.SESSION_ATTR_ORGANIZATION_ID, Objects.toString(webUserDetails.getOrganizationId(), null));
            httpSession.setAttribute(WebSecurityConstant.SESSION_ATTR_ROLE_TYPE, roleType);
            httpSession.setMaxInactiveInterval((int) policySecurityProperties.getHttpSessionMaxInactiveInterval().getSeconds());

            log.info("HttpSession login - sessionId: {}, {}secs, userId: {}, userName: {}, creationTime: {}",
                    httpSession.getId(),
                    httpSession.getMaxInactiveInterval(),
                    httpSession.getAttribute(WebSecurityConstant.SESSION_ATTR_LOGIN_USER_ID),
                    httpSession.getAttribute(WebSecurityConstant.SESSION_ATTR_LOGIN_USER_NAME),
                    getSessionCreationTime(httpSession));

            final Collection<GrantedAuthority> grantedAuthorities = webUserDetails.getAuthorities();
            List<String> roles = grantedAuthorities.stream()
                    .filter(role -> RoleType.ROLE_ADMIN.equals(role.getAuthority()))
                    .map(Object::toString)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(roles)) {
                roles = grantedAuthorities.stream()
                        .filter(role -> RoleType.ROLE_ORG_ADMIN.equals(role.getAuthority()))
                        .map(Object::toString)
                        .collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(roles)) {
                roles = grantedAuthorities.stream()
                        .filter(role -> RoleType.ROLE_COMMON.equals(role.getAuthority()))
                        .map(Object::toString)
                        .collect(Collectors.toList());
            }

            final List<Long> departmentIdList = userDepartmentService.getAuthorizedDepartmentIdListOfUser(user, org, false);
            final String profileImgFileUrl = fileService.getFirstFileUrl(webUserDetails.getId(), FileCategory.WEB_USER_PROFILE, FileType.IMAGE);

            final List<AuthSuccessResponseDTO.Warning> warnings = new ArrayList<>();

            if (user.isPasswordExpired(policySecurityProperties.getUserPasswordExpiredDuration())) {
                // 패스워드를 변경한지 오래되었는지 확인
                warnings.add(AuthSuccessResponseDTO.Warning.builder()
                        .warningCode(WebSecurityConstant.LOGIN_SUCCESS_WITH_EXPIRED_PASSWORD)
                        .warningMsg(policySecurityProperties.getUserPasswordExpiredDuration().toDays() + "일 이상 비밀번호를 변경하지 않았습니다.")
                        .build());
            }

            if (StringUtils.isEmpty(user.getPhoneNumber())) {
                // 사용자의 전화번호가 비어있는지 확인
                warnings.add(AuthSuccessResponseDTO.Warning.builder()
                        .warningCode(WebSecurityConstant.LOGIN_SUCCESS_WITH_NO_PHONE_NUMBER)
                        .warningMsg("사용자 전화번호를 입력해주세요.")
                        .build());
            }

            final AuthSuccessResponseDTO authSuccessResponseDTO = AuthSuccessResponseDTO.builder()
                    .id(webUserDetails.getId())
                    .name(webUserDetails.getName())
                    .email(webUserDetails.getUsername())
                    .organizationId(webUserDetails.getOrganizationId())
                    .openDeepLink(isOpenDeepLink)
                    .isFMSService(isFMSService)
                    .orgCodeName(orgCodeName)
                    .roles(roles)
                    .readonly(Boolean.TRUE.equals(user.getReadonly()))
                    .departmentIdList(departmentIdList)
                    .profileImgFileUrl(profileImgFileUrl)
                    .phoneNumber(user.getPhoneNumber())
                    .warnings(warnings)
                    .build();

            response.setStatus(HttpStatus.OK.value());
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            response.setCharacterEncoding("UTF-8");

            final String jsonBody = objectMapper.writeValueAsString(authSuccessResponseDTO);
            final PrintWriter printWriter = response.getWriter();
            printWriter.write(jsonBody);
//            printWriter.flush();
//            printWriter.close();

            //로그인 기록
            privacyRecordService.saveRecord(
                    PrivacyRecordDto.builder()
                            .recordType(PrivacyRecordType.USER)
                            .type(PrivacyUsageType.ACCESS)
                            .orgId(user.getOrganizationId())
                            .userId(user.getUserId())
                            .func("authenticationSuccessHandler")
                            .ip(HttpUtil.getClientIpAddr(request))
                            .build());
        };
    }

    private AuthenticationFailureHandler authenticationFailureHandler() {

        return (request, response, exception) -> {

            final Authentication authentication = SecurityContextHolder.getContext().getAuthentication(); // always null?
            final String email = request.getParameter(WebSecurityConstant.SPRING_SECURITY_FORM_USERNAME_KEY);
            final String password = request.getParameter(WebSecurityConstant.SPRING_SECURITY_FORM_PASSWORD_KEY);
            final String authcode = request.getParameter(WebSecurityConstant.SPRING_SECURITY_FORM_AUTH_CODE_KEY);
            final User user = userService.getUserByEmail(email).orElse(null);
            final Organization org = Optional.ofNullable(user)
                    .map(User::getOrganizationId)
                    .map(organizationService::getOrganizationById)
                    .orElse(null);

            if (Objects.nonNull(user)) {
                log.info("LOGIN FAILURE - {}:{}:{}:{}",
                        user.getEmail(),
                        user.getName(),
                        user.getUserId(),
                        exception.getMessage());
            } else {
                log.info("LOGIN FAILURE - no user for {}:{}:{}:{}",
                        email,
                        password,
                        authcode,
                        exception.getMessage());
            }

            String errorCode;
            String errorMsg;
            HttpStatus httpStatus;

            final Throwable causeException = exception.getCause();
            if (exception instanceof BadCredentialsException) {
                if (Objects.nonNull(user)) {
                    userService.failLogin(user);

                    final Integer failedCount = user.getLoginFailedCount();
                    if (user.isLoginFailedCountOver(policySecurityProperties.getUserLockLoginFailCount())) {
                        final Duration lockDuration = user.getLockDuration(policySecurityProperties.getUserLockDuration());
                        final long lockMinutes = lockDuration.toMinutes();

                        errorCode = WebSecurityConstant.LOGIN_FAIL_REASON_BAD_PASSWORD;
                        errorMsg = "비밀번호 " + policySecurityProperties.getUserLockLoginFailCount() + "회 이상 실패로 인해 " + lockMinutes + "분 동안 계정잠금";
                    } else {
                        errorCode = WebSecurityConstant.LOGIN_FAIL_REASON_BAD_PASSWORD;
                        errorMsg = failedCount + "회 오류 (" + policySecurityProperties.getUserLockLoginFailCount() + "회 오류시 접속을 제한합니다.)";
                    }
                } else {
                    errorCode = WebSecurityConstant.LOGIN_FAIL_REASON_BAD_USERNAME;
                    errorMsg = "아이디와 비밀번호를 확인하세요.";
                }
                httpStatus = HttpStatus.UNAUTHORIZED;
            } else if (Objects.nonNull(causeException) && causeException instanceof CustomException) {
                final String errorHeader = ((CustomException) causeException).getErrorHeader();
                errorCode = Optional.ofNullable(errorHeader).orElse(WebSecurityConstant.LOGIN_FAIL_REASON_DEFAULT);
                errorMsg = causeException.getMessage();
                httpStatus = ((CustomException) causeException).getHttpStatus();
            } else {
                errorCode = WebSecurityConstant.LOGIN_FAIL_REASON_DEFAULT;
                errorMsg = exception.getMessage();
                httpStatus = HttpStatus.UNAUTHORIZED;
            }

            switch (errorCode) {
                case WebSecurityConstant.LOGIN_FAIL_REASON_BAD_USERNAME:
                    log.warn("잘못된 아이디 실패: {}",
                            email);
                    break;
                case WebSecurityConstant.LOGIN_FAIL_REASON_BAD_PASSWORD:
                    log.warn("잘못된 비밀번호 실패: {}",
                            email);
                    break;
                case WebSecurityConstant.LOGIN_FAIL_REASON_LOCKED_ACCOUNT:
                    log.warn("비밀번호 {}회 이상 실패: {}",
                            policySecurityProperties.getUserLockLoginFailCount(), email);
                    break;
                case WebSecurityConstant.LOGIN_FAIL_REASON_OVER_USAGE_DATE:
                    log.warn("계정 사용 기간 만료: {}, {}",
                            email, Optional.ofNullable(org).map(Organization::getUsageEndDate).orElse(null));
                    break;
                case WebSecurityConstant.LOGIN_FAIL_REASON_UNDER_USAGE_DATE:
                    log.warn("계정 사용 기간 미개시: {}, {}",
                            email, Optional.ofNullable(org).map(Organization::getUsageStartDate).orElse(null));
                    break;
                case WebSecurityConstant.LOGIN_FAIL_REASON_AUTH_CODE_REQUIRED:
                    log.warn("2차 인증을 위한 인증번호 입력 필요: {}",
                            email);
                    break;
                case WebSecurityConstant.LOGIN_FAIL_REASON_PHONE_NUMBER_REQUIRED:
                    log.warn("2차 인증을 위한 사용자 전화번호 없음: {}",
                            email);
                    break;
                case WebSecurityConstant.LOGIN_FAIL_REASON_WRONG_AUTH_CODE:
                    log.warn("잘못된 인증번호 입력: {}, {}",
                            email, authcode);
                    break;
                case WebSecurityConstant.LOGIN_FAIL_REASON_NO_AUTHORIZED_DEPARTMENT:
                    log.warn("부서 지정 누락: {}",
                            email);
                    break;
                default:
                    break;
            }

            final AuthFailureResponseDTO authFailureResponseDTO = AuthFailureResponseDTO.builder()
                    .errorCode(errorCode)
                    .errorMsg(errorMsg)
                    .build();

            response.setStatus(httpStatus.value());
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            response.setCharacterEncoding("UTF-8");

            final String jsonBody = objectMapper.writeValueAsString(authFailureResponseDTO);
            final PrintWriter printWriter = response.getWriter();
            printWriter.write(jsonBody);
//            printWriter.flush();
//            printWriter.close();

            CompletableFuture.runAsync(() -> {
                userAccessHistoryService.addUserAccessHistory(request, authentication);
            });
        };
    }

    private LogoutHandler logoutHandler() {

        return (request, response, authentication) -> {

            //final Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (Objects.nonNull(authentication)) {
                final Object principal = authentication.getPrincipal();

                if (principal instanceof WebUserDetails) {
                    final WebUserDetails webUserDetails = ((WebUserDetails) principal);
                    log.info("LOGOUT - {}:{}:{}",
                            webUserDetails.getUsername(),
                            webUserDetails.getName(),
                            webUserDetails.getId());
                    try {
                        Optional.ofNullable(webUserDetails.getId()).ifPresent(authUserId -> {
                            Optional.ofNullable(organizationService.getOrganizationByCodeName(webUserDetails.getOrganizationCode()))
                                    .ifPresent(organization -> {
                                        privacyRecordService.saveRecord(
                                                PrivacyRecordDto.builder()
                                                        .recordType(PrivacyRecordType.USER)
                                                        .type(PrivacyUsageType.LOGOUT)
                                                        .orgId(organization.getId())
                                                        .userId(authUserId)
                                                        .ip(HttpUtil.getClientIpAddr(request))
                                                        .func("logoutHandler")
                                                        .build());
                                    });
                        });
                    } catch (Exception e) {
                        log.error("Error occurred while saving privacy records: ", e);
                    }

                } else {
                    log.info("LOGOUT - no principal");
                }
            } else {
                log.info("LOGOUT - no authentication");
            }

            final HttpSession httpSession = request.getSession();
            log.info("HttpSession logout - sessionId: {}, {}secs, userId: {}, userName: {}, creationTime: {}, lastAccessedTime: {}",
                    httpSession.getId(),
                    httpSession.getMaxInactiveInterval(),
                    httpSession.getAttribute(WebSecurityConstant.SESSION_ATTR_LOGIN_USER_ID),
                    httpSession.getAttribute(WebSecurityConstant.SESSION_ATTR_LOGIN_USER_NAME),
                    getSessionCreationTime(httpSession),
                    getSessionLastAccessedTime(httpSession));

            httpSession.setAttribute(WebSecurityConstant.SESSION_ATTR_LOGOUT_BY_USER, Boolean.TRUE);

            CompletableFuture.runAsync(() -> {
                userAccessHistoryService.addUserAccessHistory(request, authentication);
            });
        };
    }

    @Deprecated
    //@Bean
    public AuthenticationEventPublisher authenticationEventPublisher(final ApplicationEventPublisher applicationEventPublisher) {
        return new DefaultAuthenticationEventPublisher(applicationEventPublisher);
    }

    private final String rememberMeKey = UUID.randomUUID().toString();

//    @Bean
    public TokenBasedRememberMeServices webUserRememberMeServices() {
        final TokenBasedRememberMeServices webUserRememberMeServices = new TokenBasedRememberMeServices(rememberMeKey, webUserDetailsService);
        return webUserRememberMeServices;
    }

    private WebUserAuthenticationFilter webUserAuthenticationFilter() throws Exception {

        final WebUserAuthenticationFilter filter = new WebUserAuthenticationFilter();
        filter.setAuthenticationManager(authenticationManagerBean());
        filter.setUsernameParameter(WebSecurityConstant.SPRING_SECURITY_FORM_USERNAME_KEY);
        filter.setPasswordParameter(WebSecurityConstant.SPRING_SECURITY_FORM_PASSWORD_KEY);
        filter.setFilterProcessesUrl(WebUrl.LOGIN);
        filter.setAuthenticationSuccessHandler(authenticationSuccessHandler());
        filter.setAuthenticationFailureHandler(authenticationFailureHandler());
        filter.setRememberMeServices(webUserRememberMeServices());
        return filter;
    }

    private RememberMeAuthenticationFilter webUserRememberMeAuthenticationFilter() {

        final List<AuthenticationProvider> webUserRememberMeAuthenticationProviders = Arrays.asList(new RememberMeAuthenticationProvider(rememberMeKey));
        final AuthenticationManager webUserRememberMeAuthenticationManager = new ProviderManager(webUserRememberMeAuthenticationProviders);

        return new RememberMeAuthenticationFilter(webUserRememberMeAuthenticationManager, webUserRememberMeServices());
    }

    private static LocalDateTime getSessionCreationTime(final HttpSession httpSession) {

        if (Objects.isNull(httpSession)) {
            return null;
        }

        try {
            return DateTimeUtil.convertEpochMillisToLocalDateTime(httpSession.getCreationTime());
        } catch (IllegalStateException ignored) {
            return null;
        }
    }

    private static LocalDateTime getSessionLastAccessedTime(final HttpSession httpSession) {

        if (Objects.isNull(httpSession)) {
            return null;
        }

        try {
            return DateTimeUtil.convertEpochMillisToLocalDateTime(httpSession.getLastAccessedTime());
        } catch (IllegalStateException ignored) {
            return null;
        }
    }

}
