package com.logisteq.tms.user.repository;

import com.logisteq.tms.user.domain.UserAccessLocationOfferHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface UserAccessLocationOfferHistoryRepository extends JpaRepository<UserAccessLocationOfferHistory, Long>,
        JpaSpecificationExecutor<UserAccessLocationOfferHistory> {

    @Modifying
    @Transactional
    @Query(nativeQuery = true,
            value = "INSERT INTO user_access_location_offer_history (user_id, user_email, user_name, rider_id, rider_name, date_time) " +
                    "VALUES (:#{#ualoh.userId}, :#{#ualoh.userEmail}, :#{#ualoh.userName}, :#{#ualoh.riderId}, :#{#ualoh.riderName}, :#{#ualoh.dateTime})")
    void saveNatively(@Param("ualoh") final UserAccessLocationOfferHistory ualoh);

}
