package com.logisteq.tms.user.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.logisteq.tms.user.domain.User;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Builder
@Schema(description = "사용자 유효성 검사 응답 DTO")
public class UserValidationResponseDTO {

    @Schema(description = "사용자 ID", example = "123")
    private Long userId;

    @Schema(description = "사용자 이름", example = "홍길동")
    private String name;

    @Schema(description = "이메일 주소", example = "<EMAIL>")
    private String email;

    @Schema(description = "조직 ID", example = "456")
    private Long organizationId;

    @Schema(description = "읽기 전용 여부", example = "false")
    private boolean readonly;

    @Schema(description = "전화번호", example = "010-1234-5678")
    private String phoneNumber;

    @Schema(description = "사용자 권한 목록", example = "[\"ADMIN\", \"USER\"]")
    private List<String> roles;

    public static UserValidationResponseDTO from(User user, List<String> roles) {
        UserValidationResponseDTOBuilder builder = UserValidationResponseDTO.builder()
                .userId(user.getUserId())
                .name(user.getName())
                .email(user.getEmail())
                .readonly(Boolean.TRUE.equals(user.getReadonly()))
                .phoneNumber(user.getPhoneNumber())
                .roles(roles);

        if (user.getOrganizationId() != null) {
            builder.organizationId(user.getOrganizationId());
        }

        return builder.build();
    }
} 