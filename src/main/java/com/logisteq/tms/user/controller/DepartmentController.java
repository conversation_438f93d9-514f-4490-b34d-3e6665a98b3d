package com.logisteq.tms.user.controller;

import com.logisteq.common.auth.AuthClient;
import com.logisteq.common.auth.AuthClientDetails;
import com.logisteq.tms.common.component.AuthorityManager;
import com.logisteq.tms.rider.domain.Rider;
import com.logisteq.tms.rider.service.RiderService;
import com.logisteq.tms.user.constant.ApiUrl;
import com.logisteq.tms.user.domain.Department;
import com.logisteq.tms.user.domain.Organization;
import com.logisteq.tms.user.dto.DepartmentDTO;
import com.logisteq.tms.user.service.DepartmentService;
import com.logisteq.tms.user.service.RiderDepartmentService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Positive;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Tag(name = "Department")
@Slf4j
@Validated
@RestController
public class DepartmentController {

    private final AuthorityManager authorityManager;
    private final RiderService riderService;
    private final DepartmentService departmentService;
    private final RiderDepartmentService riderDepartmentService;

    @Autowired
    public DepartmentController(final AuthorityManager authorityManager,
                                final RiderService riderService,
                                final DepartmentService departmentService,
                                final RiderDepartmentService riderDepartmentService) {

        this.authorityManager = authorityManager;
        this.riderService = riderService;
        this.departmentService = departmentService;
        this.riderDepartmentService = riderDepartmentService;
    }

    /**
     * 부서아이디 목록으로 부서정보 목록을 조회한다.
     *
     * @param authClientDetails
     * @param departmentIdList
     * @return
     */
    @Hidden
    @GetMapping(value = ApiUrl.DEPARTMENTS_INFO_BY_ID)
    public List<DepartmentDTO> getDepartmentListByDepartmentIdList(
            @AuthClient final AuthClientDetails authClientDetails,
            @PathVariable @NotEmpty final List<Long> departmentIdList) {

        final Rider rider = authorityManager.getRiderFromAuthClientDetails(authClientDetails);
        final List<Organization> organizationList = riderService.getOrganizationListOfRider(rider);

        return organizationList.stream()
                .flatMap(organization -> departmentService.getDepartmentListByDepartmentIdList(organization, departmentIdList).stream())
                .map(DepartmentDTO::of)
                .collect(Collectors.toList());
    }

    /**
     * 부서코드 목록으로 부서정보 목록을 조회한다.
     *
     * @param authClientDetails
     * @param departmentCodeList
     * @return
     */
    @Hidden
    @GetMapping(value = ApiUrl.DEPARTMENTS_INFO_BY_CODE)
    public List<DepartmentDTO> getDepartmentListByDepartmentCodeList(
            @AuthClient final AuthClientDetails authClientDetails,
            @PathVariable @NotEmpty final List<String> departmentCodeList) {

        final Rider rider = authorityManager.getRiderFromAuthClientDetails(authClientDetails);
        final List<Organization> organizationList = riderService.getOrganizationListOfRider(rider);

        return organizationList.stream()
                .flatMap(organization -> departmentService.getDepartmentListByDepartmentCodeList(organization, departmentCodeList).stream())
                .map(DepartmentDTO::of)
                .collect(Collectors.toList());
    }

    /**
     * 기사의 부서 목록을 조회한다.
     *
     * @param authClientDetails
     * @param organizationId
     * @param level             특정 레벨
     * @return
     */
    @Operation(summary = "기사의 부서 목록 조회", description = "기사의 부서 목록을 조회한다. (level: 3-지점, 4-TC)")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "OK"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping(value = ApiUrl.DEPARTMENTS)
    public List<DepartmentDTO> getLevelDepartmentList(
            @AuthClient final AuthClientDetails authClientDetails,
            @RequestParam(required = false) @Positive final Long organizationId,
            @RequestParam(required = false) @Positive final Integer level) {

        final Rider rider = authorityManager.getRiderFromAuthClientDetails(authClientDetails);
        final List<Organization> organizationList = riderService.getOrganizationListOfRider(rider).stream()
                .filter(organization -> Objects.isNull(organizationId) || Objects.equals(organization.getId(), organizationId))
                .collect(Collectors.toList());

        return organizationList.stream()
                .flatMap(organization -> {
                    if (Objects.isNull(level)) {
                        return departmentService.getFamilyDepartmentListOfOrganization(organization).stream();
                    } else {
                        return departmentService.getLevelDepartmentListOfOrganization(organization, level).stream();
                    }
                })
                .map(DepartmentDTO::of)
                .collect(Collectors.toList());
    }

    /**
     * 기사의 부모 부서를 조회한다.
     *
     * @param authClientDetails
     * @param departmentId
     * @return
     */
    @Hidden
    @GetMapping(value = ApiUrl.DEPARTMENTS_PARENT)
    public DepartmentDTO getParentDepartment(
            @AuthClient final AuthClientDetails authClientDetails,
            @RequestParam(required = true) @Positive final Long departmentId) {

        final Department childDepartment = departmentService.getDepartmentByDepartmentId(null, departmentId);

        return DepartmentDTO.of(departmentService.getParentDepartmentOfDepartment(childDepartment));
    }

    /**
     * 기사의 자식 부서 목록을 조회한다.
     *
     * @param authClientDetails
     * @param departmentId
     * @return
     */
    @Hidden
    @GetMapping(value = ApiUrl.DEPARTMENTS_CHILD)
    public List<DepartmentDTO> getChildDepartmentList(
            @AuthClient final AuthClientDetails authClientDetails,
            @RequestParam(required = true) @Positive final Long departmentId) {

        final Department parentDepartment = departmentService.getDepartmentByDepartmentId(null, departmentId);

        return DepartmentDTO.of(departmentService.getChildDepartmentListOfDepartment(parentDepartment));
    }

    /**
     * 기사의 직계가족 부서 목록을 조회한다.
     *
     * @param authClientDetails
     * @param organizationId
     * @return
     */
    @Hidden
    @GetMapping(value = ApiUrl.DEPARTMENTS_FAMILY)
    public List<DepartmentDTO> getFamilyDepartmentList(
            @AuthClient final AuthClientDetails authClientDetails,
            @RequestParam(required = false) @Positive final Long organizationId) {

        final Rider rider = authorityManager.getRiderFromAuthClientDetails(authClientDetails);
        final List<Organization> organizationList = riderService.getOrganizationListOfRider(rider).stream()
                .filter(organization -> Objects.isNull(organizationId) || Objects.equals(organization.getId(), organizationId))
                .collect(Collectors.toList());

        return organizationList.stream()
                .flatMap(organization -> departmentService.getFamilyDepartmentListOfOrganization(organization).stream())
                .map(DepartmentDTO::of)
                .collect(Collectors.toList());
    }

    /**
     * 기사의 가시적인 부서 목록을 조회한다.
     *
     * @param authClientDetails
     * @param organizationId
     * @param level             특정 레벨
     * @return
     */
    @Hidden
    @GetMapping(value = ApiUrl.DEPARTMENTS_VISIBLE)
    public List<DepartmentDTO> getVisibleLevelDepartmentList(
            @AuthClient final AuthClientDetails authClientDetails,
            @RequestParam(required = false) @Positive final Long organizationId,
            @RequestParam(required = false) @Positive final Integer level) {

        final Rider rider = authorityManager.getRiderFromAuthClientDetails(authClientDetails);
        final List<Organization> organizationList = riderService.getOrganizationListOfRider(rider).stream()
                .filter(organization -> Objects.isNull(organizationId) || Objects.equals(organization.getId(), organizationId))
                .collect(Collectors.toList());

        return organizationList.stream()
                .flatMap(organization -> {
                    if (Objects.isNull(level)) {
                        return riderDepartmentService.getVisibleFamilyDepartmentListOfRider(rider, organization).stream();
                    } else {
                        return riderDepartmentService.getVisibleLevelDepartmentListOfRider(rider, organization, level).stream();
                    }
                })
                .map(DepartmentDTO::of)
                .collect(Collectors.toList());
    }

    /**
     * 기사의 가시적인 부모 부서를 조회한다.
     *
     * @param authClientDetails
     * @param departmentId
     * @return
     */
    @Hidden
    @GetMapping(value = ApiUrl.DEPARTMENTS_VISIBLE_PARENT)
    public DepartmentDTO getVisibleParentDepartment(
            @AuthClient final AuthClientDetails authClientDetails,
            @RequestParam(required = true) @Positive final Long departmentId) {

        final Rider rider = authorityManager.getRiderFromAuthClientDetails(authClientDetails);
        final Department childDepartment = departmentService.getDepartmentByDepartmentId(null, departmentId);

        return DepartmentDTO.of(riderDepartmentService.getVisibleParentDepartmentOfRider(rider, childDepartment));
    }

    /**
     * 기사의 가시적인 자식 부서 목록을 조회한다.
     *
     * @param authClientDetails
     * @param departmentId
     * @return
     */
    @Hidden
    @GetMapping(value = ApiUrl.DEPARTMENTS_VISIBLE_CHILD)
    public List<DepartmentDTO> getVisibleChildDepartmentList(
            @AuthClient final AuthClientDetails authClientDetails,
            @RequestParam(required = true) @Positive final Long departmentId) {

        final Rider rider = authorityManager.getRiderFromAuthClientDetails(authClientDetails);
        final Department parentDepartment = departmentService.getDepartmentByDepartmentId(null, departmentId);

        return DepartmentDTO.of(riderDepartmentService.getVisibleChildDepartmentListOfRider(rider, parentDepartment));
    }

    /**
     * 기사의 가시적인 직계가족 부서 목록을 조회한다.
     *
     * @param authClientDetails
     * @param organizationId
     * @return
     */
    @Hidden
    @GetMapping(value = ApiUrl.DEPARTMENTS_VISIBLE_FAMILY)
    public List<DepartmentDTO> getVisibleFamilyDepartmentList(
            @AuthClient final AuthClientDetails authClientDetails,
            @RequestParam(required = false) @Positive final Long organizationId) {

        final Rider rider = authorityManager.getRiderFromAuthClientDetails(authClientDetails);
        final List<Organization> organizationList = riderService.getOrganizationListOfRider(rider).stream()
                .filter(organization -> Objects.isNull(organizationId) || Objects.equals(organization.getId(), organizationId))
                .collect(Collectors.toList());

        return organizationList.stream()
                .flatMap(organization -> riderDepartmentService.getVisibleFamilyDepartmentListOfRider(rider, organization).stream())
                .map(DepartmentDTO::of)
                .collect(Collectors.toList());
    }

    /**
     * 기사의 소속된 부서 목록을 조회한다.
     *
     * @param authClientDetails
     * @param organizationId
     * @return
     */
    @Hidden
    @GetMapping(value = ApiUrl.DEPARTMENTS_ASSIGNED)
    public List<DepartmentDTO> getAuthorizedDepartmentList(
            @AuthClient final AuthClientDetails authClientDetails,
            @RequestParam(required = false) @Positive final Long organizationId) {

        final Rider rider = authorityManager.getRiderFromAuthClientDetails(authClientDetails);
        final List<Organization> organizationList = riderService.getOrganizationListOfRider(rider).stream()
                .filter(organization -> Objects.isNull(organizationId) || Objects.equals(organization.getId(), organizationId))
                .collect(Collectors.toList());

        return organizationList.stream()
                .flatMap(organization -> riderDepartmentService.getAssignedDepartmentListOfRider(rider, organization, false).stream())
                .map(DepartmentDTO::of)
                .collect(Collectors.toList());
    }

}
