package com.logisteq.tms.user.service;

import com.logisteq.tms.user.constant.RoleType;
import com.logisteq.tms.user.domain.Operation;
import com.logisteq.tms.user.domain.Role;
import com.logisteq.tms.user.repository.RoleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Validated
@Service
public class RoleService {

    private final RoleRepository roleRepository;

    @Autowired
    public RoleService(final RoleRepository roleRepository) {
        this.roleRepository = roleRepository;
    }

    /**
     * 역할 저장 with 작업 목록
     */
    @Transactional
    public Role saveWithOperations(final RoleType roleType,
                                   final List<String> operationIds) {

        return roleRepository.save(Role.builder()
                .id(roleType.getId())
                .operations(operationIds.stream()
                        .map(id -> Operation.builder().id(id).build())
                        .collect(Collectors.toList()))
                .build());
    }

    /**
     * 역할 조회 by roleType
     *
     * @return
     */
    public Optional<Role> getRoleByRoleType(final RoleType roleType) {

        return roleRepository.findById(roleType.getId());
    }

}
