package com.logisteq.tms.user.service;

import com.logisteq.common.exception.CustomException;
import com.logisteq.common.exception.InvalidParameterException;
import com.logisteq.tms.common.properties.PolicySecurityProperties;
import com.logisteq.tms.file.domain.File;
import com.logisteq.tms.file.domain.suppl.FileCategory;
import com.logisteq.tms.file.domain.suppl.FileType;
import com.logisteq.tms.file.dto.FileDTO;
import com.logisteq.tms.file.service.FileService;
import com.logisteq.tms.project.repository.ProjectRepository;
import com.logisteq.tms.user.constant.RoleType;
import com.logisteq.tms.user.constant.UserConstant;
import com.logisteq.tms.user.domain.Department;
import com.logisteq.tms.user.domain.Organization;
import com.logisteq.tms.user.domain.Role;
import com.logisteq.tms.user.domain.User;
import com.logisteq.tms.user.domain.spec.UserSpecs;
import com.logisteq.tms.user.dto.OrganizationDTO;
import com.logisteq.tms.user.repository.GroupRepository;
import com.logisteq.tms.user.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Validated
@Service
public class UserService {

    private final PasswordEncoder passwordEncoder;
    private final UserRepository userRepository;
    private final OrganizationService organizationService;
    private final FileService fileService;
    private final RoleService roleService;
    private final DepartmentService departmentService;
    private final UserDepartmentService userDepartmentService;
    private final GroupRepository groupRepository;
    private final ProjectRepository projectRepository;
    private final PolicySecurityProperties policySecurityProperties;

    @Autowired
    public UserService(final PasswordEncoder passwordEncoder,
                       final UserRepository userRepository,
                       final OrganizationService organizationService,
                       final FileService fileService,
                       final RoleService roleService,
                       final DepartmentService departmentService,
                       final UserDepartmentService userDepartmentService,
                       final GroupRepository groupRepository,
                       final ProjectRepository projectRepository,
                       final PolicySecurityProperties policySecurityProperties) {

        this.passwordEncoder = passwordEncoder;
        this.userRepository = userRepository;
        this.organizationService = organizationService;
        this.fileService = fileService;
        this.roleService = roleService;
        this.departmentService = departmentService;
        this.userDepartmentService = userDepartmentService;
        this.groupRepository = groupRepository;
        this.projectRepository = projectRepository;
        this.policySecurityProperties = policySecurityProperties;
    }

    /**
     * 유저 저장 with 역할 목록
     *
     * @param email
     * @param name
     * @param password
     * @param phoneNumber
     * @param addressBase
     * @param addressDetail
     * @param zipCode
     * @param organization
     * @param roles
     * @return
     */
    @Transactional
    public User saveWithRoles(String email,
                              String name,
                              String password,
                              String phoneNumber,
                              String addressBase,
                              String addressDetail,
                              String zipCode,
                              OrganizationDTO organization,
                              List<Role> roles,
                              Boolean readonly) {
        // Address 정보
        // TODO : 위치 정보 설정
//		Address address = addressService.ifExistAddressThenMappingElseAdd(AddressDTO.builder().base(addressBase).detail(addressDetail).zipCode(zipCode).build());
        Long organizationId = null;
        Integer loginFailedCount = 0;
        LocalDateTime accountLockedAt = null;

        if (organization != null) {
            organizationId = organizationService.ifExistOrganizationThenMappingElseAdd(organization).getId();
            if (UserConstant.ANONYMOUS_ORG_CODE_NAME.equals(organization.getCodeName()) && !email.endsWith("@anonymous.com")) {
                loginFailedCount = policySecurityProperties.getUserLockLoginFailCount();
                accountLockedAt = LocalDateTime.now().plus(policySecurityProperties.getUserLockDuration());
            }
        }

        return userRepository.save(User.builder()
                .organizationId(organizationId)
                .email(email)
                .name(name)
                .password(passwordEncoder.encode(password))
                .phoneNumber(phoneNumber)
//				.address(address)
                .roles(roles)
                .readonly(readonly)
                .loginFailedCount(loginFailedCount)//익명 사용자 등록 에러
                .accountLockedAt(accountLockedAt)
                .build());
    }

    @Transactional
    public User saveUser(String email,
                         String name,
                         String password,
                         String phoneNumber,
                         Long organizationId,
                         List<Role> roles,
                         Boolean readonly) {

        return userRepository.save(User.builder()
                .organizationId(organizationId)
                .email(email)
                .name(name)
                .password(passwordEncoder.encode(password))
                .phoneNumber(phoneNumber)
                .roles(roles)
                .readonly(readonly)
                .loginFailedCount(0)//익명 사용자 등록 에러
                .build());
    }

    /**
     * Back office 에서 org admin 변경시 사용함.
     *
     * @param user
     * @return
     */
    @Transactional
    public User editUserOrgAdmin(User user) {
        return userRepository.save(user);
    }

    /**
     * 유저 상세 조회
     *
     * @param userId
     * @return
     */
    public Optional<User> getUser(Long userId) {
        return userRepository
                .findById(userId);
    }

    /**
     * 로그인한 사용자 정보를 통해 Role을 조회하여 그중 최고 상위 Role을 반환
     *
     * @param userId
     * @return
     */
    public RoleType getUserRoleByUserId(final Long userId) {

        return userRepository.findById(userId)
                .map(this::getUserRoleByUser)
                .orElse(RoleType.ROLE_ANONYMOUS);
    }

    /**
     * 로그인한 사용자 정보를 통해 Role을 조회하여 그중 최고 상위 Role을 반환
     *
     * @param user
     * @return
     */
    public RoleType getUserRoleByUser(final User user) {

        final List<Role> roleList = Optional.ofNullable(user)
                .map(User::getRoles)
                .orElseGet(Collections::emptyList);

        final Optional<Role> adminRole = roleList.stream()
                .filter(r -> RoleType.ROLE_ADMIN.equals(r.getId()))
                .findFirst();
        if (adminRole.isPresent()) {
            return RoleType.ROLE_ADMIN;
        }

        final Optional<Role> orgAdminRole = roleList.stream()
                .filter(r -> RoleType.ROLE_ORG_ADMIN.equals(r.getId()))
                .findFirst();
        if (orgAdminRole.isPresent()) {
            return RoleType.ROLE_ORG_ADMIN;
        }

        final Optional<Role> commonRole = roleList.stream()
                .filter(r -> RoleType.ROLE_COMMON.equals(r.getId()))
                .findFirst();
        if (commonRole.isPresent()) {
            return RoleType.ROLE_COMMON;
        }

        return RoleType.ROLE_ANONYMOUS;
    }

    /**
     * 유저 정보 조회 by email
     *
     * @param email
     * @return
     */
    public Optional<User> getUserByEmail(final String email) {
        return userRepository.findByEmailAndDeleteAtIsNull(email);
    }

    public User getUserByEmailOrThrowException(final String email) {
        return userRepository.findByEmailAndDeleteAtIsNull(email)
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, "사용자이메일 " + email + "에 대한 사용자가 존재하지 않습니다.", false));
    }

    public User getUserById(final Long userId) {
        return userRepository.findById(userId)
                .orElse(null);
    }

    public User getUserByIdOrThrowException(final Long userId) {
        return userRepository.findByUserIdAndDeleteAtIsNull(userId)
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, "사용자아이디 " + userId + "에 대한 사용자가 존재하지 않습니다.", false));
    }

    public List<User> getUserByOrganization(Long organizationId) {
        return userRepository.findByOrganizationId(organizationId);
    }

    public List<User> getNotDeletedUserByOrganization(Long organizationId) {
        return userRepository.findByOrganizationIdAndDeleteAtIsNull(organizationId);
    }

    /**
     * 이메일 또는 고객사 코드로 사용자의 아이디를 얻는다
     *
     * @param orgCode
     * @param email
     * @return
     */
    public Long getUserIdByOrgCodeOrEmail(String orgCode, String email) {
        Long userId;
        if (Objects.nonNull(email)) {
            User user = this.getUserByEmail(email)
                    .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, email + " 사용자 정보가 없습니다.", false));
            userId = user.getUserId();
        } else {//email이 null일때 orgCode를 통해 고객사의 관리자를 선택한다
            Organization organization = organizationService.getOrganizationByCodeName(orgCode);
            userId = this.getAdminUserIdOfOrganization(organization);
        }
        return userId;
    }

    public Long getAdminUserIdOfOrganization(final Organization organization) {

        if (Objects.isNull(organization)) {
            return null;
        }

        if (Objects.nonNull(organization.getUserId())) {
            return organization.getUserId();
        }

        final Long orgId = organization.getId();

        //TODO: 생성할 회사의 대표자 user를 구한다.
        List<User> users = this.getUserByOrganizationOrgAdmin(orgId);
        if (CollectionUtils.isNotEmpty(users)) {
            return users.get(0).getUserId();
        }

        users = this.getNotDeletedUserByOrganization(orgId);
        if (CollectionUtils.isNotEmpty(users)) {
            return users.get(0).getUserId();
        }

        return null;
    }

    public String getAdminEmailOfOrganization(final Organization organization) {

        final Long userId = this.getAdminUserIdOfOrganization(organization);

        return this.getUser(userId)
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, userId + " 사용자 정보가 없습니다.", false))
                .getEmail();
    }

    /*
     * Organization에서 Role Common 유저만 나타나게 수정함.
     */
    public List<User> getUserByOrganizationRoleCommon(Long organizationId) {
        List<User> userList = this.getNotDeletedUserByOrganization(organizationId);

        return userList.stream()
                .filter(u -> (u.getRoles().size() > 0) && (RoleType.ROLE_COMMON.equals(u.getRoles().get(0).getId())))
                .collect(Collectors.toList());
    }

    /*
     * Organization에서 Org Admin 유저만 나타나게 수정함.
     */
    public List<User> getUserByOrganizationOrgAdmin(Long organizationId) {
        List<User> userList = this.getNotDeletedUserByOrganization(organizationId);

        return userList.stream()
                .filter(u -> (u.getRoles().size() > 0) && (RoleType.ROLE_ORG_ADMIN.equals(u.getRoles().get(0).getId())))
                .collect(Collectors.toList());
    }

    /*
     * Organization에서 Org Admin / Role Common 유저 나타나게 수정함.
     */
    public List<User> getUserByOrganizationRoleCommonAndOrgAdmin(Long organizationId) {
        List<User> userList = this.getNotDeletedUserByOrganization(organizationId);

        return userList.stream()
                .filter(u -> (u.getRoles().size() > 0) && (RoleType.ROLE_ORG_ADMIN.equals(u.getRoles().get(0).getId()) || RoleType.ROLE_COMMON.equals(u.getRoles().get(0).getId())))
                .collect(Collectors.toList());
    }

    public List<Long> getUserIdByOrganization(Long organizationId) {
        List<User> userList = this.getUserByOrganization(organizationId);

        return userList.stream()
                .map(User::getUserId)
                .collect(Collectors.toList());
    }

    /**
     * 유저 Get Name
     *
     * @return
     */
    public String getUserName(Long userId) {
        User user = getUser(userId)
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, userId + " 사용자 정보가 없습니다.", false));
        return user.getName();
    }

    /**
     * 사용자 목록 조회 (사용자 관리에 사용)
     *
     * @param userId
     * @param filterDepartmentIdList
     * @param keyword
     * @param userRole
     * @param pageable
     * @return
     */
    public Page<User> getUsersForManagement(final Long userId,
                                            final List<Long> filterDepartmentIdList,
                                            final String keyword,
                                            final RoleType userRole,
                                            final Pageable pageable) {

        Specification<User> specs = Specification.where(null);

        if (StringUtils.isWhitespace(keyword) && !keyword.equals("")) {
            return Page.empty(pageable);
        }

        if (StringUtils.isNotBlank(keyword)) {
            String keywordPhoneNumber = keyword.replace("-", "");
            specs = Objects.requireNonNull(specs).and(UserSpecs.nameLike(keyword).or(UserSpecs.phoneNumberLike(keywordPhoneNumber)));
        }

        final User user = this.getUser(userId)
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, userId + " 사용자 정보가 없습니다.", false));
        final Long orgId = user.getOrganizationId();
        final Organization organization = organizationService.getOrganizationById(orgId);
        final boolean orgHasDepartment = departmentService.existDepartmentOfOrganization(organization);
        final List<Department> authorizedDepartmentList = userDepartmentService.getAuthorizedDepartmentListOfUser(user, organization, true);
        final List<Department> filterDepartmentList = departmentService.getDescendantDepartmentListOfDepartmentIdList(filterDepartmentIdList);

        if (CollectionUtils.isNotEmpty(authorizedDepartmentList)) {
            log.info("getUsersForManagement: 부서아이디 {}에 대한 사용자 목록을 조회합니다.",
                    authorizedDepartmentList.stream().map(Department::getDepartmentId).collect(Collectors.toList()));
        }

        specs = specs.and(UserSpecs.hasAuthority(userRole, user, organization, orgHasDepartment, authorizedDepartmentList, filterDepartmentList));

        specs = specs.and(UserSpecs.isDeleted(false));

        return userRepository.findAll(specs, pageable);
    }

    /**
     * 유저 삭제
     *
     * @return
     */
    @Transactional
    public void deleteUser(Long userId) {

        // 기존 코드 주석 처리하고, User의 deleteAt을 설정하도록 변경함.
        User user = getUser(userId)
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, userId + " 사용자 정보가 없습니다.", false));

        //superAdmin 삭제 불가능
        if (RoleType.ROLE_ADMIN.equals(user.getRoles().get(0).getId())) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "SuperAdmin 삭제 할 수 없습니다.", false);
        }

        // Email은 Unique하기 때문에 다시 가입하는 경우 겹치지 않게 Random 문자열을 붙여서 저장함.
        user.setEmail(user.getEmail() + '_' + RandomStringUtils.randomAlphabetic(4));
        user.setDeleteAt(LocalDateTime.now());
        userRepository.save(user);

        /*
        User user = getUser(userId)
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, userId + " 사용자 정보가 없습니다.", false));

        List<User> userList = this.getNotDeletedUserByOrganization(user.getOrganizationId());

        //user 삭제시 프로젝트를 ORG_Admin으로 변경 시키지 위해서 User admin 정보 가져 옴
        Optional<User> orgAdmin = userList.stream()
                .filter(u -> RoleType.ROLE_ORG_ADMIN.equals(u.getRoles().get(0).getId()))
                .findFirst();

        //user 삭제시 프로젝트를 ORG_Admin가 없을 경우 같은 Org에 사용자중 첫번째 사용자에게 전달 시킴.
        Optional<User> orgCommon = userList.stream()
                .filter(u -> RoleType.ROLE_COMMON.equals(u.getRoles().get(0).getId()))
                .filter(u -> !u.getUserId().equals(user.getUserId()))
                .findFirst();

        if (orgAdmin.isPresent()) {
            //ORG_Admin으로 프로젝트 할당 시킴
            final User userAdmin = orgAdmin.get();
            this.changedProjectAndGroupsUserId(user, userAdmin);
        } else if (orgCommon.isPresent()) {
            //Common으로 프로젝트 할당 시킴
            final User userCommon = orgCommon.get();
            this.changedProjectAndGroupsUserId(user, userCommon);
        } else {
            //ORG_Admin / Common 가 없을 경우 기존 사용자의 정보들을 DB에서 삭제함.
            this.deleteDBProjectsAndGroups(user);
        }

        userDepartmentService.removeUserFromDepartmentListOfOrganization(user, null);

        userRepository.delete(user);
        */
    }

    /**
     * 익명사용자가 아닌 사용자의 아이디 목록을 반환한다.
     *
     * @return
     */
    public Set<Long> getNotAnonymousUserIdSet() {
        return userRepository.findUserIdByRoleIdIsNot(RoleType.ROLE_ANONYMOUS.getId());
    }

    /**
     * 사용자의 수를 반환한다.
     *
     * @return
     */
    public long getCountOfUsers() {
        return userRepository.count();
    }

    /**
     * 비밀번호 변경
     *
     * @param userId
     * @param orgPassword
     * @param newPassword
     */
    @Transactional
    public void changePassword(Long userId, String orgPassword, String newPassword) {

        User user = getUser(userId).orElseThrow(() -> new CustomException(HttpStatus.BAD_REQUEST, "user not found", false));

        // 기존 비밀번호 불일치 검사
        if (!passwordEncoder.matches(orgPassword, user.getPassword())) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "현재 비밀번호가 일치하지 않습니다.", false);
        }

        // 신규 비밀번호와 기본 비밀번호 일치 검사
        if (passwordEncoder.matches(newPassword, user.getPassword())) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "현재 비밀번호와 새 비밀번호가 달라야 합니다.", false);
        }

        // 비밀번호 정책에 따른 유효성 검사
        if (!isValidPassword(newPassword)) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "유효하지 않은 새 비밀번호 입니다. 비밀번호는 영문대문자,영문소문자,숫자,특수문자로 총 8자 이상이어야 합니다.", false);
        }

        user.setPassword(passwordEncoder.encode(newPassword));
        user.setPassChangedAt(LocalDateTime.now());

        userRepository.save(user);
    }

    @Transactional
    public void changePhoneNumber(Long userId, String phoneNumber) {

        User user = getUser(userId).orElseThrow(() -> new CustomException(HttpStatus.BAD_REQUEST, "user not found", false));

        user.setPhoneNumber(phoneNumber);

        userRepository.save(user);
    }

    /**
     * 비밀번호 유효성 검사
     *
     * @param password
     * @return
     */
    private boolean isValidPassword(String password) {

        /*
         * ^ represents starting character of the string.
         * (?=.*[0-9]) represents a digit must occur at least once.
         * (?=.*[a-z]) represents a lower case alphabet must occur at least once.
         * (?=.*[A-Z]) represents an upper case alphabet that must occur at least once.
         * (?=.*[!@#$%^&*()_+=]) represents a special character that must occur at least once.
         * (?=\\S+$) white spaces don't allowed in the entire string.
         * .{8, 20} represents at least 8 characters and at most 20 characters.
         * $ represents the end of the string.
         */
        String regex = "^(?=.*[0-9])"
                + "(?=.*[a-z])(?=.*[A-Z])"
                + "(?=.*[!@#$%^&*()_+=])"
                + "(?=\\S+$).{8,20}$";

        Pattern p = Pattern.compile(regex);
        if (password == null) {
            return false;
        }

        Matcher m = p.matcher(password);
        return m.matches();
    }

    /**
     * 사용자 이름 변경
     *
     * @param userId
     * @param newName
     */
    @Transactional
    public void changeName(Long userId, String newName) {

        User user = getUser(userId).orElseThrow(() -> new CustomException(HttpStatus.BAD_REQUEST, "user not found", false));

        if (StringUtils.isBlank(newName)) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "이름을 입력해 주세요.", false);
        }

        if (newName.equals(user.getName())) {
            return;
        }

        user.setName(newName);
        userRepository.save(user);
    }

    /**
     * 사용자 정보 변경
     *
     * @param authUser
     * @param userId
     * @param name
     * @param email
     * @param password
     * @param phoneNumber
     * @param organizationId
     * @param departmentIdList
     * @param roles
     * @param readonly
     */
    @Transactional
    public void changeUserInfo(final User authUser,
                               final Long userId,
                               final String name,
                               final String email,
                               final String password,
                               final String phoneNumber,
                               final Long organizationId,
                               final List<Long> departmentIdList,
                               final Set<String> roles,
                               final Boolean readonly) {

        if (Objects.isNull(organizationId)) {
            throw new InvalidParameterException("organizationId 데이터가 없습니다.");
        }

        final Long authUserOrgId = authUser.getOrganizationId();
        final RoleType authUserRoleType = getUserRoleByUser(authUser);
        final User user = getUser(userId)
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, userId + " 사용자 정보가 없습니다.", false));

        if (!RoleType.ROLE_ADMIN.equals(authUserRoleType) && !organizationId.equals(authUserOrgId)) {
            throw new CustomException(HttpStatus.UNAUTHORIZED, "소속 조직 사용자만 수정할 수 있습니다.", false);
        }

        user.setName(name);

        if (StringUtils.isNotBlank(email) && !email.equals(user.getEmail())) {
            getUserByEmail(email).ifPresent(u -> {
                throw new CustomException(HttpStatus.BAD_REQUEST, "이미 존재하는 이메일입니다.");
            });
            user.setEmail(email);
        }

        if (StringUtils.isNotEmpty(password)) {
            // 비밀번호 정책에 따른 유효성 검사
            if (!isValidPassword(password)) {
                throw new CustomException(HttpStatus.BAD_REQUEST, "유효하지 않은 새 비밀번호 입니다. 비밀번호는 영문대문자,영문소문자,숫자,특수문자로 총 8자 이상이어야 합니다.", false);
            }

            user.setPassword(passwordEncoder.encode(password));
            user.setPassChangedAt(LocalDateTime.now());
        }
        if (StringUtils.isNotEmpty(phoneNumber)) {
            user.setPhoneNumber(phoneNumber);
        }

        if (Objects.nonNull(readonly)) {
            user.setReadonly(readonly);
        }

        if (CollectionUtils.containsAny(roles, RoleType.ROLE_ORG_ADMIN.getId())) {
            changeRoleUser(user, RoleType.ROLE_ORG_ADMIN);
        } else if (CollectionUtils.containsAny(roles, RoleType.ROLE_COMMON.getId())) {
            changeRoleUser(user, RoleType.ROLE_COMMON);
        } else {
            throw new InvalidParameterException("등록하려는 사용자의 권한이 잘못 지정되었습니다.", false);
        }

        final Organization organization = organizationService.getOrganizationById(organizationId);
        final boolean orgHasDepartment = departmentService.existDepartmentOfOrganization(organization);
        if (orgHasDepartment) {
            if (CollectionUtils.isEmpty(departmentIdList)) {
                if (CollectionUtils.containsAny(roles, RoleType.ROLE_ORG_ADMIN.getId())) {
                    final List<Department> departmentList = departmentService.getRootDepartmentListOfOrganization(organization);
                    userDepartmentService.assignUserToDepartmentListOfOrganization(user, organization, departmentList, true);
                } else {
                    throw new InvalidParameterException("사용자의 부서가 지정되어 있지 않습니다.", false);
                }
            } else {
                final List<Department> departmentList = departmentService.getDepartmentListByDepartmentIdList(organization, departmentIdList);

                if (RoleType.ROLE_ADMIN.equals(authUserRoleType)) {
                    userDepartmentService.assignUserToDepartmentListOfOrganization(user, organization, departmentList, true);
                } else {
                    final List<Department> authorizedDepartmentList = userDepartmentService.getAuthorizedDepartmentListOfUser(authUser, organization, true);
                    if (CollectionUtils.isEmpty(authorizedDepartmentList)) {
                        throw new InvalidParameterException("관제자의 부서가 지정되어 있지 않습니다.", false);
                    }

                    final List<Department> assignableDepartmentList = DepartmentService.intersection(authorizedDepartmentList, departmentList);
                    if (CollectionUtils.isEmpty(assignableDepartmentList)) {
                        final List<String> departmentNameList = departmentList.stream()
                                .map(Department::getDepartmentName)
                                .collect(Collectors.toList());
                        throw new InvalidParameterException("사용자 " + email + "의 부서를 " +
                                StringUtils.join(departmentNameList, ",") + "(으)로 지정할 권한이 없습니다.", false);
                    }

                    userDepartmentService.assignUserToDepartmentListOfOrganization(user, organization, assignableDepartmentList, true);
                }
            }
        }

        userRepository.save(user);
    }

    /**
     * 비밀번호 변경
     *
     * @param userId
     * @param password
     */
    @Transactional
    public void changeNewPassword(Long userId, String password) {

        User user = getUser(userId)
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, userId + " 사용자 정보가 없습니다.", false));

        // 비밀번호 정책에 따른 유효성 검사
        if (!isValidPassword(password)) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "유효하지 않은 새 비밀번호 입니다. 비밀번호는 영문대문자,영문소문자,숫자,특수문자로 총 8자 이상이어야 합니다.", false);
        }

        user.setPassword(passwordEncoder.encode(password));
        user.setPassChangedAt(LocalDateTime.now());
        userRepository.save(user);
    }

    @Transactional
    public User changeRoleUser(User user, RoleType roleType) {

        List<Role> roles = user.getRoles();
        roles.clear();
        roles.add(roleService.getRoleByRoleType(roleType).get());
        user.setRoles(roles);

        return user;
    }

    /**
     * 사용자 프로파일 이미지 업로드
     *
     * @param userId
     * @param file
     * @return
     */
    @Transactional
    public FileDTO uploadProfileImage(Long userId, MultipartFile file) {

        // 사용자 정보 조회
        User user = getUser(userId).orElseThrow(() -> new CustomException(HttpStatus.BAD_REQUEST, "user not found", false));

        // 프로파일 사진 저장
        FileDTO fileDTO = null;
        try {
            List<File> fileList = fileService.getFileList(user.getUserId(), FileCategory.WEB_USER_PROFILE, FileType.IMAGE);
            if (fileList.size() == 0) {
                fileDTO = fileService.saveFile(file, FileCategory.WEB_USER_PROFILE, user);
            } else {
                String imgUrl = fileService.updateFile(file, fileList.get(0), user);
                fileDTO = FileDTO.builder()
                        .fileId(user.getUserId())
                        .fileCategory(FileCategory.WEB_USER_PROFILE)
                        .fileUrl(imgUrl)
                        .build();
            }
            return fileDTO;
        } catch (Exception e) {
            log.error("사용자 프로파일 사진 업로드 오류", e);
            throw new CustomException(HttpStatus.BAD_REQUEST, e);
        }
    }

    /**
     * 사용자 프로파일 이미지 제거
     *
     * @param userId
     */
    @Transactional
    public void deleteProfileImage(Long userId) {
        List<File> fileList = fileService.getFileList(userId, FileCategory.WEB_USER_PROFILE, FileType.IMAGE);
        if (CollectionUtils.isEmpty(fileList)) {
            return;
        }

        File profileImageFile = fileList.get(0);
        fileService.deleteFile(profileImageFile, null);
    }

    /**
     * 로그인 실패 처리
     *
     * @param user
     */
    @Transactional
    public boolean failLogin(final User user) {
        AtomicBoolean isLock = new AtomicBoolean(false);
        isLock.set(user.increaseLoginFailedCount(policySecurityProperties.getUserLockLoginFailCount()));
        userRepository.save(user);
        return isLock.get();
    }

    /**
     * 계정 잠금 해지
     *
     * @param userId
     */
    @Transactional
    public void unlockAccount(Long userId) {
        getUser(userId).ifPresent(user -> {
            user.unlockAccount();
            userRepository.save(user);
        });
    }

    /**
     * 계정 잠금 해지
     *
     * @param user
     */
    @Transactional
    public void unlockAccount(final User user) {
        user.unlockAccount();
        userRepository.save(user);
    }

    /**
     * 로그인 실패 횟수 조회
     *
     * @param email
     * @return
     */
    public Integer getLoginFailedCount(String email) {
        final User user = getUserByEmail(email).orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, email + " 사용자는 존재하지 않습니다.", false));
        Integer failedCount = user.getLoginFailedCount();
        log.info("비밀번호 오류 : {}", failedCount);
        return failedCount;
    }

    /**
     * 계정 잠금 일시 조회
     *
     * @param email
     * @return
     */
    public LocalDateTime getAccountLockedAt(final String email) {
        final User user = getUserByEmail(email).orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, email + " 사용자는 존재하지 않습니다.", false));
        LocalDateTime accountLockedAt = user.getAccountLockedAt();
        return accountLockedAt;
    }

    @Transactional
    public User validate(final String email, final String password) {
        final User user = getUserByEmail(email)
                .orElseThrow(() -> new CustomException(HttpStatus.UNAUTHORIZED, "이메일 또는 비밀번호가 일치하지 않습니다.", false));

        if (!passwordEncoder.matches(password, user.getPassword())) {
            failLogin(user);
            throw new CustomException(HttpStatus.UNAUTHORIZED, "이메일 또는 비밀번호가 일치하지 않습니다.", false);
        }

        if (user.isAccountLocked()) {
            if (user.isLockDurationOver(policySecurityProperties.getUserLockDuration())) {
                unlockAccount(user);
            } else {
                throw new CustomException(HttpStatus.LOCKED, "계정이 잠겨있습니다. " +
                        user.getLockDuration(policySecurityProperties.getUserLockDuration()).toMinutes() + "분 후에 다시 시도하세요.", false);
            }
        }

        if (user.getLoginFailedCount() > 0) {
            unlockAccount(user);
        }

        return user;
    }
}
