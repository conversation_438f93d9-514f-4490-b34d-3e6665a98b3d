package com.logisteq.tms.stat.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.PositiveOrZero;

@Getter
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@ToString
@Schema(description = "전체 배송 통계 응답 DTO")
public class StatGeneralTotalDeliveryResponseDTO {

    @Schema(description = "할당된 기사 수", example = "25")
    @NonNull
    @NotNull
    @Positive
    private Long numOfRiders;

    @Schema(description = "할당된 프로젝트 수", example = "15")
    @NonNull
    @NotNull
    @Positive
    private Long numOfProjects;

    @Schema(description = "근무 거리(미터)", example = "125000.5")
    @NonNull
    @NotNull
    @PositiveOrZero
    private Double workingMeters;

    @Schema(description = "근무 시간(초)", example = "28800")
    @NonNull
    @NotNull
    @PositiveOrZero
    private Long workingSeconds;

    @Schema(description = "할당된 모든 배송 건수", example = "150")
    @NonNull
    @NotNull
    @PositiveOrZero
    private Long numOfAllDeliveries;

    @Schema(description = "배차 대기 중인 배송 건수", example = "10")
    @NonNull
    @NotNull
    @PositiveOrZero
    private Long numOfWaitingDeliveries;

    @Schema(description = "배송 전인 배송 건수", example = "20")
    @NonNull
    @NotNull
    @PositiveOrZero
    private Long numOfReadyDeliveries;

    @Schema(description = "배송 중인 배송 건수", example = "30")
    @NonNull
    @NotNull
    @PositiveOrZero
    private Long numOfGoingDeliveries;

    @Schema(description = "서비스 중인 배송 건수", example = "15")
    @NonNull
    @NotNull
    @PositiveOrZero
    private Long numOfServicingDeliveries;

    @Schema(description = "거절된 배송 건수", example = "5")
    @NonNull
    @NotNull
    @PositiveOrZero
    private Long numOfRejectedDeliveries;

    @Schema(description = "실패한 배송 건수", example = "3")
    @NonNull
    @NotNull
    @PositiveOrZero
    private Long numOfFailedDeliveries;

    @Schema(description = "완료된 배송 건수", example = "67")
    @NonNull
    @NotNull
    @PositiveOrZero
    private Long numOfCompletedDeliveries;

}
