package com.logisteq.tms.project.domain.event;

import com.logisteq.tms.project.domain.Project;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ProjectEventPublisher {

    private final ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    public ProjectEventPublisher(final ApplicationEventPublisher applicationEventPublisher) {

        this.applicationEventPublisher = applicationEventPublisher;
    }

    /**
     * 프로젝트 관제중 이벤트를 발생시킨다.
     * ProjectEntityListener에서 프로젝트 상태가 관제중(IN_PROGRESS)으로 변경될 때 호출된다.
     *
     * @param project
     */
    public void publishProjectInProgress(final Project project) {

        final ProjectInProgressEvent projectInProgressEvent = ProjectInProgressEvent.of(project);

        log.info("projectId: {}, name: {} 에 대한 프로젝트 관제중 이벤트를 Publish합니다.", projectInProgressEvent.getProjectId(), projectInProgressEvent.getName());
        applicationEventPublisher.publishEvent(projectInProgressEvent);
    }

}
