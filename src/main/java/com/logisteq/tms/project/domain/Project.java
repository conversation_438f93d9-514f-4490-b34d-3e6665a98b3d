package com.logisteq.tms.project.domain;

import com.logisteq.common.constant.Constant;
import com.logisteq.tms.dispatch.ClusterRule;
import com.logisteq.tms.dispatch.ClusterRuleOnDemand;
import com.logisteq.tms.dispatch.ProjectLoadingMode;
import com.logisteq.tms.project.domain.event.ProjectEntityListener;
import com.logisteq.tms.project.domain.suppl.ProjectAttribute;
import com.logisteq.tms.project.domain.suppl.ProjectCreateFrom;
import com.logisteq.tms.project.domain.suppl.ProjectStatus;
import com.logisteq.tms.user.domain.Department;
import com.logisteq.tms.user.domain.Organization;
import com.logisteq.tms.user.domain.User;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Entity
@EntityListeners({AuditingEntityListener.class, ProjectEntityListener.class})
public class Project {

    /*
     * 프로젝트 아이디
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /*
     * 유저 아이디
     */
    @ManyToOne
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    /*
     * 조직 아이디
     */
    @ManyToOne
    @JoinColumn(name = "organization_id", nullable = true)
    private Organization organization;

    /*
     * 부서 아이디
     */
    @ManyToOne
    @JoinColumn(name = "department_id", nullable = true)
    private Department department;

    /*
     * 프로젝트 이름
     */
    @Column
    private String name;

    /*
     * 프로젝트 상태
     */
    @Enumerated(EnumType.STRING)
    @Column(columnDefinition = ProjectStatus.COLUMN_DEFINITION)
    private ProjectStatus status;

    /*
     * 콜백 URL
     */
    @Column
    private String callbackUri;

    /*
     * 프로젝트 실행일시 (기사에게 전송)
     */
    @Column
    private LocalDateTime effectiveDateTime;

    /*
     * 프로젝트 마감 일시
     */
    @Column
    private LocalDateTime cutoffTime;

    /*
     * 프로젝트 종료 일시
     */
    @Column
    private LocalDateTime doneDateTime;

    /*
     * 프로젝트 완료 일시 (모든 배송이 완료되는 시점에 설정됨)
     */
    @Column
    private LocalDateTime completeDateTime;

    /*
     * 프로젝트 삭제 여부
     */
    @Builder.Default
    @Column
    private Boolean deleted = false;

    /*
     * 익명사용자에 의한 생성 여부
     */
    @Builder.Default
    @Column
    private Boolean isAnonymous = false;

    /*
     * 프로젝트 생성 후 기사/방문지 등 편집 가능 여부
     */
    @Builder.Default
    @Column
    private Boolean isReadOnly = false;

    /*
     * 현재 클러스터링이 완료되었는지 여부
     */
    @Column
    private Boolean isClusterDone;

    /*
     * 경로 생성(false) / 경로 재탐색(true) 구분을 위한 값 - 현재는 경로탐색이 완료되었나 여부로 쓰임
     */
    @Column
    private Boolean isFirstRoutingDone;

    /*
     * 경로 탐색 버튼 가능 유무
     */
    @Column
    private Boolean isRouteEnabled;

    /*
     * 기사에게 전송 버튼 가능 유무
     */
    @Column
    private Boolean isSendingRiderEnabled;

    /*
     * 상품 검수가 필요한 프로젝트 유무
     */
    @Builder.Default
    @Column(columnDefinition = "boolean default false")
    private Boolean isProductCheckEnabled = false;

    /*
     * 실시간 프로젝트 유무
     */
    @Builder.Default
    @Column(columnDefinition = "boolean default false")
    private Boolean isOnDemandEnabled = false;

    /*
     * 프로젝트가 생성된 방법
     */
    @Convert(converter = ProjectCreateFrom.ProjectCreateFromConverter.class)
    @Column
    private ProjectCreateFrom createFrom;

    /*
     * 정적 배차 옵션 저장
     */
    @Convert(converter = ClusterRule.ClusterRuleConverter.class)
    @Column
    private ClusterRule clusterRule;

    /*
     * ???
     */
    @Convert(converter = ProjectLoadingMode.ProjectLoadingModeConverter.class)
    @Column
    private ProjectLoadingMode loadingMode;

    /**
     * 경로탐색 옵션 (Constant.java 참고)
     */
    @Builder.Default
    @Column
    private Integer routeOption = Constant.ROUTE_OPTION_DEFAULT;

    /*
     * 정적 배차 옵션 저장
     */
    @Convert(converter = ClusterRuleOnDemand.ClusterRuleOnDemandConverter.class)
    @Column
    private ClusterRuleOnDemand clusterRuleOnDemand;


    /*
     * 배송지 추가시에 마지막에 추가할지 여부 저장
     */
    @Builder.Default
    @Column(columnDefinition = "boolean default false")
    private Boolean isAddDeliveryToLastOrder = false;

    /*
     * 자동생성되는 프로젝트
     */
    @Builder.Default
    @Column(columnDefinition = "boolean default false")
    private Boolean isAutoCreated = false;

    /*
     * 경로탐색 실행 여부 flag
     */
    @Builder.Default
    @Column(columnDefinition = "boolean default false")
    private Boolean isRouteExecute = false;

    /**
     * 작업지시서 활성화 여부
     */
    @Builder.Default
    @Column(columnDefinition = "boolean default false")
    private Boolean isDriverDispatchEnabled = false;

    /*
     * 생성 일시
     */
    @CreatedDate
    @Column(nullable = false, updatable = true)    // 프로젝트 Entity를 재사용하는 경우가 있어, updatable=true로 한다.
    private LocalDateTime createAt;

    /*
     * 변경 일시
     */
    @LastModifiedDate
    @Column(nullable = false, updatable = true)
    private LocalDateTime updateAt;

    /**
     * Entity 업데이트전 프로젝트 상태
     */
    @Transient
    private ProjectStatus preProjectStatus;

    public ProjectAttribute getProjectAttribute() {

        return ProjectAttribute.builder()
                .isAnonymous(this.getIsAnonymous())
                .isReadOnly(this.getIsReadOnly())
                .isClusterDone(this.getIsClusterDone())
                .isFirstRoutingDone(this.getIsFirstRoutingDone())
                .isRouteEnabled(this.getIsRouteEnabled())
                .isSendingRiderEnabled(this.getIsSendingRiderEnabled())
                .isProductCheckEnabled(this.getIsProductCheckEnabled() )
                .isOnDemandEnabled(this.getIsOnDemandEnabled())
                .createFrom(this.getCreateFrom())
                .clusterRule(this.getClusterRule())
                .projectLoadingMode(this.getLoadingMode())
                .clusterRuleOnDemand(this.getClusterRuleOnDemand())
                .routeOption(this.getRouteOption())
                .isAddDeliveryToLastOrder(this.getIsAddDeliveryToLastOrder())
                .isAutoCreated(this.getIsAutoCreated())
                .isRouteExecute(this.getIsRouteExecute())
                .isDriverDispatchEnabled(this.getIsDriverDispatchEnabled())
                .build();
    }

    public void setProjectAttribute(final ProjectAttribute projectAttribute) {

        if (Objects.isNull(projectAttribute)) {
            return;
        }

        this.setIsAnonymous(projectAttribute.getIsAnonymous());
        this.setIsReadOnly(projectAttribute.getIsReadOnly());
        this.setIsClusterDone(projectAttribute.getIsClusterDone());
        this.setIsFirstRoutingDone(projectAttribute.getIsFirstRoutingDone());
        this.setIsRouteEnabled(projectAttribute.getIsRouteEnabled());
        this.setIsSendingRiderEnabled(projectAttribute.getIsSendingRiderEnabled());
        this.setIsProductCheckEnabled(projectAttribute.getIsProductCheckEnabled());
        this.setIsOnDemandEnabled(projectAttribute.getIsOnDemandEnabled());
        this.setCreateFrom(projectAttribute.getCreateFrom());
        this.setClusterRule(projectAttribute.getClusterRule());
        this.setLoadingMode(projectAttribute.getProjectLoadingMode());
        this.setRouteOption(projectAttribute.getRouteOption());
        this.setClusterRuleOnDemand(this.getClusterRuleOnDemand());
        this.setIsAddDeliveryToLastOrder(this.getIsAddDeliveryToLastOrder());
        this.setIsAutoCreated(this.getIsAutoCreated());
        this.setIsRouteExecute(this.getIsRouteExecute());
        this.setIsDriverDispatchEnabled(this.getIsDriverDispatchEnabled());

        log.info("setProjectAttribute - projectId: {}, isAnonymous: {}, isReadOnly: {}, isClusterDone: {}, isFirstRoutingDone: {}, isRouteEnabled: {}, isSendingRiderEnabled: {}, isProductCheckEnabled : {}, isOnDemandEnabled: {}, clusterRuleOnDemand: {}, isAddDeliveryToLastOrder: {}, isRouteExecute: {}, isDriverDispatchEnabled: {}",
                getId(), getIsAnonymous(), getIsReadOnly(), getIsClusterDone(), getIsFirstRoutingDone(), getIsRouteEnabled(), getIsSendingRiderEnabled(), getIsProductCheckEnabled(), getIsOnDemandEnabled(), getClusterRuleOnDemand(), getIsAddDeliveryToLastOrder(), getIsRouteExecute(), getIsDriverDispatchEnabled());
    }

    public void updateProjectAttribute(final ProjectAttribute projectAttribute) {

        if (Objects.isNull(projectAttribute)) {
            return;
        }

        Optional.ofNullable(projectAttribute.getIsAnonymous()).ifPresent(this::setIsAnonymous);
        Optional.ofNullable(projectAttribute.getIsReadOnly()).ifPresent(this::setIsReadOnly);
        Optional.ofNullable(projectAttribute.getIsClusterDone()).ifPresent(this::setIsClusterDone);
        Optional.ofNullable(projectAttribute.getIsFirstRoutingDone()).ifPresent(this::setIsFirstRoutingDone);
        Optional.ofNullable(projectAttribute.getIsRouteEnabled()).ifPresent(this::setIsRouteEnabled);
        Optional.ofNullable(projectAttribute.getIsSendingRiderEnabled()).ifPresent(this::setIsSendingRiderEnabled);
        Optional.ofNullable(projectAttribute.getIsProductCheckEnabled()).ifPresent(this::setIsProductCheckEnabled);
        Optional.ofNullable(projectAttribute.getIsOnDemandEnabled()).ifPresent(this::setIsOnDemandEnabled);
        Optional.ofNullable(projectAttribute.getCreateFrom()).ifPresent(this::setCreateFrom);
        Optional.ofNullable(projectAttribute.getClusterRule()).ifPresent(this::setClusterRule);
        Optional.ofNullable(projectAttribute.getProjectLoadingMode()).ifPresent(this::setLoadingMode);
        Optional.ofNullable(projectAttribute.getRouteOption()).ifPresent(this::setRouteOption);
        Optional.ofNullable(projectAttribute.getClusterRuleOnDemand()).ifPresent(this::setClusterRuleOnDemand);
        Optional.ofNullable(projectAttribute.getIsAddDeliveryToLastOrder()).ifPresent(this::setIsAddDeliveryToLastOrder);
        Optional.ofNullable(projectAttribute.getIsAutoCreated()).ifPresent(this::setIsAutoCreated);
        Optional.ofNullable(projectAttribute.getIsRouteExecute()).ifPresent(this::setIsRouteExecute);
        Optional.ofNullable(projectAttribute.getIsDriverDispatchEnabled()).ifPresent(this::setIsDriverDispatchEnabled);

        log.info("[updateProjectAttribute] projectId: {}, isAnonymous: {}, isReadOnly: {}, isClusterDone: {}, isFirstRoutingDone: {}, isRouteEnabled: {}, isSendingRiderEnabled: {}, isProductCheckEnabled: {}, isOnDemandEnabled: {}, clusterRuleOnDemand: {}, isAddDeliveryToLastOrder: {}, isAutoCreated: {}, isRouteExecute: {}, isDriverDispatchEnabled: {}",
                getId(), getIsAnonymous(), getIsReadOnly(), getIsClusterDone(), getIsFirstRoutingDone(), getIsRouteEnabled(), getIsSendingRiderEnabled(), getIsProductCheckEnabled(), getIsOnDemandEnabled(), getClusterRuleOnDemand(), getIsAddDeliveryToLastOrder(), getIsAutoCreated(), getIsRouteExecute(), getIsDriverDispatchEnabled());
    }

}
