package com.logisteq.tms.project.dto.excel.management;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.logisteq.common.component.excel.annotation.PxlSheet;
import com.logisteq.common.component.excel.annotation.PxlWorkbook;
import com.logisteq.common.component.excel.annotation.PxlWorkbookName;
import com.logisteq.common.component.excel.constant.PxlFileFormat;
import com.logisteq.common.component.excel.styler.title.PxlTitleWrapTextStyler;
import lombok.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@PxlWorkbook(exportFileFormat = PxlFileFormat.SXSSF, exportWorkbookOptionalTitleCellStyler = PxlTitleWrapTextStyler.class)
public class ProjectManagementHandWrittenDeliveryWorkbookDTO {

    @NotBlank
    @PxlWorkbookName
    private String projectName;

    //@NotEmpty(message = "'배송' 시트가 비어있습니다.")
    @Valid
    @PxlSheet(name = "배송")
    private List<ProjectManagementHandWrittenDeliverySheetDTO> deliveries;

}
