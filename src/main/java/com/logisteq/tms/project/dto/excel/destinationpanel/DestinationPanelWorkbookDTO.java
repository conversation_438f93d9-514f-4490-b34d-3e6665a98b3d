package com.logisteq.tms.project.dto.excel.destinationpanel;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.logisteq.common.component.excel.annotation.PxlSheet;
import com.logisteq.common.component.excel.annotation.PxlWorkbook;
import com.logisteq.common.component.excel.annotation.PxlWorkbookName;
import com.logisteq.common.component.excel.constant.PxlFileFormat;
import lombok.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 방문지 목록 패널에서 다운받는 엑셀의 워크북 (Export)
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@PxlWorkbook(exportFileFormat = PxlFileFormat.SXSSF)
public final class DestinationPanelWorkbookDTO {

    @NotBlank
    @PxlWorkbookName
    private String projectName;

    @NotEmpty(message = "'배송' 시트가 비어있습니다.")
    @Valid
    @PxlSheet(name = "배송")
    private List<DestinationPanelDeliverySheetDTO> deliveries;

}
