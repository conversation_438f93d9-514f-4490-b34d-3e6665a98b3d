package com.logisteq.tms.project.dto.web;

import com.logisteq.tms.vehicle.domain.Vehicle;
import com.logisteq.tms.vehicle.types.FuelType;
import com.logisteq.tms.vehicle.types.VehicleType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "웹 차량 데이터 전송 객체")
public class WebVehicleDTO {
	@Schema(description = "차량 번호판", example = "12가 3456")
	private String licensePlate;

	@Schema(description = "차량 모델명", example = "포터2")
	private String modelName;

	@Schema(description = "차량 유형")
	private VehicleType vehicleType;
	
	@Schema(description = "연료 유형")
	private FuelType fuelType;

	@Schema(description = "연비 (km/L)", example = "10.5")
	private Float fuelEfficiency;

	@Schema(description = "연료 탱크 용량 (L)", example = "65.0")
	private Float fuelTankCapacity; //Demo

	@Schema(description = "최대 주행 가능 거리 (km)", example = "600")
	private Integer maxDistanceCapacity; //Demo

	@Schema(description = "현재 주행 가능 거리 (km)", example = "300")
	private Integer currentDistanceCapacity; //Demo

	@Schema(description = "적재 용량 (kg)", example = "1000")
	private Integer payload;

	@Schema(description = "차량 고유 ID", example = "201")
	private Long vehicleId;

	public static WebVehicleDTO of(final Vehicle vehicle) {

		return WebVehicleDTO.builder()
				.licensePlate(vehicle.getLicensePlate())
				.modelName(vehicle.getVehicleModel().getModelName())
				.vehicleType(vehicle.getVehicleModel().getVehicleType())
				.fuelType(vehicle.getVehicleModel().getFuelType())
				.fuelEfficiency(vehicle.getVehicleModel().getFuelEfficiency())
				.fuelTankCapacity(vehicle.getVehicleModel().getFuelTankCapacity())//Demo
				.maxDistanceCapacity(vehicle.getMaxDistanceCapacity())//Demo
				.currentDistanceCapacity(vehicle.getCurrentDistanceCapacity())//Demo
				.vehicleId(vehicle.getVehicleId())
				.payload(vehicle.getCurrentWeightCapacity())
				.build();
	}
}