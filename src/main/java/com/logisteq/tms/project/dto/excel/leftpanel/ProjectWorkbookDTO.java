package com.logisteq.tms.project.dto.excel.leftpanel;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.logisteq.common.component.excel.annotation.PxlSheet;
import com.logisteq.common.component.excel.annotation.PxlWorkbook;
import com.logisteq.common.component.excel.annotation.PxlWorkbookName;
import com.logisteq.common.component.excel.constant.PxlFileFormat;
import com.logisteq.tms.product.dto.ProductDTO;
import lombok.*;
import lombok.experimental.SuperBuilder;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 프로젝트 엑셀의 워크북 (Import/Export)
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@PxlWorkbook(exportFileFormat = PxlFileFormat.SXSSF)
public class ProjectWorkbookDTO {

    @NotBlank
    @PxlWorkbookName
    private String projectName; // 프로젝트 이름

    private LocalDateTime cutoffTime;// 프로젝트 이름

    // redmine-#1105 코드에서 처리 @NotEmpty(message = "'기사' 시트가 비어있습니다.")
    @Valid
    @PxlSheet(name = "기사")
    private List<ProjectRiderSheetDTO> riders; // 기사 리스트

    // redmine-#1105 코드에서 처리 @NotEmpty(message = "'방문지' 시트가 비어있습니다.")
    @Valid
    @PxlSheet(name = "방문지")
    private List<ProjectDestinationSheetDTO> destinations; // 방문지 리스트

    // #1747 : 엑셀에서 물품 탭 제거
    // @Valid
    // @PxlSheet(name = "물품", importEnabled = false, exportSampleEnabled = false)
    private List<ProductDTO> products; // 물품에 대한 리스트

}
