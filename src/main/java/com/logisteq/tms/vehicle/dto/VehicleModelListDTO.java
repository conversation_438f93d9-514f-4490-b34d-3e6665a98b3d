package com.logisteq.tms.vehicle.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.logisteq.tms.vehicle.domain.VehicleModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.data.domain.Page;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "차량 모델 목록 응답 DTO")
public class VehicleModelListDTO {
	//private Pageable page;

	@Schema(description = "차량 모델 목록")
	private List<VehicleModelDTO> vehicleModelList;

	@Schema(description = "목록 생성 시간", example = "2023-12-01T10:30:00")
	private LocalDateTime timestampCreated;
	
	@Schema(description = "전체 차량 모델 수", example = "50")
	private Long totalCount;

	public static VehicleModelListDTO parseFromVehicleModelList(Page<VehicleModel> vehicleModelList) {
		VehicleModelListDTO vehicleModelListDTO = VehicleModelListDTO.builder()
																	  .totalCount(vehicleModelList.getTotalElements())
																	  .vehicleModelList(
																			  vehicleModelList.getContent().stream().map(VehicleModelDTO::parseFromVehicleModel).collect(Collectors.toList()))
																	  .timestampCreated(LocalDateTime.now())
																	  .build();
		return vehicleModelListDTO;
	}
}