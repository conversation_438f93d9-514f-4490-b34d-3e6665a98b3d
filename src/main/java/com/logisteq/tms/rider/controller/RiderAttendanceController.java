package com.logisteq.tms.rider.controller;

import com.logisteq.common.exception.CustomException;
import com.logisteq.tms.external.joins.constant.DelificateConstant;
import com.logisteq.tms.notification.service.NotificationService;
import com.logisteq.tms.notification.types.NotiType;
import com.logisteq.tms.project.domain.Project;
import com.logisteq.tms.project.domain.suppl.ProjectStatus;
import com.logisteq.tms.project.repository.ProjectRepository;
import com.logisteq.tms.project.service.ProjectBasicService;
import com.logisteq.tms.push.service.PushService;
import com.logisteq.tms.rider.constant.ApiUrl;
import com.logisteq.tms.rider.domain.RiderAttendance;
import com.logisteq.tms.rider.domain.RiderProjectSetting;
import com.logisteq.tms.rider.dto.RiderAttendanceDTO;
import com.logisteq.tms.rider.repository.RiderProjectSettingRepository;
import com.logisteq.tms.rider.service.RiderAttendanceService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @created 2021-02-02
 * @project tms-service
 */
@Tag(name = "RiderAttendance")
@Slf4j
@Validated
@RestController
@RequestMapping
public class RiderAttendanceController {

    private final ProjectRepository projectRepository;
    private final ProjectBasicService projectBasicService;
    private final RiderProjectSettingRepository riderProjectSettingRepository;
    private final RiderAttendanceService riderAttendanceService;
    private final NotificationService notificationService;
    private final PushService pushService;

    private final boolean riderAttendanceSchedulerEnabled;
    private final long riderAttendanceScheduledFixedRate;

    @Autowired
    public RiderAttendanceController(final ProjectRepository projectRepository,
                                     final ProjectBasicService projectBasicService,
                                     final RiderProjectSettingRepository riderProjectSettingRepository,
                                     final RiderAttendanceService riderAttendanceService,
                                     final NotificationService notificationService,
                                     final PushService pushService,
                                     @Value("${scheduler.rider-attendance.enabled: true}")
                                     final boolean riderAttendanceSchedulerEnabled,
                                     @Value("${scheduler.rider-attendance.fixed-rate}")
                                     final long riderAttendanceScheduledFixedRate) {

        this.projectRepository = projectRepository;
        this.projectBasicService = projectBasicService;
        this.riderProjectSettingRepository = riderProjectSettingRepository;
        this.riderAttendanceService = riderAttendanceService;
        this.notificationService = notificationService;
        this.pushService = pushService;

        this.riderAttendanceSchedulerEnabled = riderAttendanceSchedulerEnabled;
        if (riderAttendanceSchedulerEnabled) {
            log.info("riderAttendanceScheduler is enabled.");
        } else {
            log.warn("riderAttendanceScheduler is not enabled.");
        }
        this.riderAttendanceScheduledFixedRate = riderAttendanceScheduledFixedRate;
    }

    /**
     * 미출근 확인
     * https://www.baeldung.com/spring-scheduled-tasks
     * This task is executed each minutes
     */
    @Hidden
    @Scheduled(fixedRateString = "${scheduler.rider-attendance.fixed-rate}")
    @PutMapping(ApiUrl.RIDER_ATTENDANCE)    // Postman으로 테스트해보기 위해
    public void processRiderAttendance() {

        if (!riderAttendanceSchedulerEnabled) {
            return;
        }

        // 상한 일시 설정
        final LocalDateTime boundDateTime = LocalDateTime.now().withNano(0);
        final LocalDate boundDate = boundDateTime.toLocalDate();
        final LocalTime boundTime = boundDateTime.toLocalTime();

        // 하한 일시 설정
        final LocalDateTime originDateTime = boundDateTime.minusSeconds(riderAttendanceScheduledFixedRate / 1000L);
        final LocalDate originDate = originDateTime.toLocalDate();
        final LocalTime originTime = originDateTime.toLocalTime();

        List<Pair<Long, Long>> noAttendanceList = new ArrayList<>();

        // 상한 날짜와 하한 날짜의 동일 여부에 따라 분리
        if (originDate.equals(boundDate)) {

            final List<Long> todayInProgressProjectIdList =
                    projectRepository.findJoinsProjectIdByStatusAndEffectiveDateIs(ProjectStatus.IN_PROGRESS, DelificateConstant.JOINS_ORG_CODE_NAME, boundDate);
//                    projectRepository.findProjectIdByStatusAndEffectiveDateIs(ProjectStatus.IN_PROGRESS, boundDate);

            if (CollectionUtils.isNotEmpty(todayInProgressProjectIdList)) {

                final List<RiderProjectSettingRepository.ProjectRiderView> projectRiderViewList =
                        riderProjectSettingRepository.findProjectRiderViewByProjectIdInAndWorkingStartTimeIsGreaterThanAndWorkingStartTimeIsLessThanEqual(
                                todayInProgressProjectIdList,
                                originTime,
                                boundTime);

                noAttendanceList = projectRiderViewList.stream()
                        .map(x -> Pair.of(x.getProjectId(), x.getRiderId()))
                        .collect(Collectors.toList());
            }
        } else {

            {
                final List<Long> yesterdayInProgressProjectIdList =
                        projectRepository.findJoinsProjectIdByStatusAndEffectiveDateIs(ProjectStatus.IN_PROGRESS, DelificateConstant.JOINS_ORG_CODE_NAME, originDate);
//                        projectRepository.findProjectIdByStatusAndEffectiveDateIs(ProjectStatus.IN_PROGRESS, originDate);

                if (CollectionUtils.isNotEmpty(yesterdayInProgressProjectIdList)) {

                    final List<RiderProjectSettingRepository.ProjectRiderView> projectRiderViewList =
                            riderProjectSettingRepository.findProjectRiderViewByProjectIdInAndWorkingStartTimeIsGreaterThan(
                                    yesterdayInProgressProjectIdList,
                                    originTime);

                    final List<Pair<Long, Long>> yesterdayNoAttendanceList = projectRiderViewList.stream()
                            .map(x -> Pair.of(x.getProjectId(), x.getRiderId()))
                            .collect(Collectors.toList());
                    noAttendanceList.addAll(yesterdayNoAttendanceList);
                }
            }

            {
                final List<Long> todayInProgressProjectIdList =
                        projectRepository.findJoinsProjectIdByStatusAndEffectiveDateIs(ProjectStatus.IN_PROGRESS, DelificateConstant.JOINS_ORG_CODE_NAME, boundDate);
//                        projectRepository.findProjectIdByStatusAndEffectiveDateIs(ProjectStatus.IN_PROGRESS, boundDate);

                if (CollectionUtils.isNotEmpty(todayInProgressProjectIdList)) {

                    final List<RiderProjectSettingRepository.ProjectRiderView> projectRiderViewList =
                            riderProjectSettingRepository.findProjectRiderViewByProjectIdInAndWorkingStartTimeIsLessThanEqual(
                                    todayInProgressProjectIdList,
                                    boundTime);

                    final List<Pair<Long, Long>> todayNoAttendanceList = projectRiderViewList.stream()
                            .map(x -> Pair.of(x.getProjectId(), x.getRiderId()))
                            .collect(Collectors.toList());
                    noAttendanceList.addAll(todayNoAttendanceList);
                }
            }
        }

        for (final Pair<Long, Long> noAttendance : noAttendanceList) {

            final Long projectId = noAttendance.getLeft();
            final Long riderId = noAttendance.getRight();
            notificationService.notificationOccurAttendance(riderId, projectId, NotiType.NO_ATTENDANCE);

            final Long userId = projectRepository.findUserIdByProjectId(projectId);
            pushService.sendNotificationToWeb(userId, projectId, riderId);
        }
    }

    /**
     * 기사의 출근시각 저장
     *
     * @param riderAttendanceDTO
     */
    @Hidden
    @PostMapping(ApiUrl.RIDER_ATTENDANCE)
    @ResponseStatus(HttpStatus.CREATED)
    public void postRiderAttendance(@RequestBody @Valid @NotNull final RiderAttendanceDTO riderAttendanceDTO) {

        final Long riderId = riderAttendanceDTO.getRiderId();
        final Long projectId = riderAttendanceDTO.getProjectId();
        final LocalDateTime attendAt = riderAttendanceDTO.getAttendAt();

        final Project project = projectBasicService.getProjectByIdOrThrowException(projectId);

        final ProjectStatus projectStatus = project.getStatus();
        if (!ProjectStatus.IN_PROGRESS.equals(projectStatus)) {
            throw new CustomException(HttpStatus.NOT_FOUND, "프로젝트아이디 " + projectId + "에 해당하는 프로젝트는 관제 중이지 않습니다.", false);
        }

        final LocalDateTime effectiveDateTime = project.getEffectiveDateTime();
        if (Objects.isNull(effectiveDateTime)) {
            throw new CustomException(HttpStatus.NOT_FOUND, "프로젝트아이디 " + projectId + "에 해당하는 프로젝트는 아직 실행되지 않았습니다.", false);
        }

        final RiderProjectSetting riderProjectSetting = riderProjectSettingRepository.findByRiderIdAndProjectId(riderId, projectId);
        final LocalDateTime attendRequiredAt = Optional.ofNullable(riderProjectSetting)
                .map(s -> LocalDateTime.of(effectiveDateTime.toLocalDate(), s.getWorkingStartTime()))
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, "프로젝트아이디 " + projectId + ", 기사아이디 " + riderId + "에 설정된 근무시작시각을 찾을 수 없습니다.", false));

        try {
            final RiderAttendance riderAttendance = riderAttendanceService.saveRiderAttendance(riderId, projectId, attendAt, attendRequiredAt);

            if (Objects.nonNull(riderAttendance)) {

                if (Boolean.TRUE.equals(riderAttendance.getIsLate())) {
                    notificationService.notificationOccurAttendance(riderId, projectId, NotiType.LATE_ATTENDANCE);
                } else {
                    notificationService.notificationOccurAttendance(riderId, projectId, NotiType.ATTENDANCE);
                }

                final Long userId = projectRepository.findUserIdByProjectId(projectId);
                pushService.sendNotificationToWeb(userId, projectId, riderId);
            }
        } catch (DataIntegrityViolationException e) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "프로젝트아이디 " + projectId + ", 기사아이디 " + riderId + "에 이미 출근/지각 보고되었습니다.", false);
        }
    }

    /**
     * 기사의 출근시각 조회
     *
     * @param riderId
     * @param projectId
     * @return
     */
    @Hidden
    @GetMapping(ApiUrl.RIDER_ATTENDANCE)
    public RiderAttendanceDTO getRiderAttendance(@RequestParam @NotNull @Positive Long riderId,
                                                 @RequestParam @NotNull @Positive Long projectId) {

        final RiderAttendance riderAttendance = riderAttendanceService.getRiderAttendance(riderId, projectId);

        if (Objects.nonNull(riderAttendance)) {
            return RiderAttendanceDTO.of(riderAttendance);
        } else {
            throw new CustomException(HttpStatus.NOT_FOUND,
                    "프로젝트아이디 " + projectId + ", 기사아이디 " + riderId + "에 해당하는 출근내역이 존재하지 않습니다.", false);
        }
    }

}
