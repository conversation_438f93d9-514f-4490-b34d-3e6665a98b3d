package com.logisteq.tms.rider.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.logisteq.tms.rider.domain.Rider;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.data.domain.Page;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Schema(description = "라이더 목록 응답 DTO")
public class RiderListDTO {

	@Schema(description = "프로젝트 ID", example = "123")
	private Long projectId;

	@Schema(description = "전체 결과 수", example = "150")
	private Long available;

	@Schema(description = "조회 결과 수", example = "50")
	private Long totalCount;
	
	@Schema(description = "라이더 정보 목록 (timestamp 오름차순 정렬)")
	private List<RiderDTO> riders;
	
	@Builder
	protected RiderListDTO(Long projectId,
						   Long available,
						   Long totalCount,
						   List<RiderDTO> riders) {

		this.projectId = projectId;
		this.available = available;
		this.totalCount = totalCount;
		this.riders = riders;
	}
	
	public static RiderListDTO parseFromRiders(Long projectId,
											   Page<Rider> riders) {

		if (riders == null) {
			return null;
		}
		
		RiderListDTO riderListDTO = RiderListDTO.builder()
					.available(riders != null ? riders.getTotalElements() : 0L)
					.totalCount(riders != null ? riders.getContent().size() : 0L)
					.riders(riders == null ? new ArrayList<RiderDTO>() :
						riders.stream()
							.map(rider -> RiderDTO.parseFromRider(rider))
							.collect(Collectors.toList()))
				.build();
		
		if (Objects.nonNull(projectId)) {
			riderListDTO.setProjectId(projectId);
		}
		
		return riderListDTO;
	}
	
	public static RiderListDTO parseFromRiders(Long projectId,
											   List<Rider> riders) {

		if (riders == null) {
			return null;
		}
		
		RiderListDTO riderListDTO =  RiderListDTO.builder()
					.available(riders != null ? riders.size() : 0L)
					.totalCount(riders != null ? riders.size() : 0L)
					.riders(riders == null ? new ArrayList<RiderDTO>() :
						riders.stream()
							.map(rider -> { return RiderDTO.parseFromRider(rider); })
							.collect(Collectors.toList()))
				.build();
		
		if (projectId != null) {
			riderListDTO.setProjectId(projectId);
		}
		
		return riderListDTO;
	}

}
