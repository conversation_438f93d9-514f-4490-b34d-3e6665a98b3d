package com.logisteq.tms.web.controller;

import com.logisteq.common.feign.coreapi.RouteServiceClient;
import com.logisteq.common.feign.coreapi.SearchServiceClient;
import com.logisteq.tms.common.component.ProfileManager;
import com.logisteq.tms.delivery.domain.suppl.*;
import com.logisteq.tms.rider.domain.suppl.WorkAuthority;
import com.logisteq.tms.vehicle.types.*;
import com.logisteq.tms.web.constant.WebUrl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpSession;
import java.util.Map;

@Slf4j
@Validated
@Tag(name = "WebCommon", description = "공통 기능 API - Enum 조회, 버전 정보 등의 공통 기능을 제공합니다.")
@RestController
public class WebCommonController {

    private final ProfileManager profileManager;
    private final RouteServiceClient routeServiceClient;
    private final SearchServiceClient searchServiceClient;
    private final String activeProfile;

    @Autowired
    public WebCommonController(final ProfileManager profileManager,
                               final RouteServiceClient routeServiceClient,
                               final SearchServiceClient searchServiceClient) {

        this.profileManager = profileManager;
        this.routeServiceClient = routeServiceClient;
        this.searchServiceClient = searchServiceClient;
        this.activeProfile = this.profileManager.getActiveProfile();
    }

    @Value("${spring.web.version: null}")
    private String webVersion;

    @Operation(summary = "웹 버전 조회", description = "현재 웹 애플리케이션의 버전 정보를 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공")
    })
    @GetMapping(WebUrl.WEB_VERSION)
    public String getWebVersion() {
        return webVersion + " - " + activeProfile;
    }

    @Operation(summary = "라우트 서비스 버전 조회", description = "라우트 서비스의 버전 정보를 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공")
    })
    @GetMapping(WebUrl.WEB_ROUTE_VERSION)
    public String getRouteVersion() {
        return this.routeServiceClient.getRouteVersion();
    }

    @Operation(summary = "검색 서비스 버전 조회", description = "검색 서비스의 버전 정보를 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공")
    })
    @GetMapping(WebUrl.WEB_SEARCH_VERSION)
    public String getSearchVersion() {
        return this.searchServiceClient.getSearchVersion();
    }

    @Operation(summary = "세션 속성 설정", description = "세션에 속성값을 설정합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "설정 성공")
    })
    @PostMapping(WebUrl.SESSION_ATTR)
    public void setSessionValue(@RequestBody Map<String, Object> sessionAttr,
                                HttpSession session) {

        if (sessionAttr != null) {
            sessionAttr.forEach((key, value) -> {
                session.setAttribute(key, value);
            });
        }
    }

    @Operation(summary = "배송 타입 목록 조회", description = "시스템에서 사용되는 모든 배송 타입 목록을 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공")
    })
    @GetMapping(value = WebUrl.ENUM_DELIVERY_TYPES)
    public DeliveryType[] getDeliveryTypes() {
        return DeliveryType.values();
    }

    @Operation(summary = "배송 상태 목록 조회", description = "시스템에서 사용되는 모든 배송 상태 목록을 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공")
    })
    @GetMapping(value = WebUrl.ENUM_DELIVERY_STATUS)
    public DeliveryStatus[] getDeliveryStatus() {
        return DeliveryStatus.values();
    }

    @Operation(summary = "배송 완료 타입 목록 조회", description = "시스템에서 사용되는 모든 배송 완료 타입 목록을 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공")
    })
    @GetMapping(value = WebUrl.ENUM_DELIVERY_COMPLETED_TYPES)
    public DeliveryCompletedType[] getDeliveryCompletedType() {
        return DeliveryCompletedType.values();
    }

    @Operation(summary = "배송 실패 타입 목록 조회", description = "시스템에서 사용되는 모든 배송 실패 타입 목록을 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공")
    })
    @GetMapping(value = WebUrl.ENUM_DELIVERY_FAILURE_TYPES)
    public DeliveryFailureType[] getDeliveryFailureType() {
        return DeliveryFailureType.values();
    }

    @Operation(summary = "방문 타입 목록 조회", description = "시스템에서 사용되는 모든 방문 타입 목록을 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공")
    })
    @GetMapping(value = WebUrl.ENUM_VISIT_TYPES)
    public VisitType[] getVisitTypes() {
        return VisitType.values();
    }

    @Operation(summary = "화물 서브 타입 목록 조회", description = "시스템에서 사용되는 모든 화물 서브 타입 목록을 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공")
    })
    @GetMapping(value = WebUrl.ENUM_CARGO_SUB_TYPES)
    public CargoSubType[] getCargoSubTypes() {
        return CargoSubType.values();
    }

    @Operation(summary = "화물 타입 목록 조회", description = "시스템에서 사용되는 모든 화물 타입 목록을 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공")
    })
    @GetMapping(value = WebUrl.ENUM_CARGO_TYPES)
    public CargoType[] getCargoTypes() {
        return CargoType.values();
    }

    @Operation(summary = "연료 타입 목록 조회", description = "시스템에서 사용되는 모든 연료 타입 목록을 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공")
    })
    @GetMapping(value = WebUrl.ENUM_FUEL_TYPES)
    public FuelType[] getFuelTypes() {
        return FuelType.values();
    }

    @Operation(summary = "마일리지 타입 목록 조회", description = "시스템에서 사용되는 모든 마일리지 타입 목록을 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공")
    })
    @GetMapping(value = WebUrl.ENUM_MILES_TYPES)
    public MilesType[] getMilesTypes() {
        return MilesType.values();
    }

    @Operation(summary = "차량 크기 타입 목록 조회", description = "시스템에서 사용되는 모든 차량 크기 타입 목록을 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공")
    })
    @GetMapping(value = WebUrl.ENUM_VEHICLE_SIZE_TYPES)
    public VehicleSizeType[] getVehicleSizeTypes() {
        return VehicleSizeType.values();
    }

    @Operation(summary = "차량 타입 목록 조회", description = "시스템에서 사용되는 모든 차량 타입 목록을 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공")
    })
    @GetMapping(value = WebUrl.ENUM_VEHICLE_TYPES)
    public VehicleType[] getVehicleTypes() {
        return VehicleType.values();
    }

    @Operation(summary = "구동계 타입 목록 조회", description = "시스템에서 사용되는 모든 구동계 타입 목록을 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공")
    })
    @GetMapping(value = WebUrl.ENUM_WHEEL_DRIVE_TYPES)
    public WheelDrvType[] getWheelDriveTypes() {
        return WheelDrvType.values();
    }

    @Operation(summary = "상품 크기 타입 목록 조회", description = "시스템에서 사용되는 모든 상품 크기 타입 목록을 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공")
    })
    @GetMapping(value = WebUrl.ENUM_PRODUCT_SIZE_TYPES)
    public ProductSizeType[] getProductSizeTypes() {
        return ProductSizeType.values();
    }

    @Operation(summary = "작업 권한 목록 조회", description = "시스템에서 사용되는 모든 작업 권한 목록을 조회합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "조회 성공")
    })
    @GetMapping(value = WebUrl.ENUM_WORK_AUTHORITIES)
    public WorkAuthority[] getWorkAuthorities() {
        return WorkAuthority.values();
    }

}
