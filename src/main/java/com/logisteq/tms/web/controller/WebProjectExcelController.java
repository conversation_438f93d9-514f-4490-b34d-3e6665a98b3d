package com.logisteq.tms.web.controller;

import com.logisteq.common.exception.CustomException;
import com.logisteq.tms.common.component.AuthorityManager;
import com.logisteq.tms.common.security.WebUserDetails;
import com.logisteq.tms.external.joins.controller.DelificateController;
import com.logisteq.tms.project.controller.ProjectExcelController;
import com.logisteq.tms.project.domain.suppl.ProjectStatus;
import com.logisteq.tms.project.dto.excel.destinationpanel.DestinationPanelWorkbookFilterRequestDTO;
import com.logisteq.tms.web.constant.WebConstant;
import com.logisteq.tms.web.constant.WebUrl;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Tag(name = "WebProjectExcel", description = "프로젝트 엑셀 파일 생성 API")
@Slf4j
@Validated
@RestController
public class WebProjectExcelController {

    private final ProjectExcelController projectExcelController;
    private final DelificateController delificateController;
    private final AuthorityManager authorityManager;

    @Autowired
    public WebProjectExcelController(final ProjectExcelController projectExcelController,
                                     final DelificateController delificateController,
                                     final AuthorityManager authorityManager) {

        this.projectExcelController = projectExcelController;
        this.delificateController = delificateController;
        this.authorityManager = authorityManager;
    }

    /**
     * 웹 관제 화면의 배송지 상세 목록에서 엑셀 파일 생성 (Top Panel)
     *
     * @param userInfo
     * @param projectId
     * @param response
     * @throws Exception
     */
    @Operation(summary = "배송지 상세 목록 엑셀 생성", description = "웹 관제 화면의 배송지 상세 목록에서 엑셀 파일을 생성합니다 (Top Panel).")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "엑셀 파일 생성 성공"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @PostMapping(value = WebUrl.PROJECT_EXCEL_FROM_LIST_PANEL, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void exportProjectWorkbook(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                      @Parameter(description = "프로젝트 ID", example = "1") @PathVariable final Long projectId,
                                      @Parameter(description = "HttpServletResponse 객체") final HttpServletResponse response) throws Exception {

        if (!authorityManager.hasAuthorityByProjectId(userInfo, projectId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 프로젝트에  접근할 권한이 없습니다.", false);
        }

        projectExcelController.exportProjectWorkbook(userInfo, projectId, response);
    }

    /**
     * 웹 관제 화면의 가배차 프로젝트 기사들 목록만 엑셀 파일 생성 (Top Panel)
     *
     * @param userInfo
     * @param projectId
     * @param response
     * @throws Exception
     */
    @Operation(summary = "가배차 프로젝트 기사 목록 엑셀 생성", description = "웹 관제 화면의 가배차 프로젝트 기사들 목록만 엑셀 파일을 생성합니다 (Top Panel).")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "엑셀 파일 생성 성공"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @PostMapping(value = WebUrl.PROJECT_EXCEL_EXPORT_SIMULATION_RIDERS, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void exportProjectSimulationRidersWorkbook(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                      @Parameter(description = "프로젝트 ID", example = "1") @PathVariable final Long projectId,
                                                      @Parameter(description = "HttpServletResponse 객체") final HttpServletResponse response) throws Exception {

        if (!authorityManager.hasAuthorityByProjectId(userInfo, projectId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 프로젝트에  접근할 권한이 없습니다.", false);
        }

        projectExcelController.exportProjectSimulationRidersWorkbook(userInfo, projectId, response);
    }

    /**
     * 프로젝트 관리 화면에서 프로젝트 별 선택하여 엑셀파일로 생성 (왼쪽 아래 다운로드 아이콘)
     * 하나 선택하면 하나의 엑셀파일 다운로드하고, 여러개 선택하면 여러개의 엑셀파일을 Zip파일로 묶어 다운로드한다.
     *
     * @param userInfo
     * @param projectIdList
     * @param exportSingleMerged
     * @param response
     * @throws Exception
     */
    @Operation(summary = "프로젝트별 엑셀 생성", description = "프로젝트 관리 화면에서 선택한 프로젝트들의 엑셀 파일을 생성합니다. 단일 선택 시 엑셀 파일, 다중 선택 시 ZIP 파일로 제공됩니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "엑셀 파일 생성 성공"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @GetMapping(value = WebUrl.PROJECT_EXCEL, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void exportProjectManagementWorkbookByProjectIdList(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                               @Parameter(description = "프로젝트 ID 목록") @RequestParam(required = false) final List<Long> projectIdList,
                                                               @Parameter(description = "단일 파일로 병합 여부", example = "false") @RequestParam(required = false) final Boolean exportSingleMerged,
                                                               @Parameter(description = "HttpServletResponse 객체") final HttpServletResponse response) throws Exception {

        if (!authorityManager.hasAuthorityByProjectIds(userInfo, projectIdList)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 프로젝트에  접근할 권한이 없습니다.", false);
        }

        projectExcelController.exportProjectManagementWorkbookByProjectIdList(userInfo, projectIdList, exportSingleMerged, response);
    }

    /**
     * 프로젝트 관리 화면에서 기간 선택하여 해당 기간의 모든 배송결과를 하나의 엑셀파일로 생성
     *
     * @param userInfo
     * @param projectStatusList
     * @param fromDateTime
     * @param toDateTime
     * @param exportSingleMerged
     * @param response
     * @throws Exception
     */
    @Operation(summary = "기간별 배송결과 엑셀 생성", description = "프로젝트 관리 화면에서 선택한 기간의 모든 배송결과를 하나의 엑셀 파일로 생성합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "엑셀 파일 생성 성공")
    })
    @GetMapping(value = WebUrl.PROJECT_EXCEL_BY_PERIOD, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void exportProjectManagementWorkbookByPeriod(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                        @Parameter(description = "프로젝트 상태 목록") @RequestParam(required = false) final List<ProjectStatus> projectStatusList,
                                                        @Parameter(description = "시작 날짜시간") @RequestParam(required = false) @DateTimeFormat(pattern = WebConstant.REQ_DATE_FORMAT) final LocalDateTime fromDateTime,
                                                        @Parameter(description = "종료 날짜시간") @RequestParam(required = false) @DateTimeFormat(pattern = WebConstant.REQ_DATE_FORMAT) final LocalDateTime toDateTime,
                                                        @Parameter(description = "단일 파일로 병합 여부", example = "false") @RequestParam(required = false) final Boolean exportSingleMerged,
                                                        @Parameter(description = "이메일 주소") @RequestParam(required = false) @Email final String email,
                                                        @Parameter(description = "HttpServletResponse 객체") final HttpServletResponse response) throws Exception {

        projectExcelController.exportProjectManagementWorkbookByPeriod(userInfo, projectStatusList, fromDateTime, toDateTime, exportSingleMerged, email, response);
    }

    /**
     * 프로젝트에서 Hand Written Delivery 엑셀파일 생성
     *
     * @param userInfo
     * @param projectId
     * @param response
     * @throws Exception
     */
    @Operation(summary = "수기 배송 엑셀 생성", description = "프로젝트에서 Hand Written Delivery 엑셀 파일을 생성합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "엑셀 파일 생성 성공"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @GetMapping(value = WebUrl.PROJECT_HAND_WRITTEN_EXCEL, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void exportProjectManagementHandWrittenWorkbook(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                           @Parameter(description = "프로젝트 ID", example = "1") @RequestParam(required = false) final Long projectId,
                                                           @Parameter(description = "HttpServletResponse 객체") final HttpServletResponse response) throws Exception {

        if (!authorityManager.hasAuthorityByProjectId(userInfo, projectId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 프로젝트에  접근할 권한이 없습니다.", false);
        }

        projectExcelController.exportProjectManagementHandWrittenWorkbook(userInfo, projectId, response);
    }

    /**
     * 웹 관제 화면의 배송지 상세 목록에서 엑셀 파일 생성 (Left Panel)
     *
     * @param userInfo
     * @param projectId
     * @param destinationPanelWorkbookFilterRequestDTO
     * @param response
     * @throws Exception
     */
    @Operation(summary = "배송지 패널 엑셀 생성", description = "웹 관제 화면의 배송지 상세 목록에서 엑셀 파일을 생성합니다 (Left Panel).")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "엑셀 파일 생성 성공"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @PostMapping(value = WebUrl.PROJECT_EXCEL_FROM_DESTINATION_LIST_PANEL, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void exportDestinationPanelWorkbook(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                               @Parameter(description = "프로젝트 ID", example = "1") @PathVariable final Long projectId,
                                               @Parameter(description = "배송지 패널 필터 정보") @RequestBody DestinationPanelWorkbookFilterRequestDTO destinationPanelWorkbookFilterRequestDTO,
                                               @Parameter(description = "HttpServletResponse 객체") final HttpServletResponse response) throws Exception {

        if (!authorityManager.hasAuthorityByProjectId(userInfo, projectId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 프로젝트에  접근할 권한이 없습니다.", false);
        }

        projectExcelController.exportDestinationPanelWorkbook(userInfo, projectId, destinationPanelWorkbookFilterRequestDTO, response);
    }

    /**
     * 프로젝트의 각 기사가 배송할 상품 목록 엑셀 파일 생성 (현대백화점 작업지시서 - 호차 기준 물류코드 목록)
     *
     * @param userInfo
     * @param projectId
     * @param riderIdList
     * @param response
     * @throws Exception
     */
    @Operation(summary = "기사별 상품 목록 엑셀 생성", description = "프로젝트의 각 기사가 배송할 상품 목록 엑셀 파일을 생성합니다 (현대백화점 작업지시서 - 호차 기준 물류코드 목록).")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "엑셀 파일 생성 성공"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @GetMapping(value = WebUrl.PROJECT_EXCEL_DELIVERY_PRODUCT_PER_RIDER, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void exportDeliveryProductPerRiderOfProjectWorkbook(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                               @Parameter(description = "프로젝트 ID", example = "1") @RequestParam(required = true) final Long projectId,
                                                               @Parameter(description = "기사 ID 목록") @RequestParam(required = false) final List<Long> riderIdList,
                                                               @Parameter(description = "HttpServletResponse 객체") final HttpServletResponse response) throws Exception {

        if (!authorityManager.hasAuthorityByProjectId(userInfo, projectId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 프로젝트에  접근할 권한이 없습니다.", false);
        }

        projectExcelController.exportDeliveryProductPerRiderOfProjectWorkbook(userInfo, projectId, riderIdList, response);
    }

    /**
     * 프로젝트의 각 물류코드를 배송할 기사 목록 엑셀 파일 생성 (현대백화점 작업지시서 - 물류코드 기준 호차 목록)
     *
     * @param userInfo
     * @param projectId
     * @param response
     * @throws Exception
     */
    @Operation(summary = "상품별 기사 목록 엑셀 생성", description = "프로젝트의 각 물류코드를 배송할 기사 목록 엑셀 파일을 생성합니다 (현대백화점 작업지시서 - 물류코드 기준 호차 목록).")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "엑셀 파일 생성 성공"),
        @ApiResponse(responseCode = "403", description = "접근 권한 없음")
    })
    @GetMapping(value = WebUrl.PROJECT_EXCEL_DELIVERY_RIDER_PER_PRODUCT, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void exportDeliveryRiderPerProductOfProjectWorkbook(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                                               @Parameter(description = "프로젝트 ID", example = "1") @RequestParam(required = true) final Long projectId,
                                                               @Parameter(description = "HttpServletResponse 객체") final HttpServletResponse response) throws Exception {

        if (!authorityManager.hasAuthorityByProjectId(userInfo, projectId)) {
            throw new CustomException(HttpStatus.FORBIDDEN, "잘못된 인증 정보 입니다. 프로젝트에  접근할 권한이 없습니다.", false);
        }

        projectExcelController.exportDeliveryRiderPerProductOfProjectWorkbook(userInfo, projectId, response);
    }

    /**
     * 중앙일보 프로젝트에서 송달증 엑셀파일 생성
     *
     * @param userInfo
     * @param projectId
     * @param response
     * @throws Exception
     */
    @Operation(summary = "중앙일보 송달증 엑셀 생성", description = "중앙일보 프로젝트에서 송달증 엑셀 파일을 생성합니다.")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "엑셀 파일 생성 성공")
    })
    @GetMapping(value = WebUrl.PROJECT_EXCEL_JOINS_INVOICE, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void exportJoinsInvoiceWorkbook(@Parameter(hidden = true) @AuthenticationPrincipal @NotNull final WebUserDetails userInfo,
                                           @Parameter(description = "프로젝트 ID", example = "1") @PathVariable final Long projectId,
                                           @Parameter(description = "HttpServletResponse 객체") final HttpServletResponse response) throws Exception {

        delificateController.exportJoinsCirculationsWorkbook(null, projectId, response);
    }

}
