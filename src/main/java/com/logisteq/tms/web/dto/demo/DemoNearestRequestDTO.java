package com.logisteq.tms.web.dto.demo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.logisteq.common.dto.RoutesCoordinate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 근접 요청 데이터
 */
@Getter
@Setter
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "데모 근접 요청 데이터 DTO")
public class DemoNearestRequestDTO {

    /**
     * 경로 좌표 배열
     */
    @Schema(description = "경로 좌표 배열", required = true)
    @NotNull
    private List<RoutesCoordinate> routes;

    /**
     * 점 좌표
     */
    @Schema(description = "점 좌표", required = true)
    @NotNull
    private RoutesCoordinate point;

}
