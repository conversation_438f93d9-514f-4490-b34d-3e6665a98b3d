package com.logisteq.tms.web.service;

import com.logisteq.common.exception.CustomException;
import com.logisteq.common.util.FormatUtil;
import com.logisteq.tms.address.service.AddressService;
import com.logisteq.tms.common.security.WebUserDetails;
import com.logisteq.tms.delivery.dto.DeliveryRidersChangeDTO;
import com.logisteq.tms.delivery.service.DeliveryService;
import com.logisteq.tms.external.kep.constant.KepConstant;
import com.logisteq.tms.project.dto.excel.leftpanel.ProjectRiderSheetDTO;
import com.logisteq.tms.project.dto.web.WebRiderDTO;
import com.logisteq.tms.project.service.ProjectService;
import com.logisteq.tms.push.service.PushService;
import com.logisteq.tms.rider.domain.Rider;
import com.logisteq.tms.rider.domain.RiderOrgStatus;
import com.logisteq.tms.rider.domain.RiderRecentSetting;
import com.logisteq.tms.rider.domain.suppl.WorkAuthority;
import com.logisteq.tms.rider.dto.SimulationRiderDTO;
import com.logisteq.tms.rider.repository.RiderOrgStatusRepository;
import com.logisteq.tms.rider.service.RiderProjectSettingService;
import com.logisteq.tms.rider.service.RiderRecentSettingService;
import com.logisteq.tms.rider.service.RiderService;
import com.logisteq.tms.user.domain.Department;
import com.logisteq.tms.user.domain.Group;
import com.logisteq.tms.user.domain.Organization;
import com.logisteq.tms.user.domain.User;
import com.logisteq.tms.user.service.DepartmentService;
import com.logisteq.tms.user.service.GroupService;
import com.logisteq.tms.user.service.OrganizationService;
import com.logisteq.tms.user.service.UserService;
import com.logisteq.tms.vehicle.types.MilesType;
import com.logisteq.tms.vehicle.types.VehicleType;
import com.logisteq.tms.web.dto.WebRiderVehicleDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.locationtech.jts.geom.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Validated
@Service
public class WebRiderService {

    private final ProjectService projectService;
    private final RiderService riderService;
    private final UserService userService;
    private final OrganizationService organizationService;
    private final DepartmentService departmentService;
    private final PushService pushService;
    private final DeliveryService deliveryService;
    private final RiderProjectSettingService riderProjectSettingService;
    private final RiderOrgStatusRepository riderOrgStatusRepository;
    private final AddressService addressService;
    private final RiderRecentSettingService riderRecentSettingService;
    private final GroupService groupService;

    @Autowired
    public WebRiderService(final ProjectService projectService,
                           final RiderService riderService,
                           final UserService userService,
                           final OrganizationService organizationService,
                           final DepartmentService departmentService,
                           final PushService pushService,
                           final DeliveryService deliveryService,
                           final RiderProjectSettingService riderProjectSettingService,
                           final RiderOrgStatusRepository riderOrgStatusRepository,
                           final AddressService addressService,
                           final RiderRecentSettingService riderRecentSettingService,
                           final GroupService groupService) {

        this.projectService = projectService;
        this.riderService = riderService;
        this.userService = userService;
        this.organizationService = organizationService;
        this.departmentService = departmentService;
        this.pushService = pushService;
        this.deliveryService = deliveryService;
        this.riderProjectSettingService = riderProjectSettingService;
        this.riderOrgStatusRepository = riderOrgStatusRepository;
        this.addressService = addressService;
        this.riderRecentSettingService = riderRecentSettingService;
        this.groupService = groupService;
    }

    @Transactional
    public WebRiderDTO saveRidersAndVehicle(final Long authUserId,
                                            final Long authUserOrgId,
                                            final WebRiderVehicleDTO webRiderVehicleDTO,
                                            final Boolean isRiderAdd) {

        final User user = userService.getUser(authUserId)
                .orElseThrow(() -> new CustomException(HttpStatus.NOT_FOUND, authUserId + " 사용자 정보가 없습니다.", false));
        final Organization organization = organizationService.getOrganizationById(user.getOrganizationId());

        final Long riderId = webRiderVehicleDTO.getRiderId();
        WebRiderDTO webRiderDTO;

        // 기사관리 페이지에서 project에 포함 안되어 있는 기사 추가 및 변경시 riderService에서 진행한다.
        if (Boolean.TRUE.equals(isRiderAdd)) {
            if (Objects.nonNull(riderId)) {
                final Rider foundRider = riderService.getRider(riderId);
                final Rider orgFoundRider = riderService.getRidersOrgId(authUserOrgId, FormatUtil.stripPhoneNumber(webRiderVehicleDTO.getMobile(), false), false)
                        .orElse(null);

                if (Objects.nonNull(orgFoundRider)) {
                    final boolean orgHasDepartment = departmentService.existDepartmentOfOrganization(authUserOrgId);
                    if (!orgHasDepartment) {
                        throw new CustomException(HttpStatus.BAD_REQUEST, foundRider.getName() + "기사가 이미 저장되어 있습니다.", false);
                    }
                } else {
                    //기존 rider에 대한 org 정보를 저장함.
                    riderOrgStatusRepository.save(RiderOrgStatus.builder()
                            .rider(foundRider)
                            .orgId(authUserOrgId)
                            .isDeleted(false)
                            .build());
                }

                //기존 rider에 대한 정보를 update 시킴
                webRiderDTO = riderService.updateRider(authUserId, webRiderVehicleDTO, riderId);
            } else {
                webRiderDTO = riderService.addRider(authUserId, webRiderVehicleDTO);
                //기사 추가시에 자동으로 금일 생성 프로젝트에 추가되도록 함
                if (organizationService.isCodeNameOrganization(organization, KepConstant.KEP_ORG_CODE_NAME) && !WorkAuthority.SCANNING.equals(webRiderVehicleDTO.getWorkAuthority())) {
                    projectService.addRiderToTodayDepartmentProject(authUserOrgId, webRiderDTO, webRiderVehicleDTO.getDepartmentIdList().stream().findFirst().orElse(null));
                }
            }
        } else {
            final Long projectId = webRiderVehicleDTO.getProjectId();

            if (Objects.isNull(projectId)) {
                webRiderDTO = riderService.updateRider(authUserId, webRiderVehicleDTO, riderId);
            } else {
                final List<String> departmentNameList = departmentService.getDepartmentListByDepartmentIdList(organization, webRiderVehicleDTO.getDepartmentIdList()).stream()
                        .map(Department::getDepartmentName)
                        .collect(Collectors.toList());

                // 차량 번호판에 의해 차량을 조회하고 차량이 없을경우 차량을 등록하고, 있다면 저 아래서 rider와 차량을 매핑하자.....
                final ProjectRiderSheetDTO riderDetailsDTO = ProjectRiderSheetDTO.of(webRiderVehicleDTO, departmentNameList);

                if (Objects.nonNull(riderId)) {
                    webRiderDTO = projectService.updateRiderVehicle(authUserId, riderDetailsDTO, projectId, riderId, false /*addProject*/, webRiderVehicleDTO.getDispatchNumber());
                } else {
                    webRiderDTO = projectService.saveRiderVehicle(authUserId, riderDetailsDTO, projectId, true);
                }

                // 다른 사용자들에 대한 status 변경을 위해서 사용함.
                pushService.sendReloadProjectMessageToWeb(authUserId, projectId, "프로젝트 상태가 변경되었습니다.");

                // 라이더가 추가되면 경로 생성 버튼은 활성화, 기사에게 전송은 비활성화 되어야 한다.
                projectService.changeProjectAttributeToNeedToUpdateRider(projectId);
            }
        }

        return webRiderDTO;
    }

    @Transactional
    public void deleteRidersFromProject(final List<Long> riderIds, final Long projectId) {

        for (Long riderId : riderIds) {
            riderService.deleteRiderFromProject(riderId, projectId);
        }

        // 라이더가 삭제되면 경로 생성 버튼은 활성화, 기사에게 전송은 비활성화 되어야 한다.
        projectService.changeProjectAttributeToNeedToUpdateRider(projectId);
    }

    /**
     * 가배차 기사 저장
     *
     * @return
     */
    @Transactional
    public void simulationRidersAddProject(@NotNull final WebUserDetails userInfo,
                                           @Valid @NotNull final SimulationRiderDTO simulationRiderDTO) {

        final Long authUserId = userInfo.getId();
        final Long authUserOrgId = userInfo.getOrganizationId();
        final Long projectId = simulationRiderDTO.getProjectId();
        Point pointPos = null;

        if(Objects.nonNull(simulationRiderDTO.getAddress())) {
            pointPos = addressService.convertAddressToPoint(simulationRiderDTO.getAddress());
        }

        //기사 등록을 위한 for 처리함.
        for (Long startIndex = simulationRiderDTO.getCarStartNumber(); startIndex < simulationRiderDTO.getCarStartNumber() + simulationRiderDTO.getRiderCount(); startIndex++) {

            // 기사 등록을 위한 DTO 작업함.
            //authUserId+배차 번호를 합쳐서 모바일 번호를 만듬.
            String mobile = userInfo.getId().toString() + startIndex.toString();
            //기사가 존재하는지 확인하는 작업이 필요함.
            final Rider rider = riderService.getRidersOrgId(userInfo.getOrganizationId(),mobile, false).orElse(null);
            if (Objects.nonNull(rider)) {
                //받은 값 중에서 시작 주소를 넣는 방법을 찾이서 추가 해야함.
                if(Objects.isNull(simulationRiderDTO.getAddress())) {
                    riderProjectSettingService.addRiderToProject(rider.getId(), projectId);
                } else {
                    riderProjectSettingService.addRiderChangedStartAddressToProject(rider.getId(), projectId, simulationRiderDTO.getAddress(), pointPos);
                }
            } else {
                final WebRiderVehicleDTO webRiderVehicleDTO = new WebRiderVehicleDTO();
                webRiderVehicleDTO.setName(startIndex.toString());
                webRiderVehicleDTO.setLicensePlate(userInfo.getName());
                webRiderVehicleDTO.setMobile(mobile);
                webRiderVehicleDTO.setMilesType(MilesType.LASTMILE);
                webRiderVehicleDTO.setWorkingStartAddress(simulationRiderDTO.getAddress());
                webRiderVehicleDTO.setWorkingStartTime(LocalTime.of(07, 00));
                webRiderVehicleDTO.setWorkingEndTime(LocalTime.of(18, 30));
                webRiderVehicleDTO.setVehicleType(VehicleType.TRUCK_CARGO);
                webRiderVehicleDTO.setIsSimulationRider(true);
                webRiderVehicleDTO.setDispatchNumber(startIndex);

                //기존 웹에서 사용하는 방법과 같은 방식으로 기사 저장하게 수정함.
                final WebRiderDTO webRiderDTO = saveRidersAndVehicle(authUserId, authUserOrgId, webRiderVehicleDTO, true);

                // 기사 입력 후 프로젟트에 추가하는 기능 구현함.
                riderProjectSettingService.addRiderToProject(webRiderDTO.getRiderId(), projectId);
            }
        }
    }

    /**
     * 프로젝트의 가배차 기사를 실배차 기사로 변경
     *
     * @param userInfo
     * @param dto
     */
    @Transactional
    public void simulationRiderChangedDispatchRiderProject(@NotNull final WebUserDetails userInfo,
                                                           DeliveryRidersChangeDTO dto) {

        final Long projectId = dto.getProjectId();

        Rider simulationRider = riderService.getRider(dto.getRiderId1());
        //라이더 프로젝트에 할당함.
        Rider rider = riderService.getRider(dto.getRiderId2());
        Long dispatchNumber = Long.valueOf(simulationRider.getName());

        if (Objects.isNull(rider.getIsSimulationRider()) || Objects.equals(rider.getIsSimulationRider(), false)) {
            riderProjectSettingService.addRiderToProject(dto.getRiderId2(), projectId);
            rider.setDispatchNumber(dispatchNumber);
            riderService.updateRiderInfo(rider);
        } else {
            throw new CustomException(HttpStatus.BAD_REQUEST, "가배차 기사에게 할당 할 수 없습니다.", false);
        }

        //가배차 라이더 배송으로 실배차 라이더에게 전송함.
        deliveryService.switchSimulationRiderToDispatchRider(projectId, dto.getRiderId1(), dto.getRiderId2());

        //가배차 라이더 프로젝트에서 삭제함.
        riderService.deleteRiderFromProject(dto.getRiderId1(), dto.getProjectId());

        if (Objects.nonNull(dispatchNumber)) {
            // 실배차 기사의 변경된 호차번호로 담당권역 업데이트
            final String newGroupName = String.valueOf(dispatchNumber.longValue());
            riderRecentSettingService.updateRiderRecentSettingWithGroupName(rider, newGroupName);

            final List<Group> groupList = groupService.getGroupListByProjectIdAndRiderId(projectId, dto.getRiderId2());
            if (CollectionUtils.isNotEmpty(groupList)) {
                groupList.forEach(g -> g.setGroupName(newGroupName));
                groupService.saveGroupList(groupList);
            }
        }
    }

}