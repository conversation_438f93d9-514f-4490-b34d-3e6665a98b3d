package com.logisteq.tms.external.thehyundai.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum TheHyundaiStore {

    APGUJEONG("본점", "<EMAIL>"),
    TRADECENTER("무역센터점", "<EMAIL>"),
    CHEONHO("천호점", "<EMAIL>"),
    SINCHON("신촌점", "<EMAIL>"),
    MIA("미아점", "<EMAIL>"),
    MOKDONG("목동점", "<EMAIL>"),
    JUNGDONG("중동점", "<EMAIL>"),
    KINTEX("킨텍스점", "<EMAIL>"),
    PANGYO("판교점", "<EMAIL>"),
    DCUBE("디큐브시티", "<EMAIL>"),
    SEOUL("더현대서울", "<EMAIL>"),
    BUSAN("커넥트현대", "<EMAIL>"),
    DAEGU("더현대대구", "<EMAIL>"),
    ULSAN("울산점", "<EMAIL>"),
    DONGGU("동구점", "<EMAIL>"),
    CHUNGCHEONG("충청점", "<EMAIL>");

    private String storeCd;
    private String email;

    public static List<String> storeCdList = Arrays.stream(TheHyundaiStore.values())
            .map(TheHyundaiStore::toString)
            .collect(Collectors.toList());

    public static boolean isTheHyundaiStore(final String storeCd) {

        return storeCdList.contains(storeCd);
    }

    public static TheHyundaiStore getStoreByStoreCd(final String storeCd) {

        final TheHyundaiStore store = Arrays.stream(TheHyundaiStore.values())
                .filter(s -> storeCd.equals(s.getStoreCd()))
                .findAny()
                .orElse(null);

        return store;
    }

    public static TheHyundaiStore getStoreByEmail(final String email) {

        final TheHyundaiStore store = Arrays.stream(TheHyundaiStore.values())
                .filter(s -> email.equals(s.getEmail()))
                .findAny()
                .orElse(null);

        return store;
    }

    public String toString() {

        return this.storeCd;
    }

}
