package com.logisteq.tms.external.thehyundai.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TheHyundaiReqDTO {

    @JsonProperty("BODY")
    @NotEmpty(message = "BODY는 필수값입니다.")
    private List<TheHyundaiDeliveryStatusCallbackDTO> body;

    public static TheHyundaiReqDTO of(final List<TheHyundaiDeliveryStatusCallbackDTO> callbackDTOList) {

        return TheHyundaiReqDTO.builder()
                .body(callbackDTOList)
                .build();
    }

}
