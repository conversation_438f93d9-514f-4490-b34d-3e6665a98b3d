package com.logisteq.tms.external.hmg.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.logisteq.common.util.DateTimeUtil;
import com.logisteq.tms.delivery.domain.Delivery;
import com.logisteq.tms.delivery.domain.DeliveryDetail;
import com.logisteq.tms.delivery.domain.event.DeliveryFailureEvent;
import com.logisteq.tms.external.etc.utils.CustomerUtil;
import com.logisteq.tms.external.hmg.constant.HmgConstant;
import com.logisteq.tms.incident.domain.event.IncidentOccurredEvent;
import com.logisteq.tms.incident.domain.suppl.IncidentType;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Optional;

/**
 * <AUTHOR>
 * @created 2021-07-14
 * @project tms-service
 */
@Getter
@Setter
@Builder
//@JsonInclude(JsonInclude.Include.NON_NULL)
public class HmgCallbackDeliveryCancelledDTO {

    // 현대백화점 주문아이디
    @NotBlank(message = "현대백화점 주문아이디가 비어있습니다.")
    private String orderAgencyOrderId;

    // 상점아이디
    @NotBlank(message = "상점아이디가 비어있습니다.")
    private String orderAgencyStoreId;

    // 배달대행사 아이디
    @NotBlank(message = "배달대행사 아이디가 비어있습니다.")
    private String deliveryAgencyId;

    // 배달취소 유형
    @NotBlank(message = "배달취소 유형이 비어있습니다.")
    private String cancelType;

    // 배달취소 사유
    @NotNull(message = "배달취소 사유가 없습니다.")
    private String cancelReason;

    // 배달취소 수수료
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer cancelFee;

    // 배달취소 일시
    @NotNull(message = "배달취소 일시가 없습니다.")
    private Long changedAt;

    @Override
    public String toString() {
        return ReflectionToStringBuilder.toString(this, ToStringStyle.SHORT_PREFIX_STYLE, false, false, false, null);
    }

    public static HmgCallbackDeliveryCancelledDTO of(@NotNull final DeliveryFailureEvent deliveryFailureEvent,
                                                     @NotNull final Delivery delivery) {

        final String customerOrderId = deliveryFailureEvent.getCustomerOrderId();

        final String orderAgencyOrderId = CustomerUtil.extractOrganizationCodeAndUniqueCode(customerOrderId).getRight();
        final String orderAgencyStoreId = delivery.getDetail().getReceiver().getReceiverName();
        final String deliveryAgencyId = HmgConstant.THE_HYUNDAI_DELIVERY_AGENCY_ID;
        final String cancelType = "CHANGE_CUSTOMER_MIND";
        final String cancelReason = Optional.ofNullable(deliveryFailureEvent.getDeliveryFailureMessage()).orElse("");
        final Long changedAt = DateTimeUtil.convertLocalDateTimeToEpochMillis(deliveryFailureEvent.getDeliveryFailureDt());

        return HmgCallbackDeliveryCancelledDTO.builder()
                .orderAgencyOrderId(orderAgencyOrderId)
                .orderAgencyStoreId(orderAgencyStoreId)
                .deliveryAgencyId(deliveryAgencyId)
                .cancelType(cancelType)
                .cancelReason(cancelReason)
                .cancelFee(0)
                .changedAt(changedAt)
                .build();
    }

    public static HmgCallbackDeliveryCancelledDTO of(@NotNull final IncidentOccurredEvent incidentOccurredEvent,
                                                     @NotNull final Delivery delivery) {

        final DeliveryDetail deliveryDetail = delivery.getDetail();
        final String customerOrderId = deliveryDetail.getCustomerOrderId();

        final String orderAgencyOrderId = CustomerUtil.extractOrganizationCodeAndUniqueCode(customerOrderId).getRight();
        final String orderAgencyStoreId = deliveryDetail.getReceiver().getReceiverName();
        final String deliveryAgencyId = HmgConstant.THE_HYUNDAI_DELIVERY_AGENCY_ID;
        final String cancelType = incidentTypeToHmgCancelType(incidentOccurredEvent.getIncidentType());
        final String cancelReason = Optional.ofNullable(incidentOccurredEvent.getDetail()).orElse("");
        final Long changedAt = DateTimeUtil.convertLocalDateTimeToEpochMillis(incidentOccurredEvent.getOccurredAt());

        return HmgCallbackDeliveryCancelledDTO.builder()
                .orderAgencyOrderId(orderAgencyOrderId)
                .orderAgencyStoreId(orderAgencyStoreId)
                .deliveryAgencyId(deliveryAgencyId)
                .cancelType(cancelType)
                .cancelReason(cancelReason)
                .cancelFee(0)
                .changedAt(changedAt)
                .build();
    }

    private static String incidentTypeToHmgCancelType(@NotNull final IncidentType incidentType) {

        if (IncidentType.VEHICLE_ACCIDENT.equals(incidentType)) {
            return "ACCIDENT";
        } else if (IncidentType.VEHICLE_BREAKDOWN.equals(incidentType)) {
            return "BREAKDOWN";
        } else {
            return "ETC";
        }
    }

}