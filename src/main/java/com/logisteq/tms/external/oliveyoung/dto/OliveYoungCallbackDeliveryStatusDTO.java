package com.logisteq.tms.external.oliveyoung.dto;

import com.logisteq.tms.delivery.domain.event.DeliveryCompletedEvent;
import com.logisteq.tms.delivery.domain.event.DeliveryFailureEvent;
import com.logisteq.tms.delivery.domain.event.DeliveryGoingEvent;
import com.logisteq.tms.delivery.domain.suppl.DeliveryFailureType;
import com.logisteq.tms.delivery.domain.suppl.DeliveryStatus;
import com.logisteq.tms.external.etc.utils.CustomerUtil;
import com.logisteq.tms.external.oliveyoung.constant.OliveYoungConstant;
import com.logisteq.tms.external.oliveyoung.constant.OliveYoungDeliveryState;
import com.logisteq.tms.rider.domain.Rider;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

@Getter
@Setter
@Builder
//@JsonInclude(JsonInclude.Include.NON_NULL)
public class OliveYoungCallbackDeliveryStatusDTO {

    // 배달대행사 ID
    @NotBlank(message = "배달대행사 ID가 비어있습니다.")
    private String deliveryAgentId;

    // 배달대행사명
    private String deliveryAgentName;

    // 주문번호
    @NotBlank(message = "주문번호가 비어있습니다.")
    private String ordNo;

    // 배달상태
    @NotBlank(message = "배달상태가 비어있습니다.")
    private String deliveryState;

    // 배달상태 변경일시
    @NotBlank(message = "배달상태 변경일시가 비어있습니다.")
    private String changeTime;

    // 배달상태 사유
    private String reason;

    // 배달상태 비고
    private String remark;

    // 배달기사명
    private String riderName;

    // 배달기사 전화번호
    private String riderPhoneNo;

    // 도착지 이미지 주소
    private String arriveImageUrl;

    // 추가정보
    private String addedInfo;

    public static OliveYoungCallbackDeliveryStatusDTO of(@NotNull final DeliveryGoingEvent deliveryGoingEvent,
                                                         final Rider rider) {

        final String ordNo = CustomerUtil.extractOrganizationCodeAndUniqueCode(deliveryGoingEvent.getCustomerOrderId()).getRight();
        final DeliveryStatus deliveryStatus = deliveryGoingEvent.getDeliveryStatus();
        final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(OliveYoungConstant.OY_CHANGE_TIME_FORMAT);
        final String changeTime = deliveryGoingEvent.getRealGoingDateTime().format(dateTimeFormatter);

        return OliveYoungCallbackDeliveryStatusDTO.builder()
                .deliveryAgentId(OliveYoungConstant.OY_DELIVERY_AGENT_ID)
                .deliveryAgentName(OliveYoungConstant.OY_DELIVERY_AGENT_NAME)
                .ordNo(ordNo)
                .deliveryState(convertDeliveryStatus(deliveryStatus))
                .changeTime(Optional.ofNullable(changeTime).orElse(LocalDateTime.now().format(dateTimeFormatter)))
                .reason("")
                .remark("")
                .riderName(Optional.ofNullable(rider).map(Rider::getName).orElse(""))
                .riderPhoneNo(Optional.ofNullable(rider).map(Rider::getMobile).orElse(""))
                .arriveImageUrl("")
                .addedInfo("")
                .build();
    }

    public static OliveYoungCallbackDeliveryStatusDTO of(@NotNull final DeliveryCompletedEvent deliveryCompletedEvent,
                                                         final Rider rider,
                                                         final String arriveImageUrl) {

        final String ordNo = CustomerUtil.extractOrganizationCodeAndUniqueCode(deliveryCompletedEvent.getCustomerOrderId()).getRight();
        final DeliveryStatus deliveryStatus = deliveryCompletedEvent.getDeliveryStatus();
        final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(OliveYoungConstant.OY_CHANGE_TIME_FORMAT);
        final String changeTime = deliveryCompletedEvent.getDeliveryCompletedDt().format(dateTimeFormatter);
        final String remark = deliveryCompletedEvent.getDeliveryCompletedMessage();

        return OliveYoungCallbackDeliveryStatusDTO.builder()
                .deliveryAgentId(OliveYoungConstant.OY_DELIVERY_AGENT_ID)
                .deliveryAgentName(OliveYoungConstant.OY_DELIVERY_AGENT_NAME)
                .ordNo(ordNo)
                .deliveryState(convertDeliveryStatus(deliveryStatus))
                .changeTime(Optional.ofNullable(changeTime).orElse(LocalDateTime.now().format(dateTimeFormatter)))
                .reason("")
                .remark(Optional.ofNullable(remark).orElse(""))
                .riderName(Optional.ofNullable(rider).map(Rider::getName).orElse(""))
                .riderPhoneNo(Optional.ofNullable(rider).map(Rider::getMobile).orElse(""))
                .arriveImageUrl(Optional.ofNullable(arriveImageUrl).orElse(""))
                .addedInfo("")
                .build();
    }

    public static OliveYoungCallbackDeliveryStatusDTO of(@NotNull final DeliveryFailureEvent deliveryFailureEvent,
                                                         final Rider rider) {

        final String ordNo = CustomerUtil.extractOrganizationCodeAndUniqueCode(deliveryFailureEvent.getCustomerOrderId()).getRight();
        final DeliveryStatus deliveryStatus = deliveryFailureEvent.getDeliveryStatus();
        final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(OliveYoungConstant.OY_CHANGE_TIME_FORMAT);
        final String changeTime = deliveryFailureEvent.getDeliveryFailureDt().format(dateTimeFormatter);
        final String reason = deliveryFailureEvent.getDeliveryFailureType().getTypeString();
        final String remark = deliveryFailureEvent.getDeliveryFailureMessage();

        return OliveYoungCallbackDeliveryStatusDTO.builder()
                .deliveryAgentId(OliveYoungConstant.OY_DELIVERY_AGENT_ID)
                .deliveryAgentName(OliveYoungConstant.OY_DELIVERY_AGENT_NAME)
                .ordNo(ordNo)
                .deliveryState(convertDeliveryStatus(deliveryStatus))
                .changeTime(Optional.ofNullable(changeTime).orElse(LocalDateTime.now().format(dateTimeFormatter)))
                .reason(Optional.ofNullable(reason).orElse(DeliveryFailureType.OTHER.getTypeString()))
                .remark(Optional.ofNullable(remark).orElse(""))
                .riderName(Optional.ofNullable(rider).map(Rider::getName).orElse(""))
                .riderPhoneNo(Optional.ofNullable(rider).map(Rider::getMobile).orElse(""))
                .arriveImageUrl("")
                .addedInfo("")
                .build();
    }

    public static OliveYoungCallbackDeliveryStatusDTO of(final String customerOrderId,
                                                         final DeliveryStatus deliveryStatus,
                                                         final String changeTime,
                                                         final Rider rider) {

        final String ordNo = CustomerUtil.extractOrganizationCodeAndUniqueCode(customerOrderId).getRight();

        return OliveYoungCallbackDeliveryStatusDTO.builder()
                .deliveryAgentId(OliveYoungConstant.OY_DELIVERY_AGENT_ID)
                .deliveryAgentName(OliveYoungConstant.OY_DELIVERY_AGENT_NAME)
                .ordNo(ordNo)
                .deliveryState(convertDeliveryStatus(deliveryStatus))
                .changeTime(changeTime)
                .reason("")
                .remark("")
                .riderName(Optional.ofNullable(rider).map(Rider::getName).orElse(""))
                .riderPhoneNo(Optional.ofNullable(rider).map(Rider::getMobile).orElse(""))
                .arriveImageUrl("")
                .addedInfo("")
                .build();
    }

    public static String convertDeliveryStatus(final DeliveryStatus deliveryStatus) {

        String deliveryState = OliveYoungDeliveryState.ALLOCATED.getState();

        switch (deliveryStatus) {
            case GOING:
            case SERVICING:
                deliveryState = OliveYoungDeliveryState.PICKUP_FINISHED.getState();
                break;

            case COMPLETED:
                deliveryState = OliveYoungDeliveryState.DELIVERY_COMPLETE.getState();
                break;

            case FAILURE:
                deliveryState = OliveYoungDeliveryState.RETURNING.getState();
                break;

            default:
                break;
        }

        return deliveryState;
    }

}
