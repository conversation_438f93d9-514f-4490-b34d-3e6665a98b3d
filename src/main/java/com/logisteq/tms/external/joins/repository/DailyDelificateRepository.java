package com.logisteq.tms.external.joins.repository;

import com.logisteq.tms.external.joins.domain.DailyDelificate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface DailyDelificateRepository extends JpaRepository<DailyDelificate, Long>, JpaSpecificationExecutor<DailyDelificate> {

    DailyDelificate findByAloaProjectName(final String projectName);

    DailyDelificate findTopBySendDateOrderByCreatedAtDesc(final String sendDate);

    DailyDelificate findTopByAloaProjectNameIsStartingWithOrderByCreatedAtDesc(final String projectName);

    DailyDelificate findByAloaProjectId(final Long aloaProjectId);

    @Transactional
    Integer deleteByAloaProjectId(final Long aloaProjectId);

}
