package com.logisteq.tms.product.controller;

import com.logisteq.common.constant.BaseUrl;
import com.logisteq.common.exception.CustomException;
import com.logisteq.tms.product.domain.Product;
import com.logisteq.tms.product.domain.ProductAllocation;
import com.logisteq.tms.product.dto.*;
import com.logisteq.tms.product.repository.ProductAllocationRepository;
import com.logisteq.tms.product.service.ProductHistoryService;
import com.logisteq.tms.product.service.ProductService;
import com.logisteq.tms.project.repository.ProjectRepository;
import com.logisteq.tms.project.service.ProjectService;
import com.logisteq.tms.push.service.PushService;
import com.logisteq.tms.rider.domain.Rider;
import com.logisteq.tms.rider.service.RiderService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.Email;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Tag(name = "Product")
@Slf4j
@RestController
@Validated
public class ProductController {

    private final ProductService productService;
    private final ProductHistoryService productHistoryService;
    private final PushService pushService;
    private final ProjectRepository projectRepository;
    private final ProductAllocationRepository productAllocationRepository;
    private final ProjectService projectService;
    private final RiderService  riderService;

    @Autowired
    public ProductController(final ProductService productService,
                             final ProductHistoryService productHistoryService,
                             final PushService pushService,
                             final ProjectRepository projectRepository,
                             final ProductAllocationRepository productAllocationRepository,
                             final ProjectService projectService,
                             final RiderService riderService) {

        this.productService = productService;
        this.productHistoryService = productHistoryService;
        this.pushService = pushService;
        this.projectRepository = projectRepository;
        this.productAllocationRepository = productAllocationRepository;
        this.projectService = projectService;
        this.riderService = riderService;
    }

    /**
     * 개별상품 목록 저장 API
     *
     * @param productDTOList
     * @return
     */
    @Hidden
    @PostMapping(value = BaseUrl.PRODUCTS_URL, consumes = MediaType.APPLICATION_JSON_VALUE) // [WMS-API]
    public List<ProductDTO> saveProducts(@Valid @RequestBody final List<ProductDTO> productDTOList) {

        List<ProductDTO> responseList = new ArrayList<>();

        productDTOList.forEach(productDTO -> {
            final List<Product> productList = productService.getProductsByProductBarcodeAndItemCode(productDTO.getProductBarcode(), productDTO.getItemCode());

            if (CollectionUtils.isNotEmpty(productList)) {
                // 상품이 이미 등록돼있으면 업데이트
                productList.forEach(p -> {
                    final Product updProduct = productService.updateProduct(p.getProductId(), productDTO);
                    if (Objects.nonNull(updProduct)) {
                        log.info("[saveProducts] 상품정보 업데이트 productBarcode:{}, itemCode:{}", p.getProductBarcode(), p.getItemCode());
                        responseList.add(ProductDTO.parseFromProduct(updProduct, null));
                    } else {
                        log.error("[saveProducts] 상품정보 업데이트 실패 productBarcode:{}, itemCode:{}", p.getProductBarcode(), p.getItemCode());
                    }
                });
            } else {
                // 해당 상품코드의 개별상품이 없으면 새로 등록
                final Product regProduct = productService.saveProduct(Product.of(productDTO));

                if (Objects.nonNull(regProduct)) {
                    log.info("[saveProducts] 상품정보 등록 productBarcode:{}, itemCode:{}", productDTO.getProductBarcode(), productDTO.getItemCode());
                    responseList.add(ProductDTO.parseFromProduct(regProduct, null));
                } else {
                    log.error("[saveProducts] 상품정보 등록 실패 productBarcode:{}, itemCode:{}", productDTO.getProductBarcode(), productDTO.getItemCode());
                }
            }
        });

        return responseList;
    }

    /**
     * 프로젝트 별 개별상품 목록 Get API
     *
     * @param response
     * @param projectId
     * @return
     */
    @Hidden
    @GetMapping(value = ApiUrl.PRODUCT_PROJECT)
    public List<ProjectRiderProductsDTO> getProjectProducts(HttpServletResponse response, @PathVariable final Long projectId)    {

        return productService.getProjectRiderProducts(projectId);
    }

    /**
     * 개별상품 목록 조회 API
     *
     * @return
     */
    @Hidden
    @GetMapping(BaseUrl.PRODUCTS_URL) // [WMS-API]
    public List<ProductDTO> getProducts() {

        final List<Product> productList = productService.getProducts();
        List<ProductDTO> productDTOList = new ArrayList<ProductDTO>();

        if (CollectionUtils.isNotEmpty(productList)) {
            productDTOList = ProductDTO.parseFromProductList(productList);
        } else {
            throw new CustomException(HttpStatus.NOT_FOUND, "상품 목록이 없습니다.", false);
        }

        return productDTOList;
    }

    /**
     * 상품코드로 개별상품 단일 조회 API
     *
     * @param itemCode
     * @return
     */
    @Hidden
    @GetMapping(BaseUrl.PRODUCTS_URL + ApiUrl.ITEM_CODE) // [WMS-API]
    public ProductDTO getProductByItemCode(@PathVariable final String itemCode) {

        ProductDTO productDTO = null;
        final List<Product> productList = productService.getProductsByItemCode(itemCode);

        if (CollectionUtils.isNotEmpty(productList)) {
            final Product product = productList.get(0); // 가장 최신꺼 하나

            if (Objects.nonNull(product)) {
                productDTO = ProductDTO.parseFromProduct(product, null);
            }
        }

        return productDTO;
    }

    /**
     * productBarcode, projectId, riderId로 개별상품 단일 조회
     *
     * @param productBarcode
     * @param projectId
     * @param riderId
     * @return
     */
    @Hidden
    @GetMapping(BaseUrl.PRODUCTS_URL + ApiUrl.PRODUCT_BARCODE)
    public ProductDTO getProductByProductBarcode(@PathVariable final String productBarcode,
                                        @RequestParam @NotNull @Positive final Long projectId,
                                        @RequestParam @NotNull @Positive final Long riderId) {

        ProductDTO productDTO = null;
        final String savedProductBarcode = Product.convertScannedProductBarcode(productBarcode, "");
        final List<Product> productList = productService.getProductsByProductBarcode(savedProductBarcode);

        if (CollectionUtils.isNotEmpty(productList)) {
            final Product product = productList.get(0); // 동일 바코드를 갖는 상품 목록중 가장 최신 상품

            if (Objects.nonNull(product)) {
                final ProductAllocation productAllocation = productService.getLastProductAllocation(projectId, riderId, product.getProductId());
                final Long currentQuantity = Objects.nonNull(productAllocation) ? productAllocation.getCurrentQuantity() : null; // 단일 조회시 App에서 최신 현재수량을 알고 싶어한다.

                productDTO = ProductDTO.parseFromProduct(product, currentQuantity);
            }
        } else {
            throw new CustomException(HttpStatus.NOT_FOUND, "바코드:" + productBarcode + " 로 조회된 상품이 없습니다. (projectId:" + projectId + ", riderId:" + riderId + ")", false);
        }

        return productDTO;
    }

    /**
     * 차량의 현재 상품 현황 조회
     *
     * @param email
     * @param date
     * @param riderId
     * @return
     */
    @Hidden
    @GetMapping(BaseUrl.PRODUCTS_URL + ApiUrl.STATUS) // [WMS-API]
    public List<ProjectRiderProductsDTO> getProductCurrentStatus( @RequestParam(required = false) Long projectId,
                                                                  @RequestParam(required = false) @Email final String email,
                                                                  @RequestParam(required = false) final String date,
                                                                  @RequestParam(required = false) final Long riderId ) {

        if( Objects.isNull(projectId)) {
            projectId = this.projectService.getAutoCreateProjectIdByOrgCodeOrEmail(null, email, date);
        }

        if (Objects.isNull(riderId)) {
            // riderId가 null이면 해당 project에 속한 모든 차량의 개별상품 현황 조회
            return productService.getProjectRiderProducts(projectId);
        } else {
            // 특정 rider 차량의 개별상품 현황 조회
            List<ProjectRiderProductsDTO> projectRiderProductsDTOList = new ArrayList<>();
            final ProjectRiderProductsDTO projectRiderProductsDTO = productService.getProductCurrentStatus(projectId, riderId);

            if (Objects.nonNull(projectRiderProductsDTO)) {
                projectRiderProductsDTOList.add(projectRiderProductsDTO);
            } else {
                projectRiderProductsDTOList = null;
            }

            return projectRiderProductsDTOList;
        }
    }

    /**
     * 개별상품 업데이트 API (단일)
     *
     * @param productId
     * @param productDTO
     * @return
     */
    @Hidden
    @PutMapping(BaseUrl.PRODUCTS_URL + ApiUrl.PRODUCT_ID) // [WMS-API]
    public ProductDTO updateProduct(@PathVariable @Min(1) final Long productId,
                               @Valid @RequestBody final ProductDTO productDTO) {

        if (Objects.nonNull(productDTO.getProductId()) && !productDTO.getProductId().equals(productId)) {
            throw new CustomException(HttpStatus.BAD_REQUEST, "productId가 올바르지 않습니다.", false);
        }

        final Product product = productService.updateProduct(productId, productDTO);

        if (Objects.nonNull(product)) {
            return ProductDTO.parseFromProduct(product, null);
        } else {
            return null;
        }
    }

    /**
     * 개별상품 업데이트 API (다중)
     *
     * @param productDTOList
     * @return
     */
    @Hidden
    @PutMapping(BaseUrl.PRODUCTS_URL) // [WMS-API]
    public List<ProductDTO> updateProducts(@Valid @RequestBody final List<ProductDTO> productDTOList) {

        List<ProductDTO> responseList = new ArrayList<ProductDTO>();

        for (ProductDTO dto : productDTOList) {
            final Long productId = dto.getProductId();

            // Request Body에 productId가 있는 경우만 업데이트
            if (Objects.nonNull(productId) && productId > 0) {
                final ProductDTO productDTO = this.updateProduct(productId, dto);

                if (Objects.nonNull(productDTO)) {
                    responseList.add(productDTO);
                }
            }
        }

        return responseList;
    }

    /**
     * 개별상품 삭제 API
     *
     * @param productId
     * @return
     */
    @Hidden
    @DeleteMapping(BaseUrl.PRODUCTS_URL + ApiUrl.PRODUCT_ID) // [WMS-API]
    public Integer deleteProduct(@PathVariable @Min(1) final Long productId) {

        return productService.deleteByProductId(productId);
    }

    /**
     * 개별상품 검수상태 변경 API
     *
     * @param projectId
     * @param riderId
     * @param inspectionDTOList
     * @return
     */
    @Hidden
    @PutMapping(BaseUrl.PRODUCTS_URL + ApiUrl.INSPECTION)
    public List<ProductAllocationDTO> updateProductInspectionStatus(@RequestParam @NotNull @Positive final Long projectId,
                                                              @RequestParam @NotNull @Positive final Long riderId,
                                                              @NotNull @Valid @RequestBody final List<ProductInspectionDTO> inspectionDTOList) {

        if (CollectionUtils.isNotEmpty(inspectionDTOList)) {
            List<ProductAllocationDTO> allocationDTOList = new ArrayList<>();

            for (int i = 0; i < inspectionDTOList.size(); i++) {
                final ProductInspectionDTO inspectionDTO = inspectionDTOList.get(i);
                final ProductAllocation allocation = productService.updateProductInspectionStatus(projectId, riderId, inspectionDTO, i);

                if (Objects.nonNull(allocation)) {
                    final Long deliveryId = Objects.nonNull(inspectionDTO.getDeliveryId()) ? inspectionDTO.getDeliveryId() : null;

                    productHistoryService.saveProductHistory(allocation, inspectionDTO);
                    allocationDTOList.add(ProductAllocationDTO.createAllocationDto(allocation, deliveryId));
                }
            }

            final Long userId = projectRepository.findUserIdByProjectId(projectId);
            pushService.sendProductInspectionAndOrderToWeb(userId, projectId);

            return allocationDTOList;
        } else {
            throw new CustomException(HttpStatus.BAD_REQUEST, "ProductInspectionDTO 리스트가 없습니다.", false);
        }
    }

    /**
     * 개별상품의 당일(프로젝트) 전체 현황 조회
     *
     * @param response
     * @param projectId
     * @return
     */
    @Hidden
    @GetMapping(BaseUrl.PRODUCTS_URL + ApiUrl.OVERALL_STATUS)
    public List<RiderProductsDTO> getProductsOverallStatusByProject(HttpServletResponse response,
                                                                    @RequestParam @NotNull @Positive final Long projectId) {

        return productService.getProductsOverallStatusByProject(projectId);
    }

    /**
     * 고객에게 상품패킹 알림을 위한 하차시작 설정
     *
     * @param deliveryId
     * @param projectId
     * @param riderId
     */
    @Hidden
    @PostMapping(BaseUrl.PRODUCTS_URL + ApiUrl.START_UNLOADING + ApiUrl.DELIVERY_ID)
    public void setStartProductUnloading(@PathVariable @NotNull @Positive final Long deliveryId,
                                         @RequestParam(required = false) final Long projectId,
                                         @RequestParam(required = false) final Long riderId) {

        productService.setStartProductUnloading(deliveryId, projectId, riderId);
    }

    /**
     * 주문접수된 개별상품의 하차여부 조회
     *
     * @param deliveryId
     * @return
     */
    @Hidden
    @GetMapping(BaseUrl.PRODUCTS_URL + ApiUrl.CHECK_UNLOADING)
    public List<DeliveryProductDTO> checkProductUnloading(@RequestParam @NotNull @Positive final Long deliveryId) {

        return productService.checkProductUnloading(deliveryId);
    }

    // 상품 주문 접수, 취소 Dev 테스트용 (임시)
    @Hidden
    @Deprecated
    @PutMapping("/api/products/reception/{productAllocationId}")
    public void receiveProductOrder(@PathVariable @Min(1) final Long productAllocationId,
                                     @RequestParam @NotNull @Positive final Long receptionQuantity) {

        productService.updateByProductOrder(productAllocationId, receptionQuantity, true, null);

        final ProductAllocation productAllocation = productAllocationRepository.findByProductAllocationId(productAllocationId).orElse(null);
        if (Objects.nonNull(productAllocation)) {
            final Long projectId = productAllocation.getProjectId();
            final Long userId = projectRepository.findUserIdByProjectId(projectId);
            pushService.sendProductInspectionAndOrderToWeb(userId, projectId);
        }
    }

    // 상품 주문 접수, 취소 Dev 테스트용 (임시)
    @Hidden
    @Deprecated
    @PutMapping("/api/products/cancellation/{productAllocationId}")
    public void cancelProductOrder(@PathVariable @Min(1) final Long productAllocationId,
                                    @RequestParam @NotNull @Positive final Long cancellationQuantity) {

        productService.updateByProductOrder(productAllocationId, cancellationQuantity, false, "주문취소");

        final ProductAllocation productAllocation = productAllocationRepository.findByProductAllocationId(productAllocationId).orElse(null);
        if (Objects.nonNull(productAllocation)) {
            final Long projectId = productAllocation.getProjectId();
            final Long userId = projectRepository.findUserIdByProjectId(projectId);
            pushService.sendProductInspectionAndOrderToWeb(userId, projectId);
        }
    }

    @Hidden
    @PutMapping(BaseUrl.PRODUCTS_URL + ApiUrl.INSPECTION_BY_RIDER_MOBILE)
    public List<ProductAllocationDTO> updateProductInspectionStatusByMobile(@RequestParam(required = true) @Email String email,
                                                                            @RequestParam(required = false) final String date,
                                                                            @RequestParam(required = true) String mobile,
                                                                            @NotNull @Valid @RequestBody final List<ProductInspectionDTO> inspectionDTOList) {

        final Rider rider = riderService.getRider(mobile)
                .orElseThrow(() -> new CustomException(HttpStatus.NO_CONTENT, "기사 정보가 없습니다."));
        final Long projectId = this.projectService.getAutoCreateProjectIdByOrgCodeOrEmail(null, email, date);

        return updateProductInspectionStatus(projectId, rider.getId(), inspectionDTOList);
    }

}
