package com.logisteq.tms.product.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.logisteq.tms.delivery.domain.DeliveryProduct;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderItemDTO {

    @Schema(description = "상품코드", example = "C25480")
    private String itemCode; // 상품코드

    @Schema(description = "바코드", example = "87033367")
    private String productBarcode; // 실물바코드. TheHyundai에서는 입고, 분류, 상차, 하차시 사용하는 물류코드

    @Schema(description = "상품명", example = "삼다수 2L")
    private String itemName; // 상품명

    @Schema(description = "주문수량", example = "1")
    private Long orderQuantity; // 주문수량

    @Schema(description = "보관유형", example = "상온")
    private String storageType; // 보관유형

    @Schema(description = "박스번호", example = "123")
    private String boxNumber; // 박스번호 (상차후에 알 수 있다.)

    public static OrderItemDTO of(final DeliveryProduct product) {

        return OrderItemDTO.builder()
                .itemCode(product.getItemCode())
                .productBarcode(product.getProductBarcode())
                .itemName(product.getItemName())
                .orderQuantity(product.getOrderQuantity())
                .build();
    }

}
