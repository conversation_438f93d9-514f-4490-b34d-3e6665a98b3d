package com.logisteq.tms.delivery.domain;

import com.logisteq.tms.delivery.domain.suppl.GlovisDeliveryStatus;
import com.logisteq.tms.delivery.domain.suppl.GlovisUndeliveryType;
import lombok.*;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.Optional;

@Entity
@Table(name = "delivery_glovis_status_history")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EntityListeners(value = {AuditingEntityListener.class})
public class DeliveryGlovisStatusHistory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long historyId;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "delivery_id", nullable = false)
    private Delivery delivery;

    /**
     * 상태코드
     */
    @Column(nullable = false, length = 4)
    private String status;

    /**
     * 상태명
     */
    @Column(nullable = false, length = 60)
    private String statusName;

    /**
     * 배송상태변경일시 (오더일시)
     */
    @Column(columnDefinition = "DATETIME(3)", updatable = false)
    private LocalDateTime statusUpdateDt;

    /**
     * 점소명
     */
    @Column(length = 200)
    private String branchName;

    /**
     * 미배송 사유코드
     */
    @Column(length = 4)
    private String unDeliveryCode;

    /**
     * 미배송 사유명
     */
    @Column(length = 60)
    private String unDeliveryCodeName;

    /**
     * 미배송 상세사유
     */
    @Column(length = 300)
    private String unDeliveryCodeDetail;

    public static DeliveryGlovisStatusHistory of(final Delivery delivery,
                                                 final GlovisDeliveryStatus deliveryStatus,
                                                 final LocalDateTime statusUpdateDt,
                                                 final String branchName,
                                                 final GlovisUndeliveryType undeliveryType,
                                                 final String undeliveryCodeDetail) {

        return DeliveryGlovisStatusHistory.builder()
                .delivery(delivery)
                .status(Optional.ofNullable(deliveryStatus).map(GlovisDeliveryStatus::getCode).orElse(""))
                .statusName(Optional.ofNullable(deliveryStatus).map(GlovisDeliveryStatus::getName).orElse(""))
                .statusUpdateDt(statusUpdateDt)
                .branchName(branchName)
                .unDeliveryCode(Optional.ofNullable(undeliveryType).map(GlovisUndeliveryType::getUnDeliveryCode).orElse(""))
                .unDeliveryCodeName(Optional.ofNullable(undeliveryType).map(GlovisUndeliveryType::getUnDeliveryCodeName).orElse(""))
                .unDeliveryCodeDetail(Optional.ofNullable(undeliveryCodeDetail).orElse(""))
                .build();
    }
}
