package com.logisteq.tms.delivery.domain.event;

import com.logisteq.tms.delivery.domain.TrunkLine;
import com.logisteq.tms.delivery.domain.suppl.TrunkLineStatus;
import com.logisteq.tms.external.thehyundai.constant.TheHyundaiConstant;
import com.logisteq.tms.user.domain.Organization;
import lombok.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TrunkLineEvent {

    // 주문번호
    private String slipNo;

    // 배송상태 대구분
    private String deliveryStatus;

    // 배송상태 중구분
    private String statusType;

    // 변경일자
    private String changeDt;

    // 변경시각
    private String changeDs;

    // 기사명
    private String driverNm;

    // 기사연락처
    private String driverHp;

    // 조직
    private Organization organization;

    public static TrunkLineEvent of(final TrunkLine trunkLine,
                                    final String riderName,
                                    final String riderMobile) {

        final TrunkLineStatus trunkLineStatus = trunkLine.getTrunkLineStatus();
        final String deliveryStatus = TrunkLineStatus.PICKUP.equals(trunkLineStatus) ? TheHyundaiConstant.TheHyundaiDeliveryStatus.TRUNK_LINE_PICKUP : TheHyundaiConstant.TheHyundaiDeliveryStatus.TRUNK_LINE_DROPOFF;
        final LocalDateTime scanDt = trunkLine.getScanDt();
        final String changeDt = Optional.ofNullable(scanDt).map(t -> t.format(DateTimeFormatter.ofPattern("yyyyMMdd"))).orElse("");
        final String changeDs = Optional.ofNullable(scanDt).map(t -> t.toLocalTime().format(DateTimeFormatter.ofPattern("HHmmss"))).orElse("");

        return TrunkLineEvent.builder()
                .slipNo(trunkLine.getScanCode())
                .deliveryStatus(deliveryStatus)
                .statusType(trunkLineStatus.toString())
                .changeDt(changeDt)
                .changeDs(changeDs)
                .driverNm(riderName)
                .driverHp(riderMobile)
                .organization(trunkLine.getOrganization())
                .build();
    }

}
