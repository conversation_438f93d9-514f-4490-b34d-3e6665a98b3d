package com.logisteq.tms.delivery.dto;

import com.logisteq.common.component.excel.annotation.PxlColumn;
import com.logisteq.common.component.excel.styler.data.PxlDataHorizontalCenterTextStyler;
import com.logisteq.tms.delivery.constant.DeliveryConstant;
import lombok.*;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ReportDailyDTO {

    // 조회일
    @PxlColumn(name = "조회일", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_NAME, exportColumnDataCellStyler = PxlDataHorizontalCenterTextStyler.class)
    private String period;

    // 점포명
    @PxlColumn(name = "점포명", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_AMOUNT, exportColumnDataCellStyler = PxlDataHorizontalCenterTextStyler.class)
    private String branchName;

    // 배송담당자
    @PxlColumn(name = "배송담당자", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_AMOUNT, exportColumnDataCellStyler = PxlDataHorizontalCenterTextStyler.class)
    private String driverName;

    // 프로젝트명
    @PxlColumn(name = "프로젝트명", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_NAME)
    private String projectName;

    // 누적주행거리 (km)
    @PxlColumn(name = "주행거리(km)", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_AMOUNT)
    private Long mileage;

    // 주문채널
    @PxlColumn(name = "주문채널", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_AMOUNT, exportColumnDataCellStyler = PxlDataHorizontalCenterTextStyler.class)
    private String orderChannel;

    // 거래처명
    @PxlColumn(name = "거래처명", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_AMOUNT, exportColumnDataCellStyler = PxlDataHorizontalCenterTextStyler.class)
    private String receiverName;

    // 배차진행여부
    @PxlColumn(name = "배차진행여부", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_COUNT, exportColumnDataCellStyler = PxlDataHorizontalCenterTextStyler.class)
    private String isDispatched;

    // 배송완료여부
    @PxlColumn(name = "배송완료여부", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_COUNT, exportColumnDataCellStyler = PxlDataHorizontalCenterTextStyler.class)
    private String isEnded;

    // 전화주문금액
    @PxlColumn(name = "전화주문금액", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_AMOUNT)
    private Long sisOrderAmount;

    // 온라인주문금액
    @PxlColumn(name = "온라인주문금액", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_AMOUNT)
    private Long mosOrderAmount;

    // 전체주문금액
    @PxlColumn(name = "전체주문금액", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_AMOUNT)
    private Long totalOrderAmount;

    // 배차완료금액
    @PxlColumn(name = "배차완료금액", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_AMOUNT)
    private Long dispatchAmount;

    // 미배차금액
    @PxlColumn(name = "미배차금액", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_AMOUNT)
    private Long undispatchAmount;

    // 배송완료금액
    @PxlColumn(name = "배송완료금액", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_AMOUNT)
    private Long completedAmount;

    // 미배송금액
    @PxlColumn(name = "미배송금액", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_AMOUNT)
    private Long undeliveredAmount;

    // 배송실패금액
    @PxlColumn(name = "배송실패금액", exportColumnWidth = DeliveryConstant.ExcelConst.COL_WIDTH_AMOUNT)
    private Long failureAmount;

}
