package com.logisteq.tms.delivery.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.logisteq.tms.project.dto.excel.leftpanel.ProjectDestinationSheetDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "배송 등록 응답 DTO")
public class DeliveriesResponseDTO {

    @Schema(description = "프로젝트 ID", example = "123")
    private Long projectId;

    @Schema(description = "실패 사유", example = "지오코딩 실패")
    private String reason;

    @Schema(description = "프로젝트 ID 목록")
    private List<Long> projectIds;

    @Schema(description = "지오코딩 실패 목록")
    List<ProjectDestinationSheetDTO> geoCodingFails;

    @Schema(description = "지오코딩 성공 목록 (응답에 포함되지 않음)")
    @JsonIgnore//성공 리스트는 보내주지 않음
    List<ProjectDestinationSheetDTO> geoCodingSuccess;

    @Schema(description = "배차 실패 목록 (기사를 찾을 수 없는 배송)")
    List<ProjectDestinationSheetDTO> riderNotFounds;
}
