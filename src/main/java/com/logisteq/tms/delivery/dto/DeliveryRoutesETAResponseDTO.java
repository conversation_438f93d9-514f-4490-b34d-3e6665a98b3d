package com.logisteq.tms.delivery.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.logisteq.tms.delivery.domain.DeliveryAllocation;
import com.logisteq.tms.delivery.domain.suppl.DeliveryStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;

import java.time.LocalDateTime;

@Getter
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "배송 경로 예상 도착 시간 응답 DTO")
public class DeliveryRoutesETAResponseDTO {

    @Schema(description = "배송 ID", example = "12345")
    private Long deliveryId;

    @Schema(description = "배송 순서", example = "3")
    private Integer order;

    @Schema(description = "배송 상태")
    private DeliveryStatus deliveryStatus;

    @Schema(description = "실제 예상 도착 시간", example = "2023-12-01T15:30:00")
    private LocalDateTime etaDateTimeReal;

    public static DeliveryRoutesETAResponseDTO of(final DeliveryAllocation deliveryAllocation) {

        return DeliveryRoutesETAResponseDTO.builder()
                .deliveryId(deliveryAllocation.getDelivery().getId())
                .order(deliveryAllocation.getOrderNum())
                .deliveryStatus(deliveryAllocation.getStatus())
                .etaDateTimeReal(deliveryAllocation.getRouteRun().getEtaDateTime())
                .build();
    }

}
