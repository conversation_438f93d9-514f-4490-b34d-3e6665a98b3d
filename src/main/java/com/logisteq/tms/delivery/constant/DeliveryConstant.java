package com.logisteq.tms.delivery.constant;

/**
 * 배송 상수 지정 클래스
 */
public class DeliveryConstant {

    private DeliveryConstant() {
        throw new AssertionError();
    }

    // 앱에서 전체 경로선형 올리지 않고 현재 목적지까지의 경로선형만 올린다.
    public static final boolean ENABLE_NEW_ROUTING_FEATURE = true;
    public static final Integer MAX_PRODUCT_IMAGE_URLS = 3;

    // 추천 경로만 사용 (최단경로/에코경로 탐색 시 반환되는 시간/거리가 잘못됨)
    public static final boolean USE_RECOMMENDED_ROUTING_ONLY = true;

    // 그룹/시간 주어진 프로젝트에 대해 기사별로 Thread 생성하여 경로탐색
    public static final boolean USE_MULTI_THREADS_WHEN_GROUP_ORDER_ROUTING = true;

    public static class UrlConst {
        public static final String API = "/api";
        public static final String STATUS = "/status";
        public static final String ORDER = "/order";
        public static final String ALLOCATION = "/allocation";
        public static final String UPLOAD = "/upload";
        public static final String FILE = "/file";
        public static final String FILES_BUNDLE_ENDED = "/files-bundle-ended";
        public static final String ZIP_DOWNLOAD = "/zip-download";
        public static final String VOC_LIST = "/voc-list";
        public static final String VOC = "/voc";

        public static final String DELIVERY = "/delivery";
        public static final String DELIVERIES = "/deliveries";
        public static final String DELIVERY_PLAN = DELIVERIES + "/plan";

        public static final String RIDERS = "/riders";
        public static final String RIDER = "/rider";
        public static final String VEHICLES = "/vehicles";
        public static final String PROJECTS = "/projects";
        public static final String ANONYMOUS = "/anonymous";
        public static final String CLUSTER = "/cluster";
        public static final String ROUTE = "/route";
        public static final String ROUTE_INSPECTION = "/routeinspection";
        public static final String ROUTE_SECTION = "/routesection";
        public static final String ROUTE_SECTION_PROGRESS = "/routesection/progress";
        public static final String ROUTES = "/routes";
        public static final String ROUTE_ETA = "/routeeta";
        public static final String ROUTE_OPTIMIZATION = "/routeoptim";
        public static final String REPORT = "/report";
        public static final String DELIVERY_ENDED = "/delivery-ended";
        public static final String DELIVERIES_ENDED = "/deliveries-ended";
        public static final String DELIVERIES_BUNDLE_ENDED = "/deliveries-bundle-ended";
        public static final String INSPECTION = "/inspection";
        public static final String INSPECTION_ENDED = "/inspection-ended";
        public static final String INVOICE_PRINT_COUNT = "/invoice-print-count";
        public static final String INVOICE_INFO = "/invoice-info";
        public static final String ON_DEMAND = "/ondemand";
        public static final String MEMO = "/memo";
        public static final String TMS_REPORT = "/tms-report";
    }

    public static class PathConst {
        public static final String DELIVERY_ID = "/{deliveryId}";
    }

    public static class ApiConst {

        // delivery
        public static final String DELIVERY = UrlConst.API + UrlConst.DELIVERY;
        public static final String DELIVERIES = UrlConst.API + UrlConst.DELIVERIES;
        public static final String DELIVERY_INFO = DELIVERIES + PathConst.DELIVERY_ID;
        public static final String DELIVERY_STATUS = DELIVERY_INFO + UrlConst.STATUS;
        public static final String DELIVERY_ORDER = DELIVERY_INFO + UrlConst.ORDER;
        public static final String DELIVERY_ALLOCATION = DELIVERY_INFO + UrlConst.ALLOCATION;
        public static final String DELIVERY_CLUSTER = DELIVERY + UrlConst.CLUSTER;
        public static final String DELIVERIES_CLUSTER = DELIVERIES + UrlConst.CLUSTER;
        public static final String DELIVERIES_ROUTE = DELIVERIES + UrlConst.ROUTE;
        public static final String DELIVERIES_ROUTE_INSPECTION = DELIVERIES + UrlConst.ROUTE_INSPECTION;
        public static final String DELIVERIES_ROUTE_SECTION = DELIVERIES + UrlConst.ROUTE_SECTION;
        public static final String DELIVERIES_ROUTE_SECTION_PROGRESS = DELIVERIES + UrlConst.ROUTE_SECTION_PROGRESS;
        public static final String DELIVERIES_ROUTE_ETA = DELIVERIES + UrlConst.ROUTE_ETA;
        public static final String DELIVERIES_ROUTE_OPTIMIZATION = DELIVERIES + UrlConst.ROUTE_OPTIMIZATION;
        public static final String DELIVERIES_REPORT = DELIVERIES + UrlConst.REPORT;
        public static final String DELIVERY_ENDED = DELIVERIES + UrlConst.DELIVERY_ENDED;
        public static final String DELIVERIES_ENDED = DELIVERIES + UrlConst.DELIVERIES_ENDED;
        public static final String DELIVERIES_BUNDLE_ENDED = DELIVERIES + UrlConst.DELIVERIES_BUNDLE_ENDED;
        public static final String DELIVERY_FILE = DELIVERY_INFO + UrlConst.FILE;
        public static final String DELIVERY_FILES_BUNDLE_ENDED = DELIVERY_INFO + UrlConst.FILES_BUNDLE_ENDED;
        public static final String DELIVERIES_FILE_ZIP_DOWNLOAD = DELIVERY_FILE + UrlConst.ZIP_DOWNLOAD;
        public static final String DELIVERIES_EXCEL = DELIVERIES + "/excel";
        public static final String DELIVERIES_PAST_EXCEL = DELIVERIES + "/past-excel";
        public static final String DELIVERIES_PAST = DELIVERIES + "/past";
        public static final String DELIVERY_MESSAGE = DELIVERY_INFO + "/message";
        public static final String DELIVERIES_INSPECTION = DELIVERIES + UrlConst.INSPECTION;
        public static final String DELIVERIES_INSPECTION_ENDED = DELIVERIES + UrlConst.INSPECTION_ENDED;
        public static final String DELIVERIES_RIDER = DELIVERIES + UrlConst.RIDER;
        public static final String DELIVERIES_QR_BAR_CODE = DELIVERIES + "/qr-bar-code";
        public static final String DELIVERY_VOC_SAVE = DELIVERY_INFO + UrlConst.VOC;
        public static final String DELIVERY_VOC_GET_MESSAGE_LIST = DELIVERIES + UrlConst.VOC_LIST;
        public static final String DELIVERY_SET_INVOICE_PRINT_COUNT = DELIVERIES + UrlConst.INVOICE_PRINT_COUNT;
        public static final String DELIVERIES_FORCE_STATUS = DELIVERIES + "/status-force";

        //푸디스트 배송지 추가 API - 프로젝트 이름으로 프로젝트 생성 기능 추가
        public static final String DELIVERIES_WITH_PROJECT_NAME = DELIVERIES + "/project";

        public static final String DELIVERIES_ON_DEMAND = DELIVERIES + UrlConst.ON_DEMAND;
        // anonymous delivery
        public static final String ANONY_DELIVERY = UrlConst.API + UrlConst.ANONYMOUS + UrlConst.DELIVERY;
        public static final String ANONY_DELIVERIES = UrlConst.API + UrlConst.ANONYMOUS + UrlConst.DELIVERIES;
        public static final String ANONY_DELIVERIES_CLUSTER = ANONY_DELIVERIES + UrlConst.CLUSTER;
        public static final String ANONY_DELIVERIES_ROUTE = ANONY_DELIVERIES + UrlConst.ROUTE;

        // rider
        public static final String RIDERS = UrlConst.API + UrlConst.RIDERS;

        // vehicle
        public static final String VEHICLES = UrlConst.API + UrlConst.VEHICLES;

        public static final String CANCEL_GOING = DELIVERIES + "/cancel-going";
        public static final String CANCEL_INSPECTION_END = DELIVERIES + "/cancel-inspection-end";

        public static final String DELIVERY_STATUS_HISTORY = DELIVERIES + "/history/{customerOrderIdWithOrgId}";

        public static final String DELIVERIES_UNASSIGNED = DELIVERIES +  "/unassigned";

        //올리브영 자동 생성 프로젝트 Command 컨트롤로
        public static final String DELIVERIES_COMMAND_CREATE_OLIVEYOUNG_PROJECT = DELIVERIES + "/command/oliveyoung/create";

        public static final String DELIVERY_MEMO = DELIVERY_INFO + UrlConst.MEMO;

        public static final String DESTROY_PERSONAL_INFO = DELIVERIES + "/destroy-personal-info";

        public static final String DELIVERIES_TMS_REPORT = DELIVERIES + UrlConst.TMS_REPORT;

        public static final String TRUNK_LINE = DELIVERIES + "/trunk-line";

        public static final String TRUNK_LINE_LIST = DELIVERIES + "/trunk-line/list";

        public static final String PRODUCT_DISPATCH_INFO = DELIVERIES + "/product-dispatch-info";

        public static final String PICKUP_VERIFICATION = DELIVERIES + "/pickup-verification";

        public static final String PROJECTS_DELIVERIES = DELIVERIES + "/projects-deliveries";

        public static final String DELIVERIES_CANCEL = DELIVERIES + "/cancel";
    }

    public static class ExcelConst {

        public static final int COL_WIDTH_NAME = 10000;
        public static final int COL_WIDTH_AMOUNT = 6000;
        public static final int COL_WIDTH_COUNT = 4000;
    }

}
