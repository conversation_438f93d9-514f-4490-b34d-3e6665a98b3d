package com.logisteq.tms.delivery.repository.dao;

import com.logisteq.tms.address.domain.Address;
import com.logisteq.tms.delivery.domain.Delivery;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DriverLastDeliveryDAO {
    private Long riderId;
    private Long deliveryAllocationId;
    private Delivery delivery;
    private Address destinationAddress;
    private Integer orderNum;
}
