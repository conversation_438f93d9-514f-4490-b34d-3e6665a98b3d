package com.logisteq.tms.bizmessage.repository;

import com.logisteq.tms.bizmessage.domain.BizMessageOauth;
import com.logisteq.tms.user.domain.Organization;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Repository
public interface BizMessageOauthRepository extends JpaRepository<BizMessageOauth, Long> {

    BizMessageOauth findTop1ByOrganizationAndUpdatedAtBetweenOrderByUpdatedAtDesc(@NotNull final Organization organization,
                                                                                  @NotNull final LocalDateTime fromDt,
                                                                                  @NotNull final LocalDateTime toDt);
}
