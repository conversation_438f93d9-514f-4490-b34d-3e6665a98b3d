package com.logisteq.common.exception;

import org.springframework.core.NestedRuntimeException;

@SuppressWarnings("serial")
public abstract class BaseException extends NestedRuntimeException {
	static class Message {
		public static final String DATA_PERSIST_FAILURE_EXCEPTION	= "Data Persist Failure Exception";
		public static final String ITEM_NOT_FOUND_EXCEPTION	= "Item Not Found Exception";
		public static final String ITEM_EXISTS_EXCEPTION	= "Item Exists Exception";
		public static final String INVALID_PARAMETER_EXCEPTION	= "Invalid Parameter Exception";
		public static final String ROUTING_FAIL_EXCEPTION = "Routing Fail Exception";
		public static final String GEOCODING_FAIL_EXCEPTION = "Geocoding Fail Exception";
		public static final String NO_CONTENTS = "No contents";
	}
	
	public BaseException(String message) {
		super(message);
	}
}
