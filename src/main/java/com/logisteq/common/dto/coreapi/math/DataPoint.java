package com.logisteq.common.dto.coreapi.math;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.logisteq.common.dto.PointDTO;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DataPoint extends PointDTO {
	@JsonProperty("nid")
	private Long dataId;
	
	@Builder(builderMethodName = "DataPointBuilder")
	protected DataPoint(Double x, Double y, Long dataId) {
		super(x, y);
		this.dataId = dataId;
	}
}
