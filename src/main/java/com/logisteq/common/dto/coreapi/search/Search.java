package com.logisteq.common.dto.coreapi.search;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Search {

    private List<Double> coordinates;

    @JsonProperty("adminarea")
    private String adminArea;

    @JsonProperty("subadminarea")
    private String subAdminArea;

    @JsonProperty("locality")
    private String locality;

    @JsonProperty("sublocality")
    private String subLocality;

    @JsonProperty("subno")
    private String subNo;

    @JsonProperty("postalcode")
    private String postalcode;

    @JsonProperty("intersectinglocality")
    private String intersectingLocality;

    @JsonProperty("buildingName")
    private String buildingName;

    @JsonIgnore
    public String getSearchResultBaseAddr() {

        // 검색 키워드가 주소일 경우에만 띄어쓰기가 존재한다.
        if (ArrayUtils.getLength(StringUtils.split(this.intersectingLocality, " ")) > 2) {
            return this.intersectingLocality;
        } else {
            return Arrays.stream(new String[]{
                    this.adminArea,
                    this.subAdminArea,
                    this.locality,
                    this.subLocality,
                    this.subNo,
                    this.intersectingLocality})
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.joining(" "));
        }
    }

}
