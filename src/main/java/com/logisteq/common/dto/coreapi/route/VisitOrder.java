package com.logisteq.common.dto.coreapi.route;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.logisteq.common.dto.CoordinateDTO;
import lombok.*;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VisitOrder {

    @JsonProperty("nid")
    private Long id;

    @JsonProperty("visit")
    private Long order;

    private RouteDestinationType destinationType;

    @JsonProperty("estimate")
    private Long estimatedSeconds;

    @JsonProperty("dist")
    private Double estimatedMeters;

    private List<CoordinateDTO> estimatedRoutePath;

    @Override
    public String toString() {
        return "id(" + id + "), order(" + order + "), seconds(" + estimatedSeconds + "), meters(" + estimatedMeters + ")";
    }

}
