package com.logisteq.common.component.excel.info;

import com.logisteq.common.component.excel.annotation.PxlSheet;
import com.logisteq.common.component.excel.constant.PxlConstant;
import com.logisteq.common.component.excel.exception.PxlDataException;
import com.logisteq.common.component.excel.exception.PxlException;
import com.logisteq.common.component.excel.option.PxlImportColumnOption;
import com.logisteq.common.component.excel.option.PxlImportSheetOption;
import com.logisteq.common.component.excel.utils.PxlClassUtils;
import com.logisteq.common.component.excel.utils.PxlCollectionUtils;
import com.logisteq.common.component.excel.utils.PxlReflectionUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Nullable;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 엑셀 시트 Import 정보
 *
 * <AUTHOR>
 */
@Getter
public final class PxlImportSheetInfo {

    private final Field sheetField;

    private final Class<?> rowCollectionClass;

    private final Class<?> rowClass;

    private List<String> candidateSheetNames;

    private final boolean importEnabled;

    private final boolean importOverrideSuperClassSheet;

    private final boolean importExcludeHiddenRows;

    private final boolean importExcludeHiddenColumns;

    private final boolean importEachCellOfMergedRegion;

    private final int importTitleRowIndex;          // 1-based

    private final int importFirstDataRowIndex;      // 1-based inclusive

    private final int importLastDataRowIndex;       // 1-based inclusive

    private final int importFirstDataColumnIndex;   // 1-based inclusive

    private final int importLastDataColumnIndex;    // 1-based inclusive

    private final List<PxlImportColumnOption> importColumnOptions;

    private final List<PxlImportColumnInfo> importColumnInfos;

    private final boolean isMandatory;

    @Setter
    private String actualImportSheetName;

    @Setter
    private int actualImportSheetIndex = -1;

    @Setter
    private int actualImportTitleRowIndex = -1;         // 0-based

    @Setter
    private int actualImportOriginDataRowIndex = -1;    // 0-based inclusive

    @Setter
    private int actualImportBoundDataRowIndex = -1;     // 0-based exclusive

    @Setter
    private int actualImportOriginDataColumnIndex = -1; // 0-based inclusive

    @Setter
    private int actualImportBoundDataColumnIndex = -1;  // 0-based exclusive

    private PxlImportSheetInfo(final Field sheetField,
                               final Class<?> rowCollectionClass,
                               final Class<?> rowClass,
                               final List<String> candidateSheetNames,
                               final boolean importEnabled,
                               final boolean importOverrideSuperClassSheet,
                               final boolean importExcludeHiddenRows,
                               final boolean importExcludeHiddenColumns,
                               final boolean importEachCellOfMergedRegion,
                               final int importTitleRowIndex,
                               final int importFirstDataRowIndex,
                               final int importLastDataRowIndex,
                               final int importFirstDataColumnIndex,
                               final int importLastDataColumnIndex,
                               final List<PxlImportColumnOption> importColumnOptions,
                               final List<PxlImportColumnInfo> importColumnInfos) {

        this.sheetField = sheetField;
        this.rowCollectionClass = rowCollectionClass;
        this.rowClass = rowClass;
        this.candidateSheetNames = candidateSheetNames;
        this.importEnabled = importEnabled;
        this.importOverrideSuperClassSheet = importOverrideSuperClassSheet;
        this.importExcludeHiddenRows = importExcludeHiddenRows;
        this.importExcludeHiddenColumns = importExcludeHiddenColumns;
        this.importEachCellOfMergedRegion = importEachCellOfMergedRegion;
        this.importTitleRowIndex = importTitleRowIndex;
        this.importFirstDataRowIndex = importFirstDataRowIndex;
        this.importLastDataRowIndex = importLastDataRowIndex;
        this.importFirstDataColumnIndex = importFirstDataColumnIndex;
        this.importLastDataColumnIndex = importLastDataColumnIndex;
        this.importColumnOptions = importColumnOptions;
        this.importColumnInfos = importColumnInfos;

        this.isMandatory = Objects.isNull(sheetField) || Objects.nonNull(sheetField.getAnnotation(NotEmpty.class));
    }

    @Override
    public String toString() {
        if (Objects.nonNull(sheetField)) {
            return "[" + candidateSheetNames + ", " + sheetField.getName() + "]";
        } else {
            return "[" + candidateSheetNames + "]";
        }
    }

    /**
     * Import시 시트 옵션들과 워크북 클래스로부터 엑셀 파일의 시트 정보를 수집한다.
     * 워크북 클래스보다 시트 옵션들에 우선순위가 있다.
     *
     * @param workbookClass
     * @param sheetOptions
     * @return
     * @throws PxlException
     */
    public static List<PxlImportSheetInfo> makeImportSheetInfos(@Nullable final Class<?> workbookClass,
                                                                @Nullable final List<PxlImportSheetOption> sheetOptions) throws PxlException {

//        final Field[] sheetFields = workbookClass.getDeclaredFields();
        final List<Field> sheetFields = PxlReflectionUtils.getAllFields(workbookClass);
        final List<PxlImportSheetInfo> sheetInfos = new ArrayList<>(PxlCollectionUtils.size(sheetFields));
        final Set<String> overridedSheetNames = new HashSet<>();

        for (final Field sheetField : sheetFields) {
            // Collection 형태가 아니면 무시한다.
            final Class<?> rowCollectionClass = sheetField.getType();
            if (!PxlClassUtils.isCollectionClass(rowCollectionClass)) {
                continue;
            }

            final Class<?> rowConcreteCollectionClass = PxlClassUtils.getConcreteCollectionClass(rowCollectionClass);

            // Collection에 대한 Generic Class를 구한다.
            final Class<?> rowClass = PxlReflectionUtils.getParameterizedArgument0(sheetField);

            // Sheet Field에 대한 @PxlSheet 어노테이션을 구한다.
            final PxlSheet sheetAnnotation = sheetField.getAnnotation(PxlSheet.class);

            if (Objects.nonNull(sheetAnnotation)) {

                final PxlImportSheetOption sheetOption = Optional.ofNullable(sheetOptions)
                        .flatMap(options -> options.stream()
                                .filter(o -> StringUtils.equals(o.getFieldName(), sheetField.getName()))
                                .findFirst())
                        .orElseGet(() -> Optional.ofNullable(sheetOptions)
                                .flatMap(options -> options.stream()
                                        .filter(o -> StringUtils.equals(o.getFieldName(), PxlConstant.SHEET_FIELD_NAME_WILD_CARD))
                                        .findFirst())
                                .orElse(null));

                final List<String> candidateSheetNames = makeCandidateSheetNames(sheetOption, sheetAnnotation, sheetField);

                // 시트이름이 설정되어 있지 않으면 무시한다.
                if (PxlCollectionUtils.isEmpty(candidateSheetNames)) {
                    continue;
                }

                // 시트이름이 이미 override되어 사용중이면 무시한다.
                final boolean overridedSheet = candidateSheetNames.stream()
                        .anyMatch(overridedSheetNames::contains);
                if (overridedSheet) {
                    continue;
                }

                final boolean importEnabled = Optional.ofNullable(sheetOption)
                        .flatMap(option -> Optional.ofNullable(option.getImportEnabled()))
                        .orElseGet(sheetAnnotation::importEnabled);

                final boolean importOverrideSuperClassSheet = Optional.ofNullable(sheetOption)
                        .flatMap(option -> Optional.ofNullable(option.getImportOverrideSuperClassSheet()))
                        .orElseGet(sheetAnnotation::importOverrideSuperClassSheet);

                final boolean importExcludeHiddenRows = Optional.ofNullable(sheetOption)
                        .flatMap(option -> Optional.ofNullable(option.getImportExcludeHiddenRows()))
                        .orElseGet(sheetAnnotation::importExcludeHiddenRows);

                final boolean importExcludeHiddenColumns = Optional.ofNullable(sheetOption)
                        .flatMap(option -> Optional.ofNullable(option.getImportExcludeHiddenColumns()))
                        .orElseGet(sheetAnnotation::importExcludeHiddenColumns);

                final boolean importEachCellOfMergedRegion = Optional.ofNullable(sheetOption)
                        .flatMap(option -> Optional.ofNullable(option.getImportEachCellOfMergedRegion()))
                        .orElseGet(sheetAnnotation::importEachCellOfMergedRegion);

                final int importTitleRowIndex = Optional.ofNullable(sheetOption)
                        .flatMap(option -> Optional.ofNullable(option.getImportTitleRowIndex()))
                        .orElseGet(sheetAnnotation::importTitleRowIndex);
                if (importTitleRowIndex < 0) {
                    throw new PxlDataException(candidateSheetNames + " 시트의 importTitleRowIndex의 값은 0이상이어야 합니다.");
                }

                final int importFirstDataRowIndex = Optional.ofNullable(sheetOption)
                        .flatMap(option -> Optional.ofNullable(option.getImportFirstDataRowIndex()))
                        .orElseGet(sheetAnnotation::importFirstDataRowIndex);
                if (importFirstDataRowIndex < 0) {
                    throw new PxlDataException(candidateSheetNames + " 시트의 importFirstDataRowIndex의 값은 0이상이어야 합니다.");
                }

                final int importLastDataRowIndex = Optional.ofNullable(sheetOption)
                        .flatMap(option -> Optional.ofNullable(option.getImportLastDataRowIndex()))
                        .orElseGet(sheetAnnotation::importLastDataRowIndex);
                if (importLastDataRowIndex < 0) {
                    throw new PxlDataException(candidateSheetNames + " 시트의 importLastDataRowIndex의 값은 0이상이어야 합니다.");
                }

                final int importFirstDataColumnIndex = Optional.ofNullable(sheetOption)
                        .flatMap(option -> Optional.ofNullable(option.getImportFirstDataColumnIndex()))
                        .orElseGet(sheetAnnotation::importFirstDataColumnIndex);
                if (importFirstDataColumnIndex < 0) {
                    throw new PxlDataException(candidateSheetNames + " 시트의 importFirstDataColumnIndex의 값은 0이상이어야 합니다.");
                }

                final int importLastDataColumnIndex = Optional.ofNullable(sheetOption)
                        .flatMap(option -> Optional.ofNullable(option.getImportLastDataColumnIndex()))
                        .orElseGet(sheetAnnotation::importLastDataColumnIndex);
                if (importLastDataColumnIndex < 0) {
                    throw new PxlDataException(candidateSheetNames + " 시트의 importLastDataColumnIndex의 값은 0이상이어야 합니다.");
                }

                if (importTitleRowIndex != PxlConstant.DEFAULT_IMPORT_TITLE_ROW_INDEX
                        && importFirstDataRowIndex != PxlConstant.DEFAULT_IMPORT_FIRST_DATA_ROW_INDEX
                        && importFirstDataRowIndex <= importTitleRowIndex) {
                    throw new PxlDataException(candidateSheetNames + " 시트의 importFirstDataRowIndex의 값은 importTitleRowIndex의 값보다 커야 합니다.");
                }

                if (importFirstDataRowIndex != PxlConstant.DEFAULT_IMPORT_FIRST_DATA_ROW_INDEX
                        && importLastDataRowIndex != PxlConstant.DEFAULT_IMPORT_LAST_DATA_ROW_INDEX
                        && importLastDataRowIndex < importFirstDataRowIndex) {
                    throw new PxlDataException(candidateSheetNames + " 시트의 importLastDataRowIndex의 값은 importFirstDataRowIndex의 값 이상이어야 합니다.");
                }

                if (importFirstDataColumnIndex != PxlConstant.DEFAULT_IMPORT_FIRST_DATA_COLUMN_INDEX
                        && importLastDataColumnIndex != PxlConstant.DEFAULT_IMPORT_LAST_DATA_COLUMN_INDEX
                        && importLastDataColumnIndex < importFirstDataColumnIndex) {
                    throw new PxlDataException(candidateSheetNames + " 시트의 importLastDataColumnIndex의 값은 importFirstDataColumnIndex의 값 이상이어야 합니다.");
                }

                final List<PxlImportColumnOption> importColumnOptions = Optional.ofNullable(sheetOption)
                        .map(option -> option.getImportColumnOptions())
                        .orElseGet(ArrayList::new);

                final List<PxlImportColumnInfo> importColumnInfos = new ArrayList<>();

                sheetField.setAccessible(true);
                sheetInfos.add(
                        new PxlImportSheetInfo(
                                sheetField,                     // sheetField
                                rowConcreteCollectionClass,     // rowCollectionClass
                                rowClass,                       // rowClass
                                candidateSheetNames,            // candidateSheetNames
                                importEnabled,                  // importEnabled
                                importOverrideSuperClassSheet,  // importOverrideSuperClassSheet
                                importExcludeHiddenRows,        // importExcludeHiddenRows
                                importExcludeHiddenColumns,     // importExcludeHiddenColumns
                                importEachCellOfMergedRegion,   // importEachCellOfMergedRegion
                                importTitleRowIndex,            // importTitleRowIndex
                                importFirstDataRowIndex,        // importFirstDataRowIndex
                                importLastDataRowIndex,         // importLastDataRowIndex
                                importFirstDataColumnIndex,     // importFirstDataColumnIndex
                                importLastDataColumnIndex,      // importLastDataColumnIndex
                                importColumnOptions,            // importColumnOptions
                                importColumnInfos               // importColumnInfos
                        ));

                if (importEnabled && importOverrideSuperClassSheet) {
                    overridedSheetNames.addAll(candidateSheetNames);
                }
            }
        }

        return sheetInfos;
    }

    /**
     * Import시 메서드 파라미터로부터 엑셀 파일의 시트 정보를 수집한다.
     *
     * @param candidateSheetNames
     * @param rowCollectionClass
     * @param rowClass
     * @param sheetOption
     * @return
     * @throws PxlException
     */
    public static PxlImportSheetInfo makeImportSheetInfo(@NotEmpty final List<String> candidateSheetNames,
                                                         @NotNull final Class<?> rowCollectionClass,
                                                         @NotNull final Class<?> rowClass,
                                                         @Nullable final PxlImportSheetOption sheetOption) throws PxlException {

        if (!PxlClassUtils.isCollectionClass(rowCollectionClass)) {
            throw new PxlDataException(rowCollectionClass.getSimpleName() + "은 Collection 형태의 클래스가 아닙니다.");
        }

        final Class<?> rowConcreteCollectionClass = PxlClassUtils.getConcreteCollectionClass(rowCollectionClass);

        final List<String> sheetNames = candidateSheetNames.stream()
                .map(StringUtils::deleteWhitespace)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        if (PxlCollectionUtils.isEmpty(sheetNames)) {
            throw new PxlDataException(candidateSheetNames + " 시트 이름이 없거나 잘못되었습니다.");
        }

        final boolean importEnabled = true;

        final boolean importOverrideSuperClassSheet = Optional.ofNullable(sheetOption)
                .flatMap(option -> Optional.ofNullable(option.getImportOverrideSuperClassSheet()))
                .orElse(PxlConstant.DEFAULT_IMPORT_OVERRIDE_SUPER_CLASS_SHEET);

        final boolean importExcludeHiddenRows = Optional.ofNullable(sheetOption)
                .flatMap(option -> Optional.ofNullable(option.getImportExcludeHiddenRows()))
                .orElse(PxlConstant.DEFAULT_IMPORT_EXCLUDE_HIDDEN_ROWS);

        final boolean importExcludeHiddenColumns = Optional.ofNullable(sheetOption)
                .flatMap(option -> Optional.ofNullable(option.getImportExcludeHiddenColumns()))
                .orElse(PxlConstant.DEFAULT_IMPORT_EXCLUDE_HIDDEN_COLUMNS);

        final boolean importEachCellOfMergedRegion = Optional.ofNullable(sheetOption)
                .flatMap(option -> Optional.ofNullable(option.getImportEachCellOfMergedRegion()))
                .orElse(PxlConstant.DEFAULT_IMPORT_EACH_CELL_OF_MERGED_REGION);

        final int importTitleRowIndex = Optional.ofNullable(sheetOption)
                .flatMap(option -> Optional.ofNullable(option.getImportTitleRowIndex()))
                .orElse(PxlConstant.DEFAULT_IMPORT_TITLE_ROW_INDEX);
        if (importTitleRowIndex < 0) {
            throw new PxlDataException(candidateSheetNames + " 시트의 importTitleRowIndex의 값은 0이상이어야 합니다.");
        }

        final int importFirstDataRowIndex = Optional.ofNullable(sheetOption)
                .flatMap(option -> Optional.ofNullable(option.getImportFirstDataRowIndex()))
                .orElse(PxlConstant.DEFAULT_IMPORT_FIRST_DATA_ROW_INDEX);
        if (importFirstDataRowIndex < 0) {
            throw new PxlDataException(candidateSheetNames + " 시트의 importFirstDataRowIndex의 값은 0이상이어야 합니다.");
        }

        final int importLastDataRowIndex = Optional.ofNullable(sheetOption)
                .flatMap(option -> Optional.ofNullable(option.getImportLastDataRowIndex()))
                .orElse(PxlConstant.DEFAULT_IMPORT_LAST_DATA_ROW_INDEX);
        if (importLastDataRowIndex < 0) {
            throw new PxlDataException(candidateSheetNames + " 시트의 importLastDataRowIndex의 값은 0이상이어야 합니다.");
        }

        final int importFirstDataColumnIndex = Optional.ofNullable(sheetOption)
                .flatMap(option -> Optional.ofNullable(option.getImportFirstDataColumnIndex()))
                .orElse(PxlConstant.DEFAULT_IMPORT_FIRST_DATA_COLUMN_INDEX);
        if (importFirstDataColumnIndex < 0) {
            throw new PxlDataException(candidateSheetNames + " 시트의 importFirstDataColumnIndex의 값은 0이상이어야 합니다.");
        }

        final int importLastDataColumnIndex = Optional.ofNullable(sheetOption)
                .flatMap(option -> Optional.ofNullable(option.getImportLastDataColumnIndex()))
                .orElse(PxlConstant.DEFAULT_IMPORT_LAST_DATA_COLUMN_INDEX);
        if (importLastDataColumnIndex < 0) {
            throw new PxlDataException(candidateSheetNames + " 시트의 importLastDataColumnIndex의 값은 0이상이어야 합니다.");
        }

        if (importTitleRowIndex != PxlConstant.DEFAULT_IMPORT_TITLE_ROW_INDEX
                && importFirstDataRowIndex != PxlConstant.DEFAULT_IMPORT_FIRST_DATA_ROW_INDEX
                && importFirstDataRowIndex <= importTitleRowIndex) {
            throw new PxlDataException(candidateSheetNames + " 시트의 importFirstDataRowIndex의 값은 importTitleRowIndex의 값보다 커야 합니다.");
        }

        if (importFirstDataRowIndex != PxlConstant.DEFAULT_IMPORT_FIRST_DATA_ROW_INDEX
                && importLastDataRowIndex != PxlConstant.DEFAULT_IMPORT_LAST_DATA_ROW_INDEX
                && importLastDataRowIndex < importFirstDataRowIndex) {
            throw new PxlDataException(candidateSheetNames + " 시트의 importLastDataRowIndex의 값은 importFirstDataRowIndex의 값 이상이어야 합니다.");
        }

        if (importFirstDataColumnIndex != PxlConstant.DEFAULT_IMPORT_FIRST_DATA_COLUMN_INDEX
                && importLastDataColumnIndex != PxlConstant.DEFAULT_IMPORT_LAST_DATA_COLUMN_INDEX
                && importLastDataColumnIndex < importFirstDataColumnIndex) {
            throw new PxlDataException(candidateSheetNames + " 시트의 importLastDataColumnIndex의 값은 importFirstDataColumnIndex의 값 이상이어야 합니다.");
        }

        final List<PxlImportColumnOption> importColumnOptions = Optional.ofNullable(sheetOption)
                .map(option -> option.getImportColumnOptions())
                .orElseGet(ArrayList::new);

        final List<PxlImportColumnInfo> importColumnInfos = new ArrayList<>();

        return new PxlImportSheetInfo(
                null,                           // sheetField
                rowConcreteCollectionClass,     // rowCollectionClass
                rowClass,                       // rowClass
                sheetNames,                     // candidateSheetNames
                importEnabled,                  // importEnabled
                importOverrideSuperClassSheet,  // importOverrideSuperClassSheet
                importExcludeHiddenRows,        // importExcludeHiddenRows
                importExcludeHiddenColumns,     // importExcludeHiddenColumns
                importEachCellOfMergedRegion,   // importEachCellOfMergedRegion
                importTitleRowIndex,            // importTitleRowIndex
                importFirstDataRowIndex,        // importFirstDataRowIndex
                importLastDataRowIndex,         // importLastDataRowIndex
                importFirstDataColumnIndex,     // importFirstDataColumnIndex
                importLastDataColumnIndex,      // importLastDataColumnIndex
                importColumnOptions,            // importColumnOptions
                importColumnInfos               // importColumnInfos
        );
    }

    private static List<String> makeCandidateSheetNames(@Nullable final PxlImportSheetOption sheetOption,
                                                        @NotNull final PxlSheet sheetAnnotation,
                                                        @NotNull final Field sheetField) {

        List<String> candidateSheetNames;

        candidateSheetNames = PxlImportSheetOption.getImportSheetNames(sheetOption);
        if (PxlCollectionUtils.isNotEmpty(candidateSheetNames)) {
            return candidateSheetNames;
        }

        // @PxlSheet 어노테이션의 값, 즉 시트이름을 구한다.
        candidateSheetNames = Arrays.stream(sheetAnnotation.name())
                .map(StringUtils::deleteWhitespace)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        if (PxlCollectionUtils.isNotEmpty(candidateSheetNames)) {
            return candidateSheetNames;
        }

        candidateSheetNames = Arrays.asList(sheetField.getName());

        return candidateSheetNames;
    }

    public void addImportColumnInfos(final List<PxlImportColumnInfo> importColumnInfos) {

        this.importColumnInfos.addAll(importColumnInfos);
    }

    public void addImportColumnInfo(final PxlImportColumnInfo importColumnInfo) {

        this.importColumnInfos.add(importColumnInfo);
    }

}
