package com.logisteq.common.push.mqtt.service;

import com.logisteq.common.push.common.dto.PushEvent;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

public interface MqttService {

    /**
     * Subscribe
     *
     * @param topic
     * @param qos
     * @param mqttMessageListener
     */
    void subscribe(@NotBlank final String topic,
                   @NotNull final Integer qos,
                   @NotNull final MqttMessageListener mqttMessageListener);

    /**
     * Unsubscribe
     *
     * @param topic
     */
    void unsubscribe(@NotBlank final String topic);

    /**
     * 메시지 publish
     *
     * @param topic
     * @param pushPayload
     * @param retryable
     */
    void publish(@NotBlank final String topic,
                 @NotBlank final String pushPayload,
                 final boolean retryable);

    /**
     * 메시지 publish
     *
     * @param topic
     * @param pushEvent
     * @param pushMessageMap
     */
    void publish(@NotBlank final String topic,
                 @NotNull final PushEvent pushEvent,
                 @NotNull final Map<String, Object> pushMessageMap);

}
