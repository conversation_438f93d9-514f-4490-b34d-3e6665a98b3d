package com.logisteq.common.push.mqtt.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.logisteq.common.push.common.dto.PushMessage;
import com.logisteq.common.push.mqtt.constant.MqttApiUrl;
import com.logisteq.common.push.mqtt.service.MqttService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Slf4j
@Validated
@RestController
public class MqttController {

    private final ObjectMapper objectMapper;
    private final MqttService mqttService;

    @Autowired
    public MqttController(final ObjectMapper objectMapper,
                          final MqttService mqttService) {

        this.objectMapper = objectMapper;
        this.mqttService = mqttService;
    }

    /**
     * MQTT 메세지 전송
     *
     * @param pushMessage MQTT 요청 데이터
     */
    @PostMapping(value = MqttApiUrl.SEND_MESSAGE, consumes = MediaType.APPLICATION_JSON_VALUE)
    public void sendMessage(@NotNull @RequestBody @Valid final PushMessage pushMessage) throws JsonProcessingException {

        final String topic = pushMessage.getTopic();
        final String message = objectMapper.writeValueAsString(pushMessage.getMessage());
        mqttService.publish(topic, message, true);
    }

}
