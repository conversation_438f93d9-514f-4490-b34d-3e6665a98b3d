package com.logisteq.common.service;

import com.logisteq.common.feign.track.TrackServiceClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Validated
@Service
public class TrackAsyncService {

    private final TrackServiceClient trackServiceClient;

    @Autowired
    public TrackAsyncService(final TrackServiceClient trackServiceClient) {
        this.trackServiceClient = trackServiceClient;
    }

    @Async
    public CompletableFuture<Integer> deleteByRiderIdAsync(final List<Long> riderIdList) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("기사아이디 {}의 위치정보 삭제 요청", riderIdList);
                return trackServiceClient.deleteByRiderId(riderIdList);
            } catch (Exception e) {
                log.error("기사아이디 {}의 위치정보 삭제 요청 시 오류 발생: {}", riderIdList, e.toString());
                return null;
            }
        });
    }

    @Async
    public CompletableFuture<Integer> deleteByProjectIdAsync(final List<Long> projectIdList) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("프로젝트아이디 {}의 위치정보 삭제 요청", projectIdList);
                return trackServiceClient.deleteByProjectId(projectIdList);
            } catch (Exception e) {
                log.error("프로젝트아이디 {}의 위치정보 삭제 요청 시 오류 발생: {}", projectIdList, e.toString());
                return null;
            }
        });
    }

}
