<!DOCTYPE html>
<html lang="en" xmlns:sec="" xmlns:th="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="UTF-8">
    <title>Title</title>

    <link href="/css/owl.carousel.css" rel="stylesheet"/>
    <link href="/css/font-awesome.css" rel="stylesheet"/>

    <!-- Custom Style -->
    <link href="/css/main.css" rel="stylesheet"/>
    <!-- CSS --> 
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css">

    <!-- JS -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js"></script>

</head>
<body>

<div th:replace="bo/common/header :: header"></div>
<div class="container">

		<form th:action="@{/api/bo/userView}" method="post">
			<table class="table table-bordered" style="border: 1px solid red; height: 100px; margin: auto; text-align: center; font-size: 12px; font-family: 'Noto Sans KR', sans-serif;">
	            <tr>
	                <td><label class=" control-label">사용자 ID</label></td>
	                <td><input type="text" class="form-control" name="userId"  th:value="${user.userId}" readOnly></td>
	            </tr>
	            <tr>
	                <td><label class=" control-label">이름</label></td>
	                <td><input type="text" class="form-control" name="name" th:value="${user.name}" readOnly></td>
	            </tr>
	            <tr>
	                <td><label class=" control-label">이메일</label></td>
	                <td><input type="text" class="form-control" name="email" th:value="${user.email}" readOnly></td>
	            </tr>
				<tr>
					<td><label class=" control-label">전화번호</label></td>
					<td><input type="text" class="form-control" name="email" th:value="${user.phoneNumber}" readOnly></td>
				</tr>
			</table>

			<br>
			<button style="font-size: 12px; font-family: 'Noto Sans KR', sans-serif" class="btn btn-primary mx-3" type="button" th:onClick="'gogogo('+${user.userId}+')'">사용자 정보 수정</button>

			<button style="font-size: 12px; font-family: 'Noto Sans KR', sans-serif" class="btn btn-primary mx-3" type="button" th:onClick="'gogo('+${user.userId}+')'">비밀번호 변경</button>

			<button style="font-size: 12px; font-family: 'Noto Sans KR', sans-serif" class="btn btn-info mx-3" type="submit">계정 삭제</button>

		    <button style="font-size: 12px; font-family: 'Noto Sans KR', sans-serif" class="btn btn-warning mx-3" type="button" th:onClick="'goUserList('+${user.organizationId}+')'"> 목록으로</button>
		</form>

</div>


<br>
<div th:replace="bo/common/footer :: footer"></div>

</body>
<script>
	function gogogo(id) {
		window.location='/bo/userModify/'+id;
	}

	function gogo(id) {
		window.location='/bo/userChangePassword/'+id;
	}

	function goUserList(orgId){
		if( orgId ){
			window.location='/bo/userList/' + orgId;
		}else{
			window.location='/bo/userList/';
		}
	}
</script>
</html>