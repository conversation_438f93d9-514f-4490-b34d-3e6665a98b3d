<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="UTF-8">
    <title>Pagination</title>
</head>
<body>

<div th:fragment="pagination">
    <ul class="pagination" th:if="${totalPage}">
        <li th:class="${pageBlock} > 0 ?'page-item' : 'page-item disabled' ">
            <a class="page-link" th:href="@{${(pageBlock-1)*10}(type=${type},keyword=${keyword}) }">&lt;&lt;</a>
        </li>
        <li th:class="${page} > 0 ? 'page-item' : 'page-item disabled' ">
            <a class="page-link" th:href="@{${page-1}(type=${type},keyword=${keyword}) }">&lt;</a>
        </li>
        </th:block>

        <th:block th:each="index: ${#numbers.sequence(startPage, endPage)}">
            <li th:class="${page == index} ? 'page-item active' : 'page-item' ">
                <a class="page-link" th:href="@{${index}(type=${type},keyword=${keyword}) }"
                   th:text="${index}+1"/>
            </li>
        </th:block>

        <li th:class="${page+1 <totalPage } ?'page-item' : 'page-item disabled' ">
            <a class="page-link" th:href="@{${page+1}(type=${type},keyword=${keyword}) }">&gt;</a>
        </li>
        <li th:class="${ pageBlock +1 < totalPageBlock} ?'page-item' : 'page-item disabled' ">
            <a class="page-link"
               th:href="@{${(pageBlock+1)*10}( type=${type},keyword=${keyword}) }">&gt;&gt;</a>
        </li>
        </th:block>
    </ul>
</div>

</body>
</html>