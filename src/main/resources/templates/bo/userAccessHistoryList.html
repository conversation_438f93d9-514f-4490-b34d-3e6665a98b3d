<!DOCTYPE html>
<html lang="en" xmlns:sec="" xmlns:th="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8">
    <link href="/css/owl.carousel.css" rel="stylesheet"/>
    <link href="/css/font-awesome.css" rel="stylesheet"/>

    <!-- Custom Style -->
    <link href="/css/main.css" rel="stylesheet"/>
    <!-- CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css">

    <!-- JS -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js"></script>
</head>
<body>
<div th:replace="bo/common/header :: header"/>

<div class="container-fluid">
    <br>
    <br>
    <table class="table table-bordered"
           style="border: 1px solid red; height: 100px; margin: auto; text-align: center; font-size: 12px; font-family: 'Noto Sans KR', sans-serif;">
        <tr bgcolor="#e1e8d6">
            <td align="center">아이디</td>
            <td align="center">이름</td>
            <td align="center">구분</td>
            <td align="center">아이피</td>
            <td align="center">날짜</td>
        </tr>

        <!--				 th:onClick="'gogogo('+${user.userId}+')'"-->
        <tr th:each="content, state : ${contentList}">
            <td th:align="center" th:text="${content.userEmail}"></td>
            <td th:align="center" th:text="${content.userName}"></td>
            <td th:align="center" th:text="${content.kindOfMethod}"></td>
            <td th:align="center" th:text="${content.ip}"></td>
            <td th:align="center" th:text="${content.dateTime}"></td>
        </tr>
    </table>
</div>
<br>
<nav aria-label="Page navigation example">
    <!--표에 사용될 변수값 초기화 -->
    <ul class="pagination justify-content-center">

        <li class="page-item">
            <a class="page-link" th:href="@{/bo/userAccessHistoryList(page=1)}"  aria-label="First">
                <span aria-hidden="true">&laquo;</span>
            </a>
        </li>

        <li class="page-item" th:class="${isFirst} ? 'disabled'">
            <a class="page-link" th:href="${isFirst} ? '#':@{/bo/userAccessHistoryList(page=${start-1})}" aria-label="Previous">
                <span aria-hidden="true">&lt;</span>
            </a>
        </li>

        <th:block th:each="index: ${#numbers.sequence(start, last)}">
            <li th:class="${page == index} ? 'page-item active' : 'page-item' ">
                <a class="page-link" th:text="${index}" th:href="@{/bo/userAccessHistoryList(page=${index})}"/>
            </li>
        </th:block>


        <li class="page-item" th:class="${isLast} ? 'disabled'">
            <a class="page-link" th:href="${isLast} ? '#':@{/bo/userAccessHistoryList(page=${last+1})}" aria-label="Next">
                <span aria-hidden="true">&gt;</span>
            </a>
        </li>

        <li class="page-item">
            <a class="page-link" th:href="@{/bo/userAccessHistoryList(page=${totalPages})}"  aria-label="Last">
                <span aria-hidden="true">&raquo;</span>
            </a>
        </li>
    </ul>
</nav>

<div th:replace="bo/common/footer :: footer"/>
</body>

<script>
    function goIndex() {
        window.location = '/bo/userAccessHistoryList'
    }
</script>
</html>