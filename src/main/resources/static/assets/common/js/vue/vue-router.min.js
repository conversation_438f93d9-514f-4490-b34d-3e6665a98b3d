!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t=t||self).VueRouter=e()}(this,function(){"use strict";function t(t,e){if(!t)throw new Error("[vue-router] "+e)}function e(t,e){t||"undefined"!=typeof console&&console.warn("[vue-router] "+e)}function n(t){return Object.prototype.toString.call(t).indexOf("Error")>-1}function r(t,e){return e instanceof t||e&&(e.name===t.name||e._name===t._name)}function o(t,e){for(var n in e)t[n]=e[n];return t}var i={name:"RouterView",functional:!0,props:{name:{type:String,default:"default"}},render:function(t,e){var n=e.props,r=e.children,i=e.parent,u=e.data;u.routerView=!0;for(var c=i.$createElement,s=n.name,p=i.$route,f=i._routerViewCache||(i._routerViewCache={}),h=0,l=!1;i&&i._routerRoot!==i;){var d=i.$vnode?i.$vnode.data:{};d.routerView&&h++,d.keepAlive&&i._directInactive&&i._inactive&&(l=!0),i=i.$parent}if(u.routerViewDepth=h,l){var v=f[s],y=v&&v.component;return y?(v.configProps&&a(y,u,v.route,v.configProps),c(y,u,r)):c()}var m=p.matched[h],g=m&&m.components[s];if(!m||!g)return f[s]=null,c();f[s]={component:g},u.registerRouteInstance=function(t,e){var n=m.instances[s];(e&&n!==t||!e&&n===t)&&(m.instances[s]=e)},(u.hook||(u.hook={})).prepatch=function(t,e){m.instances[s]=e.componentInstance},u.hook.init=function(t){t.data.keepAlive&&t.componentInstance&&t.componentInstance!==m.instances[s]&&(m.instances[s]=t.componentInstance)};var b=m.props&&m.props[s];return b&&(o(f[s],{route:p,configProps:b}),a(g,u,p,b)),c(g,u,r)}};function a(t,n,r,i){var a=n.props=function(t,n){switch(typeof n){case"undefined":return;case"object":return n;case"function":return n(t);case"boolean":return n?t.params:void 0;default:e(!1,'props in "'+t.path+'" is a '+typeof n+", expecting an object, function or boolean.")}}(r,i);if(a){a=n.props=o({},a);var u=n.attrs=n.attrs||{};for(var c in a)t.props&&c in t.props||(u[c]=a[c],delete a[c])}}var u=/[!'()*]/g,c=function(t){return"%"+t.charCodeAt(0).toString(16)},s=/%2C/g,p=function(t){return encodeURIComponent(t).replace(u,c).replace(s,",")},f=decodeURIComponent;function h(t){var e={};return(t=t.trim().replace(/^(\?|#|&)/,""))?(t.split("&").forEach(function(t){var n=t.replace(/\+/g," ").split("="),r=f(n.shift()),o=n.length>0?f(n.join("=")):null;void 0===e[r]?e[r]=o:Array.isArray(e[r])?e[r].push(o):e[r]=[e[r],o]}),e):e}function l(t){var e=t?Object.keys(t).map(function(e){var n=t[e];if(void 0===n)return"";if(null===n)return p(e);if(Array.isArray(n)){var r=[];return n.forEach(function(t){void 0!==t&&(null===t?r.push(p(e)):r.push(p(e)+"="+p(t)))}),r.join("&")}return p(e)+"="+p(n)}).filter(function(t){return t.length>0}).join("&"):null;return e?"?"+e:""}var d=/\/?$/;function v(t,e,n,r){var o=r&&r.options.stringifyQuery,i=e.query||{};try{i=y(i)}catch(t){}var a={name:e.name||t&&t.name,meta:t&&t.meta||{},path:e.path||"/",hash:e.hash||"",query:i,params:e.params||{},fullPath:b(e,o),matched:t?g(t):[]};return n&&(a.redirectedFrom=b(n,o)),Object.freeze(a)}function y(t){if(Array.isArray(t))return t.map(y);if(t&&"object"==typeof t){var e={};for(var n in t)e[n]=y(t[n]);return e}return t}var m=v(null,{path:"/"});function g(t){for(var e=[];t;)e.unshift(t),t=t.parent;return e}function b(t,e){var n=t.path,r=t.query;void 0===r&&(r={});var o=t.hash;return void 0===o&&(o=""),(n||"/")+(e||l)(r)+o}function w(t,e){return e===m?t===e:!!e&&(t.path&&e.path?t.path.replace(d,"")===e.path.replace(d,"")&&t.hash===e.hash&&x(t.query,e.query):!(!t.name||!e.name)&&(t.name===e.name&&t.hash===e.hash&&x(t.query,e.query)&&x(t.params,e.params)))}function x(t,e){if(void 0===t&&(t={}),void 0===e&&(e={}),!t||!e)return t===e;var n=Object.keys(t),r=Object.keys(e);return n.length===r.length&&n.every(function(n){var r=t[n],o=e[n];return"object"==typeof r&&"object"==typeof o?x(r,o):String(r)===String(o)})}function k(t,e,n){var r=t.charAt(0);if("/"===r)return t;if("?"===r||"#"===r)return e+t;var o=e.split("/");n&&o[o.length-1]||o.pop();for(var i=t.replace(/^\//,"").split("/"),a=0;a<i.length;a++){var u=i[a];".."===u?o.pop():"."!==u&&o.push(u)}return""!==o[0]&&o.unshift(""),o.join("/")}function R(t){return t.replace(/\/\//g,"/")}var E=Array.isArray||function(t){return"[object Array]"==Object.prototype.toString.call(t)},O=V,_=$,A=function(t,e){return P($(t,e))},j=P,C=M,S=new RegExp(["(\\\\.)","([\\/.])?(?:(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?|(\\*))"].join("|"),"g");function $(t,e){for(var n,r=[],o=0,i=0,a="",u=e&&e.delimiter||"/";null!=(n=S.exec(t));){var c=n[0],s=n[1],p=n.index;if(a+=t.slice(i,p),i=p+c.length,s)a+=s[1];else{var f=t[i],h=n[2],l=n[3],d=n[4],v=n[5],y=n[6],m=n[7];a&&(r.push(a),a="");var g=null!=h&&null!=f&&f!==h,b="+"===y||"*"===y,w="?"===y||"*"===y,x=n[2]||u,k=d||v;r.push({name:l||o++,prefix:h||"",delimiter:x,optional:w,repeat:b,partial:g,asterisk:!!m,pattern:k?q(k):m?".*":"[^"+L(x)+"]+?"})}}return i<t.length&&(a+=t.substr(i)),a&&r.push(a),r}function T(t){return encodeURI(t).replace(/[\/?#]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()})}function P(t){for(var e=new Array(t.length),n=0;n<t.length;n++)"object"==typeof t[n]&&(e[n]=new RegExp("^(?:"+t[n].pattern+")$"));return function(n,r){for(var o="",i=n||{},a=(r||{}).pretty?T:encodeURIComponent,u=0;u<t.length;u++){var c=t[u];if("string"!=typeof c){var s,p=i[c.name];if(null==p){if(c.optional){c.partial&&(o+=c.prefix);continue}throw new TypeError('Expected "'+c.name+'" to be defined')}if(E(p)){if(!c.repeat)throw new TypeError('Expected "'+c.name+'" to not repeat, but received `'+JSON.stringify(p)+"`");if(0===p.length){if(c.optional)continue;throw new TypeError('Expected "'+c.name+'" to not be empty')}for(var f=0;f<p.length;f++){if(s=a(p[f]),!e[u].test(s))throw new TypeError('Expected all "'+c.name+'" to match "'+c.pattern+'", but received `'+JSON.stringify(s)+"`");o+=(0===f?c.prefix:c.delimiter)+s}}else{if(s=c.asterisk?encodeURI(p).replace(/[?#]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()}):a(p),!e[u].test(s))throw new TypeError('Expected "'+c.name+'" to match "'+c.pattern+'", but received "'+s+'"');o+=c.prefix+s}}else o+=c}return o}}function L(t){return t.replace(/([.+*?=^!:${}()[\]|\/\\])/g,"\\$1")}function q(t){return t.replace(/([=!:$\/()])/g,"\\$1")}function U(t,e){return t.keys=e,t}function I(t){return t.sensitive?"":"i"}function M(t,e,n){E(e)||(n=e||n,e=[]);for(var r=(n=n||{}).strict,o=!1!==n.end,i="",a=0;a<t.length;a++){var u=t[a];if("string"==typeof u)i+=L(u);else{var c=L(u.prefix),s="(?:"+u.pattern+")";e.push(u),u.repeat&&(s+="(?:"+c+s+")*"),i+=s=u.optional?u.partial?c+"("+s+")?":"(?:"+c+"("+s+"))?":c+"("+s+")"}}var p=L(n.delimiter||"/"),f=i.slice(-p.length)===p;return r||(i=(f?i.slice(0,-p.length):i)+"(?:"+p+"(?=$))?"),i+=o?"$":r&&f?"":"(?="+p+"|$)",U(new RegExp("^"+i,I(n)),e)}function V(t,e,n){return E(e)||(n=e||n,e=[]),n=n||{},t instanceof RegExp?function(t,e){var n=t.source.match(/\((?!\?)/g);if(n)for(var r=0;r<n.length;r++)e.push({name:r,prefix:null,delimiter:null,optional:!1,repeat:!1,partial:!1,asterisk:!1,pattern:null});return U(t,e)}(t,e):E(t)?function(t,e,n){for(var r=[],o=0;o<t.length;o++)r.push(V(t[o],e,n).source);return U(new RegExp("(?:"+r.join("|")+")",I(n)),e)}(t,e,n):function(t,e,n){return M($(t,n),e,n)}(t,e,n)}O.parse=_,O.compile=A,O.tokensToFunction=j,O.tokensToRegExp=C;var B=Object.create(null);function N(t,n,r){n=n||{};try{var o=B[t]||(B[t]=O.compile(t));return n.pathMatch&&(n[0]=n.pathMatch),o(n,{pretty:!0})}catch(t){return e("string"==typeof n.pathMatch,"missing param for "+r+": "+t.message),""}finally{delete n[0]}}function D(t,n,r,i){var a="string"==typeof t?{path:t}:t;if(a._normalized)return a;if(a.name){var u=(a=o({},t)).params;return u&&"object"==typeof u&&(a.params=o({},u)),a}if(!a.path&&a.params&&n){(a=o({},a))._normalized=!0;var c=o(o({},n.params),a.params);if(n.name)a.name=n.name,a.params=c;else if(n.matched.length){var s=n.matched[n.matched.length-1].path;a.path=N(s,c,"path "+n.path)}else e(!1,"relative params navigation requires a current route.");return a}var p=function(t){var e="",n="",r=t.indexOf("#");r>=0&&(e=t.slice(r),t=t.slice(0,r));var o=t.indexOf("?");return o>=0&&(n=t.slice(o+1),t=t.slice(0,o)),{path:t,query:n,hash:e}}(a.path||""),f=n&&n.path||"/",l=p.path?k(p.path,f,r||a.append):f,d=function(t,n,r){void 0===n&&(n={});var o,i=r||h;try{o=i(t||"")}catch(t){e(!1,t.message),o={}}for(var a in n)o[a]=n[a];return o}(p.query,a.query,i&&i.options.parseQuery),v=a.hash||p.hash;return v&&"#"!==v.charAt(0)&&(v="#"+v),{_normalized:!0,path:l,query:d,hash:v}}var F,H=[String,Object],z=[String,Array],J=function(){},K={name:"RouterLink",props:{to:{type:H,required:!0},tag:{type:String,default:"a"},exact:Boolean,append:Boolean,replace:Boolean,activeClass:String,exactActiveClass:String,event:{type:z,default:"click"}},render:function(t){var n=this,r=this.$router,i=this.$route,a=r.resolve(this.to,i,this.append),u=a.location,c=a.route,s=a.href,p={},f=r.options.linkActiveClass,h=r.options.linkExactActiveClass,l=null==f?"router-link-active":f,y=null==h?"router-link-exact-active":h,m=null==this.activeClass?l:this.activeClass,g=null==this.exactActiveClass?y:this.exactActiveClass,b=c.redirectedFrom?v(null,D(c.redirectedFrom),null,r):c;p[g]=w(i,b),p[m]=this.exact?p[g]:function(t,e){return 0===t.path.replace(d,"/").indexOf(e.path.replace(d,"/"))&&(!e.hash||t.hash===e.hash)&&function(t,e){for(var n in e)if(!(n in t))return!1;return!0}(t.query,e.query)}(i,b);var x=function(t){W(t)&&(n.replace?r.replace(u,J):r.push(u,J))},k={click:W};Array.isArray(this.event)?this.event.forEach(function(t){k[t]=x}):k[this.event]=x;var R={class:p},E=!this.$scopedSlots.$hasNormal&&this.$scopedSlots.default&&this.$scopedSlots.default({href:s,route:c,navigate:x,isActive:p[m],isExactActive:p[g]});if(E){if(1===E.length)return E[0];if(E.length>1||!E.length)return e(!1,'RouterLink with to="'+this.to+"\" is trying to use a scoped slot but it didn't provide exactly one child. Wrapping the content with a span element."),0===E.length?t():t("span",{},E)}if("a"===this.tag)R.on=k,R.attrs={href:s};else{var O=function t(e){if(e)for(var n,r=0;r<e.length;r++){if("a"===(n=e[r]).tag)return n;if(n.children&&(n=t(n.children)))return n}}(this.$slots.default);if(O){O.isStatic=!1;var _=O.data=o({},O.data);for(var A in _.on=_.on||{},_.on){var j=_.on[A];A in k&&(_.on[A]=Array.isArray(j)?j:[j])}for(var C in k)C in _.on?_.on[C].push(k[C]):_.on[C]=x;(O.data.attrs=o({},O.data.attrs)).href=s}else R.on=k}return t(this.tag,R,this.$slots.default)}};function W(t){if(!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey||t.defaultPrevented||void 0!==t.button&&0!==t.button)){if(t.currentTarget&&t.currentTarget.getAttribute){var e=t.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(e))return}return t.preventDefault&&t.preventDefault(),!0}}function Y(t){if(!Y.installed||F!==t){Y.installed=!0,F=t;var e=function(t){return void 0!==t},n=function(t,n){var r=t.$options._parentVnode;e(r)&&e(r=r.data)&&e(r=r.registerRouteInstance)&&r(t,n)};t.mixin({beforeCreate:function(){e(this.$options.router)?(this._routerRoot=this,this._router=this.$options.router,this._router.init(this),t.util.defineReactive(this,"_route",this._router.history.current)):this._routerRoot=this.$parent&&this.$parent._routerRoot||this,n(this,this)},destroyed:function(){n(this)}}),Object.defineProperty(t.prototype,"$router",{get:function(){return this._routerRoot._router}}),Object.defineProperty(t.prototype,"$route",{get:function(){return this._routerRoot._route}}),t.component("RouterView",i),t.component("RouterLink",K);var r=t.config.optionMergeStrategies;r.beforeRouteEnter=r.beforeRouteLeave=r.beforeRouteUpdate=r.created}}var Q="undefined"!=typeof window;function X(n,r,o,i){var a=r||[],u=o||Object.create(null),c=i||Object.create(null);n.forEach(function(n){!function n(r,o,i,a,u,c){var s=a.path;var p=a.name;t(null!=s,'"path" is required in a route configuration.'),t("string"!=typeof a.component,'route config "component" for path: '+String(s||p)+" cannot be a string id. Use an actual component instead.");var f=a.pathToRegexpOptions||{};var h=function(t,e,n){n||(t=t.replace(/\/$/,""));if("/"===t[0])return t;if(null==e)return t;return R(e.path+"/"+t)}(s,u,f.strict);"boolean"==typeof a.caseSensitive&&(f.sensitive=a.caseSensitive);var l={path:h,regex:G(h,f),components:a.components||{default:a.component},instances:{},name:p,parent:u,matchAs:c,redirect:a.redirect,beforeEnter:a.beforeEnter,meta:a.meta||{},props:null==a.props?{}:a.components?a.props:{default:a.props}};a.children&&(a.name&&!a.redirect&&a.children.some(function(t){return/^\/?$/.test(t.path)})&&e(!1,"Named Route '"+a.name+"' has a default child route. When navigating to this named route (:to=\"{name: '"+a.name+"'\"), the default child route will not be rendered. Remove the name from this route and use the name of the default child route for named links instead."),a.children.forEach(function(t){var e=c?R(c+"/"+t.path):void 0;n(r,o,i,t,l,e)}));o[l.path]||(r.push(l.path),o[l.path]=l);if(void 0!==a.alias)for(var d=Array.isArray(a.alias)?a.alias:[a.alias],v=0;v<d.length;++v){var y=d[v];if(y!==s){var m={path:y,children:a.children};n(r,o,i,m,u,l.path||"/")}else e(!1,'Found an alias with the same value as the path: "'+s+'". You have to remove that alias. It will be ignored in development.')}p&&(i[p]?c||e(!1,'Duplicate named routes definition: { name: "'+p+'", path: "'+l.path+'" }'):i[p]=l)}(a,u,c,n)});for(var s=0,p=a.length;s<p;s++)"*"===a[s]&&(a.push(a.splice(s,1)[0]),p--,s--);var f=a.filter(function(t){return t&&"*"!==t.charAt(0)&&"/"!==t.charAt(0)});f.length>0&&e(!1,"Non-nested routes must include a leading slash character. Fix the following routes: \n"+f.map(function(t){return"- "+t}).join("\n"));return{pathList:a,pathMap:u,nameMap:c}}function G(t,n){var r=O(t,[],n),o=Object.create(null);return r.keys.forEach(function(n){e(!o[n.name],'Duplicate param keys in route with path: "'+t+'"'),o[n.name]=!0}),r}function Z(n,r){var o=X(n),i=o.pathList,a=o.pathMap,u=o.nameMap;function c(t,n,o){var c=D(t,n,!1,r),s=c.name;if(s){var f=u[s];if(e(f,"Route with name '"+s+"' does not exist"),!f)return p(null,c);var h=f.regex.keys.filter(function(t){return!t.optional}).map(function(t){return t.name});if("object"!=typeof c.params&&(c.params={}),n&&"object"==typeof n.params)for(var l in n.params)!(l in c.params)&&h.indexOf(l)>-1&&(c.params[l]=n.params[l]);return c.path=N(f.path,c.params,'named route "'+s+'"'),p(f,c,o)}if(c.path){c.params={};for(var d=0;d<i.length;d++){var v=i[d],y=a[v];if(tt(y.regex,c.path,c.params))return p(y,c,o)}}return p(null,c)}function s(n,o){var i=n.redirect,a="function"==typeof i?i(v(n,o,null,r)):i;if("string"==typeof a&&(a={path:a}),!a||"object"!=typeof a)return e(!1,"invalid redirect option: "+JSON.stringify(a)),p(null,o);var s=a,f=s.name,h=s.path,l=o.query,d=o.hash,y=o.params;if(l=s.hasOwnProperty("query")?s.query:l,d=s.hasOwnProperty("hash")?s.hash:d,y=s.hasOwnProperty("params")?s.params:y,f)return t(u[f],'redirect failed: named route "'+f+'" not found.'),c({_normalized:!0,name:f,query:l,hash:d,params:y},void 0,o);if(h){var m=function(t,e){return k(t,e.parent?e.parent.path:"/",!0)}(h,n);return c({_normalized:!0,path:N(m,y,'redirect route with path "'+m+'"'),query:l,hash:d},void 0,o)}return e(!1,"invalid redirect option: "+JSON.stringify(a)),p(null,o)}function p(t,e,n){return t&&t.redirect?s(t,n||e):t&&t.matchAs?function(t,e,n){var r=c({_normalized:!0,path:N(n,e.params,'aliased route with path "'+n+'"')});if(r){var o=r.matched,i=o[o.length-1];return e.params=r.params,p(i,e)}return p(null,e)}(0,e,t.matchAs):v(t,e,n,r)}return{match:c,addRoutes:function(t){X(t,i,a,u)}}}function tt(t,e,n){var r=e.match(t);if(!r)return!1;if(!n)return!0;for(var o=1,i=r.length;o<i;++o){var a=t.keys[o-1],u="string"==typeof r[o]?decodeURIComponent(r[o]):r[o];a&&(n[a.name||"pathMatch"]=u)}return!0}var et=Q&&window.performance&&window.performance.now?window.performance:Date;function nt(){return et.now().toFixed(3)}var rt=nt();function ot(){return rt}function it(t){return rt=t}var at=Object.create(null);function ut(){var t=window.location.protocol+"//"+window.location.host,e=window.location.href.replace(t,"");window.history.replaceState({key:ot()},"",e),window.addEventListener("popstate",function(t){st(),t.state&&t.state.key&&it(t.state.key)})}function ct(e,n,r,o){if(e.app){var i=e.options.scrollBehavior;i&&(t("function"==typeof i,"scrollBehavior must be a function"),e.app.$nextTick(function(){var a=function(){var t=ot();if(t)return at[t]}(),u=i.call(e,n,r,o?a:null);u&&("function"==typeof u.then?u.then(function(t){dt(t,a)}).catch(function(e){t(!1,e.toString())}):dt(u,a))}))}}function st(){var t=ot();t&&(at[t]={x:window.pageXOffset,y:window.pageYOffset})}function pt(t){return ht(t.x)||ht(t.y)}function ft(t){return{x:ht(t.x)?t.x:window.pageXOffset,y:ht(t.y)?t.y:window.pageYOffset}}function ht(t){return"number"==typeof t}var lt=/^#\d/;function dt(t,e){var n,r="object"==typeof t;if(r&&"string"==typeof t.selector){var o=lt.test(t.selector)?document.getElementById(t.selector.slice(1)):document.querySelector(t.selector);if(o){var i=t.offset&&"object"==typeof t.offset?t.offset:{};e=function(t,e){var n=document.documentElement.getBoundingClientRect(),r=t.getBoundingClientRect();return{x:r.left-n.left-e.x,y:r.top-n.top-e.y}}(o,i={x:ht((n=i).x)?n.x:0,y:ht(n.y)?n.y:0})}else pt(t)&&(e=ft(t))}else r&&pt(t)&&(e=ft(t));e&&window.scrollTo(e.x,e.y)}var vt,yt=Q&&((-1===(vt=window.navigator.userAgent).indexOf("Android 2.")&&-1===vt.indexOf("Android 4.0")||-1===vt.indexOf("Mobile Safari")||-1!==vt.indexOf("Chrome")||-1!==vt.indexOf("Windows Phone"))&&window.history&&"pushState"in window.history);function mt(t,e){st();var n=window.history;try{if(e){var r=o({},n.state);r.key=ot(),n.replaceState(r,"",t)}else n.pushState({key:it(nt())},"",t)}catch(n){window.location[e?"replace":"assign"](t)}}function gt(t){mt(t,!0)}function bt(t,e,n){var r=function(o){o>=t.length?n():t[o]?e(t[o],function(){r(o+1)}):r(o+1)};r(0)}function wt(t){return function(r,o,i){var a=!1,u=0,c=null;xt(t,function(t,r,o,s){if("function"==typeof t&&void 0===t.cid){a=!0,u++;var p,f=Et(function(e){var n;((n=e).__esModule||Rt&&"Module"===n[Symbol.toStringTag])&&(e=e.default),t.resolved="function"==typeof e?e:F.extend(e),o.components[s]=e,--u<=0&&i()}),h=Et(function(t){var r="Failed to resolve async component "+s+": "+t;e(!1,r),c||(c=n(t)?t:new Error(r),i(c))});try{p=t(f,h)}catch(t){h(t)}if(p)if("function"==typeof p.then)p.then(f,h);else{var l=p.component;l&&"function"==typeof l.then&&l.then(f,h)}}}),a||i()}}function xt(t,e){return kt(t.map(function(t){return Object.keys(t.components).map(function(n){return e(t.components[n],t.instances[n],t,n)})}))}function kt(t){return Array.prototype.concat.apply([],t)}var Rt="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag;function Et(t){var e=!1;return function(){for(var n=[],r=arguments.length;r--;)n[r]=arguments[r];if(!e)return e=!0,t.apply(this,n)}}var Ot=function(t){function e(e){t.call(this),this.name=this._name="NavigationDuplicated",this.message='Navigating to current location ("'+e.fullPath+'") is not allowed',Object.defineProperty(this,"stack",{value:(new t).stack,writable:!0,configurable:!0})}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e}(Error);Ot._name="NavigationDuplicated";var _t=function(t,e){this.router=t,this.base=function(t){if(!t)if(Q){var e=document.querySelector("base");t=(t=e&&e.getAttribute("href")||"/").replace(/^https?:\/\/[^\/]+/,"")}else t="/";"/"!==t.charAt(0)&&(t="/"+t);return t.replace(/\/$/,"")}(e),this.current=m,this.pending=null,this.ready=!1,this.readyCbs=[],this.readyErrorCbs=[],this.errorCbs=[]};function At(t,e,n,r){var o=xt(t,function(t,r,o,i){var a=function(t,e){"function"!=typeof t&&(t=F.extend(t));return t.options[e]}(t,e);if(a)return Array.isArray(a)?a.map(function(t){return n(t,r,o,i)}):n(a,r,o,i)});return kt(r?o.reverse():o)}function jt(t,e){if(e)return function(){return t.apply(e,arguments)}}_t.prototype.listen=function(t){this.cb=t},_t.prototype.onReady=function(t,e){this.ready?t():(this.readyCbs.push(t),e&&this.readyErrorCbs.push(e))},_t.prototype.onError=function(t){this.errorCbs.push(t)},_t.prototype.transitionTo=function(t,e,n){var r=this,o=this.router.match(t,this.current);this.confirmTransition(o,function(){r.updateRoute(o),e&&e(o),r.ensureURL(),r.ready||(r.ready=!0,r.readyCbs.forEach(function(t){t(o)}))},function(t){n&&n(t),t&&!r.ready&&(r.ready=!0,r.readyErrorCbs.forEach(function(e){e(t)}))})},_t.prototype.confirmTransition=function(t,o,i){var a=this,u=this.current,c=function(t){!r(Ot,t)&&n(t)&&(a.errorCbs.length?a.errorCbs.forEach(function(e){e(t)}):(e(!1,"uncaught error during route navigation:"),console.error(t))),i&&i(t)};if(w(t,u)&&t.matched.length===u.matched.length)return this.ensureURL(),c(new Ot(t));var s=function(t,e){var n,r=Math.max(t.length,e.length);for(n=0;n<r&&t[n]===e[n];n++);return{updated:e.slice(0,n),activated:e.slice(n),deactivated:t.slice(n)}}(this.current.matched,t.matched),p=s.updated,f=s.deactivated,h=s.activated,l=[].concat(function(t){return At(t,"beforeRouteLeave",jt,!0)}(f),this.router.beforeHooks,function(t){return At(t,"beforeRouteUpdate",jt)}(p),h.map(function(t){return t.beforeEnter}),wt(h));this.pending=t;var d=function(e,r){if(a.pending!==t)return c();try{e(t,u,function(t){!1===t||n(t)?(a.ensureURL(!0),c(t)):"string"==typeof t||"object"==typeof t&&("string"==typeof t.path||"string"==typeof t.name)?(c(),"object"==typeof t&&t.replace?a.replace(t):a.push(t)):r(t)})}catch(t){c(t)}};bt(l,d,function(){var e=[];bt(function(t,e,n){return At(t,"beforeRouteEnter",function(t,r,o,i){return function(t,e,n,r,o){return function(i,a,u){return t(i,a,function(t){"function"==typeof t&&r.push(function(){!function t(e,n,r,o){n[r]&&!n[r]._isBeingDestroyed?e(n[r]):o()&&setTimeout(function(){t(e,n,r,o)},16)}(t,e.instances,n,o)}),u(t)})}}(t,o,i,e,n)})}(h,e,function(){return a.current===t}).concat(a.router.resolveHooks),d,function(){if(a.pending!==t)return c();a.pending=null,o(t),a.router.app&&a.router.app.$nextTick(function(){e.forEach(function(t){t()})})})})},_t.prototype.updateRoute=function(t){var e=this.current;this.current=t,this.cb&&this.cb(t),this.router.afterHooks.forEach(function(n){n&&n(t,e)})};var Ct=function(t){function e(e,n){var r=this;t.call(this,e,n);var o=e.options.scrollBehavior,i=yt&&o;i&&ut();var a=St(this.base);window.addEventListener("popstate",function(t){var n=r.current,o=St(r.base);r.current===m&&o===a||r.transitionTo(o,function(t){i&&ct(e,t,n,!0)})})}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.go=function(t){window.history.go(t)},e.prototype.push=function(t,e,n){var r=this,o=this.current;this.transitionTo(t,function(t){mt(R(r.base+t.fullPath)),ct(r.router,t,o,!1),e&&e(t)},n)},e.prototype.replace=function(t,e,n){var r=this,o=this.current;this.transitionTo(t,function(t){gt(R(r.base+t.fullPath)),ct(r.router,t,o,!1),e&&e(t)},n)},e.prototype.ensureURL=function(t){if(St(this.base)!==this.current.fullPath){var e=R(this.base+this.current.fullPath);t?mt(e):gt(e)}},e.prototype.getCurrentLocation=function(){return St(this.base)},e}(_t);function St(t){var e=decodeURI(window.location.pathname);return t&&0===e.indexOf(t)&&(e=e.slice(t.length)),(e||"/")+window.location.search+window.location.hash}var $t=function(t){function e(e,n,r){t.call(this,e,n),r&&function(t){var e=St(t);if(!/^\/#/.test(e))return window.location.replace(R(t+"/#"+e)),!0}(this.base)||Tt()}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.setupListeners=function(){var t=this,e=this.router.options.scrollBehavior,n=yt&&e;n&&ut(),window.addEventListener(yt?"popstate":"hashchange",function(){var e=t.current;Tt()&&t.transitionTo(Pt(),function(r){n&&ct(t.router,r,e,!0),yt||Ut(r.fullPath)})})},e.prototype.push=function(t,e,n){var r=this,o=this.current;this.transitionTo(t,function(t){qt(t.fullPath),ct(r.router,t,o,!1),e&&e(t)},n)},e.prototype.replace=function(t,e,n){var r=this,o=this.current;this.transitionTo(t,function(t){Ut(t.fullPath),ct(r.router,t,o,!1),e&&e(t)},n)},e.prototype.go=function(t){window.history.go(t)},e.prototype.ensureURL=function(t){var e=this.current.fullPath;Pt()!==e&&(t?qt(e):Ut(e))},e.prototype.getCurrentLocation=function(){return Pt()},e}(_t);function Tt(){var t=Pt();return"/"===t.charAt(0)||(Ut("/"+t),!1)}function Pt(){var t=window.location.href,e=t.indexOf("#");if(e<0)return"";var n=(t=t.slice(e+1)).indexOf("?");if(n<0){var r=t.indexOf("#");t=r>-1?decodeURI(t.slice(0,r))+t.slice(r):decodeURI(t)}else t=decodeURI(t.slice(0,n))+t.slice(n);return t}function Lt(t){var e=window.location.href,n=e.indexOf("#");return(n>=0?e.slice(0,n):e)+"#"+t}function qt(t){yt?mt(Lt(t)):window.location.hash=t}function Ut(t){yt?gt(Lt(t)):window.location.replace(Lt(t))}var It=function(t){function e(e,n){t.call(this,e,n),this.stack=[],this.index=-1}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.push=function(t,e,n){var r=this;this.transitionTo(t,function(t){r.stack=r.stack.slice(0,r.index+1).concat(t),r.index++,e&&e(t)},n)},e.prototype.replace=function(t,e,n){var r=this;this.transitionTo(t,function(t){r.stack=r.stack.slice(0,r.index).concat(t),e&&e(t)},n)},e.prototype.go=function(t){var e=this,n=this.index+t;if(!(n<0||n>=this.stack.length)){var o=this.stack[n];this.confirmTransition(o,function(){e.index=n,e.updateRoute(o)},function(t){r(Ot,t)&&(e.index=n)})}},e.prototype.getCurrentLocation=function(){var t=this.stack[this.stack.length-1];return t?t.fullPath:"/"},e.prototype.ensureURL=function(){},e}(_t),Mt=function(e){void 0===e&&(e={}),this.app=null,this.apps=[],this.options=e,this.beforeHooks=[],this.resolveHooks=[],this.afterHooks=[],this.matcher=Z(e.routes||[],this);var n=e.mode||"hash";switch(this.fallback="history"===n&&!yt&&!1!==e.fallback,this.fallback&&(n="hash"),Q||(n="abstract"),this.mode=n,n){case"history":this.history=new Ct(this,e.base);break;case"hash":this.history=new $t(this,e.base,this.fallback);break;case"abstract":this.history=new It(this,e.base);break;default:t(!1,"invalid mode: "+n)}},Vt={currentRoute:{configurable:!0}};function Bt(t,e){return t.push(e),function(){var n=t.indexOf(e);n>-1&&t.splice(n,1)}}return Mt.prototype.match=function(t,e,n){return this.matcher.match(t,e,n)},Vt.currentRoute.get=function(){return this.history&&this.history.current},Mt.prototype.init=function(e){var n=this;if(t(Y.installed,"not installed. Make sure to call `Vue.use(VueRouter)` before creating root instance."),this.apps.push(e),e.$once("hook:destroyed",function(){var t=n.apps.indexOf(e);t>-1&&n.apps.splice(t,1),n.app===e&&(n.app=n.apps[0]||null)}),!this.app){this.app=e;var r=this.history;if(r instanceof Ct)r.transitionTo(r.getCurrentLocation());else if(r instanceof $t){var o=function(){r.setupListeners()};r.transitionTo(r.getCurrentLocation(),o,o)}r.listen(function(t){n.apps.forEach(function(e){e._route=t})})}},Mt.prototype.beforeEach=function(t){return Bt(this.beforeHooks,t)},Mt.prototype.beforeResolve=function(t){return Bt(this.resolveHooks,t)},Mt.prototype.afterEach=function(t){return Bt(this.afterHooks,t)},Mt.prototype.onReady=function(t,e){this.history.onReady(t,e)},Mt.prototype.onError=function(t){this.history.onError(t)},Mt.prototype.push=function(t,e,n){var r=this;if(!e&&!n&&"undefined"!=typeof Promise)return new Promise(function(e,n){r.history.push(t,e,n)});this.history.push(t,e,n)},Mt.prototype.replace=function(t,e,n){var r=this;if(!e&&!n&&"undefined"!=typeof Promise)return new Promise(function(e,n){r.history.replace(t,e,n)});this.history.replace(t,e,n)},Mt.prototype.go=function(t){this.history.go(t)},Mt.prototype.back=function(){this.go(-1)},Mt.prototype.forward=function(){this.go(1)},Mt.prototype.getMatchedComponents=function(t){var e=t?t.matched?t:this.resolve(t).route:this.currentRoute;return e?[].concat.apply([],e.matched.map(function(t){return Object.keys(t.components).map(function(e){return t.components[e]})})):[]},Mt.prototype.resolve=function(t,e,n){var r=D(t,e=e||this.history.current,n,this),o=this.match(r,e),i=o.redirectedFrom||o.fullPath;return{location:r,route:o,href:function(t,e,n){var r="hash"===n?"#"+e:e;return t?R(t+"/"+r):r}(this.history.base,i,this.mode),normalizedTo:r,resolved:o}},Mt.prototype.addRoutes=function(t){this.matcher.addRoutes(t),this.history.current!==m&&this.history.transitionTo(this.history.getCurrentLocation())},Object.defineProperties(Mt.prototype,Vt),Mt.install=Y,Mt.version="3.1.5",Q&&window.Vue&&window.Vue.use(Mt),Mt});