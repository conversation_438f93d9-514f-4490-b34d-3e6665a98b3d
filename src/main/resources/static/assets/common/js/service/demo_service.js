class DemoService extends ProdService {
    constructor(mapModule, project, fields) {
        super(mapModule, project, fields);
        this.profile = "demo";

        this.hailing = {pickupPoint: null, dropPoint: null};
        this.resultHailing = {isReceived: false, data: undefined, callUserId: undefined};
        this.simulationRunning = false;
        this.needToResumeSimulation = false;
        this.simulationTime = TimeUtil.getSimulationStartTime();
        this.showAlertTimer = -1;

        //Demo 모드 리스트에 따른 기능 on/off 플래그
        // this.optionReservationTime = false;//예약 배송 옵션 on/off
        this.isMobileTrackingMode = Constant.DEFAULT_SETTING.MOBILE_TRACKING_MODE;//TODO: 나중에 고려
        this.createManager(this.mapViewController);
        this.contextMenu = null;

        this.pathControlMessageIntervalId = -1;//MMPOptions.pathControlMessage Route서버에  MPP API를 요청해서 MPP 경로 갱신주기 설정 값


        this.trackEventMap = {};

    }

    createMapViewController( mapModule, fields ){
        return new DemoMapViewController(mapModule, fields);
    }

    createManager(mapViewController) {
        this.evMgr = new EvStationManager(mapViewController);
        this.rpMgr = new RangeProjectionManager(mapViewController, this.fields);
        this.mppMgr = new MPPManager(mapViewController, this.fields);
        this.locationHistoryMgr = new LocationHistoryManager(mapViewController);
        this.rtMgr = new ReservationTimeManager(mapViewController, this.project, this.fields);
    }


    canRiderMove(rider) {
        let canMove = true;
        if (rider.pause) {
            return false;
        }
        return canMove;
    }


    setNextDrivingDestination() {
        for (let rider of this.project.riders) {
            if (!rider.drivingDest || PlaceUtil.isDeliveryCompleted(rider.drivingDest)) {
                rider.drivingDest = DemoUtil.getDrivingDestination(rider);
                if (rider.drivingDest) {
                    rider.timeUnitSum = 0;
                    this.requestUpdateDeliveryStatusInfo(rider, rider.drivingDest, {
                        deliveryStatus: Constant.DELIVERY_STATUS.GOING,
                        realStartTime: this.simulationTime + ":00"
                    });
                }
                this.updateRiderStatus(rider);
            }
        }
    }

    drawAllRidersByTrackEvent(){
        for( const rider of this.project.riders){
            const eventData = this.trackEventMap[rider.riderId];
            if( eventData ){
                this.drawRiderByTrackEvent(rider, eventData );
                this.trackEventMap[rider.riderId] = null;
            }
        }
    }

    drawRiderByTrackEvent( rider , eventData ){

        if (rider.timeUnitSum === undefined) {
            rider.timeUnitSum = 0;
        }

        if (eventData.isResetTimeUnitSum) {
            rider.timeUnitSum = 0;
        } else {
            if (this.canRiderMove(rider)) {
                rider.timeUnitSum += eventData.timeUnit;//EV_Demo
                //rider.timeUnitSum += 1; // TODO : 임시용
            }else if( eventData.type === "MOVE_RIDER_MPP") {
                console.log("[MPP-EXCEPTION] Rider paused Return !!!!");
                return;
            }
        }

        if (eventData.simulationTime) {
            this.simulationTime = eventData.simulationTime;
        }

        // 실차인 경우 Skip
        if (rider.isRealTracking) {
            return;
        }

        // 이동중인 배송지 찾기
        let destination = rider.drivingDest;

        // 선형에서 위치 & 각도 계산
        let isDeliveryCompleted = false;
        if (eventData.type === "MOVE_RIDER_DEST" && destination && destination.deliveryStatus === Constant.DELIVERY_STATUS.COMPLETED)
            isDeliveryCompleted = true;

        if (!isDeliveryCompleted) {
            let routePath = null;
            let totalUnit = 0;

            if (destination && eventData.type === "MOVE_RIDER_DEST") {
                totalUnit = destination.estimatedSeconds;
                routePath = destination.routePath;
            } else if (eventData.type === "MOVE_RIDER_MPP") {
                totalUnit = rider.mpp.estimatedTime;
                routePath = rider.mppRoutePath;
            } else {
                return;
            }

            if (rider.timeUnitSum > totalUnit) {
                rider.timeUnitSum = totalUnit;
            }

            let moveInfo = this.getPointAngleObj(routePath, totalUnit, rider.timeUnitSum);

            if( !moveInfo ){
                console.log("[MPP-EXCEPTION] moveInfo si NULL !!!!");
                return;
            }

            const hour = eventData.timeUnit / 60 / 60;//EV_Demo Speed구하기
            const distance = Util.getDistance(rider, {x: moveInfo.dx, y: moveInfo.dy});
            let speed = distance / hour * 10 ;
            if( speed > 150 ){
                speed = 150;
            }

            rider.speed = Math.round(speed);

            rider.preX = rider.x;
            rider.preY = rider.y;
            rider.x = moveInfo.dx;
            rider.y = moveInfo.dy;
            rider.angle = moveInfo.angle;

            // this.evMgr.updateDrivableDistanceWhileDriving(rider, distance * 1000 /* km */);
            // await this.evMgr.showNearEvStationIfLowEnergy(rider); - 에너지가  적을때 자동으로 주유소 보여주는 기능인데 이상해 보이니 지운다

            if (destination && eventData.type === "MOVE_RIDER_DEST") {
                this.onArriveAtDestination(rider, destination);
            } else if (eventData.type === "MOVE_RIDER_MPP") {
                this.mppMgr.onProcessMppRoutePath(rider);
            }
        }

        console.log("moveRider");
        this.mapViewController.moveRider(rider);

        if (eventData.type === "MOVE_RIDER_DEST") {
            if (rider.isSelected && destination) {
                console.log("drawTrackingRoutePath");
                this.mapViewController.drawTrackingRoutePath(rider, destination);
            }
        }

    }

    async handleSimulationWorker(event) {

        if (!event.data) {
            return;
        }

        if (event.data.type === "COMPLETED") {
            this.drawAllRidersByTrackEvent();
            this.setNextDrivingDestination();
            this.updateMapView();
        } else {
            this.trackEventMap[event.data.riderId] = event.data;
        }
    }


    /**
     * 목적지 도착(로선형 끝점)에 도착시 처리
     */
    async onArriveAtDestination(rider, destination) {

        if (destination.routePath.length < 1)//EV_Demo
            return;

        if (rider.pause)
            return;


        // const routeEndPoint = destination.routePath[destination.routePath.length - 1];
        // const diffX = Math.abs(rider.x - routeEndPoint.x) * 100000;
        // const diffY = Math.abs(rider.y - routeEndPoint.y) * 100000;
        // if (diffX < 5 && diffY < 5) {
        if (destination.estimatedSeconds <= rider.timeUnitSum) {

            this.requestUpdateDeliveryStatusInfo(rider, destination, {
                deliveryStatus: Constant.DELIVERY_STATUS.SERVICING,
                realArrivalTime: this.simulationTime + ":00"
            });


            let realServiceDuration = destination.duration ? destination.duration : 1;
            if (!realServiceDuration)
                realServiceDuration = 1;

            const _rider = rider;
            const _destination = destination;
            setTimeout(() => {
                //프로젝트 리로딩 일어날 경우 대비하여 다시 값을 구한다
                let rider = RiderUtil.findRiderById(this.project.riders, _rider.riderId);
                let destination = PlaceUtil.findDestinationById(this.project.destinations, _destination.deliveryId);
                this.requestUpdateDeliveryStatusInfo(rider, destination, {
                    deliveryStatus: Constant.DELIVERY_STATUS.COMPLETED,
                    realEndTime: this.simulationTime + ":00",
                    realServiceDuration: realServiceDuration
                });
                rider.drivingDest = null;

            }, realServiceDuration * 500);

            this.updateRiderStatus(rider);
        }
    }


    requestUpdateDeliveryStatusInfo(rider, destination, delivery) {

        if (destination.deliveryStatus === delivery.deliveryStatus) {
            return;
        }

        const _this = this;
        console.log("[demo] requestUpdateDeliveryStatusInfo   - rider : " + rider.name + ", foundDestination : " + PlaceUtil.getDestinationDisplayId(destination) + " ,delivery status : " + delivery.deliveryStatus);
        TMS_WEB_API.updateForceDeliveryStatusInfoDemo(rider.riderId, destination.deliveryId, delivery, null);
        super.updateDeliveryStatusByEvent(rider, destination, delivery);
    }

    /**
     * 모의주행 모드 ON
     */
    simulatedDrivingMode() {

        this.setNextDrivingDestination();
        super.performSending( this.project.riders );
    }


    simulationTrackingPaused() {
        console.log("simulation tracking Paused");
        this.simulationRunning = false;
        this.clearPathControlMessageInterval();
    }

    simulationTrackingResumed() {
        console.log("simulation tracking Started");
        this.simulationRunning = true;
        // this.setAllRiderEvStationListOnRoute();
        this.startPathControlMessageInterval();
    }

    pauseSimulationTracking() {
        if (  this.simulationRunning  ) {
            const clockRef = app.$refs.aloaMap.$refs.clock;
            clockRef.$emit(EVENT.CLOCK.PAUSE_TRACKING);
            this.needToResumeSimulation = true;
        } else {
            this.needToResumeSimulation = false;
        }
    }

    resumeSimulationTracking() {
        if (  this.needToResumeSimulation  ) {
            this.startSimulationTracking();
            this.needToResumeSimulation = false;
        }
    }

    startSimulationTracking() {
        if ( !this.simulationRunning ) {
            const clockRef = app.$refs.aloaMap.$refs.clock;
            clockRef.$emit(EVENT.CLOCK.PLAY_TRACKING);
        }
    }

    startTracking() {
        console.log("[demo] startTracking() - do nothing");
    }

    //1개의 Route 만 실행
    async getRouteInfoAgainByRider(rider) {

        // 남은 경로 삭제
        this.mapViewController.clearRiderAllRoutePath(rider);
        this.clearRiderRoutingInfo(rider);
        this.evMgr.removeEvStationPinByRider(rider);

        const riders = [rider];

        PopupUtil.showLoadingPopup("탐색 중...", "잠시만 기다려 주세요.");

        // this.performClusteringForOneRider(rider);

        const firstmileRiders = RiderUtil.findRidersByMilesType(riders, true);
        const lastmileRiders = RiderUtil.findRidersByMilesType(riders, false);

        await this.getRoutingInfo(firstmileRiders, true, { isFindEvStation : false });
        await this.getRoutingInfo(lastmileRiders, false, { isFindEvStation : false });

        this.updateDestinationDisplayId(rider);
        PopupUtil.dismissLoadingPopup();

        this.drawRiderRouteLine(rider);

        this.calculateDeliveryTime( [rider] ); //start,endTime,serviceTime의 값을 destinations에 추가해준다.
        this.updateMapView();
    }

    /**
     * Utility functions using map
     */

    getPointAngleObj(routePath, totalUnit, unit) {
        try {
            if( !routePath) return;
            let pointAngleObj = this.mapViewController.getPointAngleOnPolylineByUnit(MapUtil.parsePtsArrayFromCoordinates(routePath), totalUnit, unit);
            if (pointAngleObj.angle <= 0) {
                pointAngleObj.angle = (pointAngleObj.angle * -1) + 180;
            } else {
                pointAngleObj.angle = 180 - (pointAngleObj.angle);
            }
            return pointAngleObj;
        } catch (e) {
            console.log(" ERROR(1) - " + JSON.stringify(routePath));
            console.log(" ERROR(2) - totalUnit: " + totalUnit + ", unit: " + unit);
            console.error("getPointAngleOnPolylineByUnit() - " + e);
        }
    }

    updateMppSimulatedDriving(timeUnit, simulationTime) {
        this.fields.simulationWorker.postMessage({
            country: this.fields.selectedCountry,
            riders: this.project.riders,
            timeUnit: timeUnit,
            reqUrl: Url.WEB.MPP,
            simulationTime: simulationTime,
        });
    }

    publicMQTTMessageToRiderApps(riders, projectStatus) {
        console.log("[demo]] - send FAKE publicMQTTMessageToRiderApps " + riders);
    }


   /**
     * waypoint -> waypointStationList -> stationOnRouteList 순으로 station을 찾는다.
     */
    findStationByStationName( rider, stationName ){
        let  findStation = DemoUtil.findWaypointByName(rider.waypoints, stationName);

        if( !findStation){
            findStation = DemoUtil.findWaypointByName ( rider.state.waypointStationList, stationName);
        }

        if( !findStation){
            findStation = DemoUtil.findWaypointByName ( rider.state.stationOnRouteList, stationName);
        }

        return findStation
    }

    /*
     * 충전소 후보에서 하나 선택시 rider.waypointStationList 에  추가 한다.
     * @param data
     */
    async onSelectedEvStation( data ) {
        if (data && data.riderId && data.station  ) {
            const rider = RiderUtil.findRiderById( this.project.riders, data.riderId);
            let  station = this.findStationByStationName( rider, data.station.name );
            //this.removeEvStationOnRoutePin(rider);
            // const station = rider.state.stationOnRouteList[data.stationIndex];
            this.evMgr.removeEvStationPinByRider(rider);
            this.evMgr.addEvStationToWaypointList(rider, station);

            if( this.fields.mapMode  === Constant.MAPMODE.TRACKING){
                await this.joinStationOnRoutePathDynamic(  rider );
            }else {
                if (this.project.completedRoutePath) {
                    await this.getRouteInfoAgainByRider(rider);
                }
                this.evMgr.showEvStationPinByRider(rider, this.fields.isShowStationListPanel  );
            }

            this.updateMapView();
        } else {
            alert("Error cant' find any data or ;event : " + JSON.stringify(data));
        }
    }

    /**
     * 충전소 후보에서 하나 선택시 waypointStationList 에  추가 한다.
     * @param data
     */
    async onUnSelectedEvStation( data  ){

        if ( data && data.riderId && data.station ) {
            const rider = RiderUtil.findRiderById( this.project.riders, data.riderId );
            let  station = this.findStationByStationName( rider, data.station.name );

            if( !station){
                console.warn(" onUnSelectedEvStation : Can't find station ");
                return;
            }

            if( station.chargeStatus === Constant.STATION_CHARGE_STATE.CHARGING || station.chargeStatus === Constant.STATION_CHARGE_STATE.CHARGE_COMPLETED   ){
                alert("이미 경유한 충전소는 취소할 수 없습니다." );
                return;
            }

            this.evMgr.removeEvStationPinByRider( rider );
            this.rpMgr.cleanWayPointRangeProjection( rider );

            // let willDeleteStation = rider.state.waypointStationList[data.stationIndex];
            // const delWaypoint = rider.waypoints.find( waypoint => waypoint.sid === willDeleteStation.sid);
            const delWaypoint = station;
            this.deleteWaypointRider( rider, delWaypoint );

            if( this.fields.mapMode  === Constant.MAPMODE.TRACKING) {
                await this.joinStationOnRoutePathDynamic( rider );
            }else if( this.project.completedRoutePath){
                await this.getRouteInfoAgainByRider(rider);
                //this.generateRoutingInfo();
                this.evMgr.showEvStationPinByRider(rider, this.fields.isShowStationListPanel  );
            }

            this.updateMapView();
        }else{
            alert("Error cant' find any data or ;event : " + JSON.stringify(data));
        }
    }

    async loadProject(projectId, callback = null, props ) {
        console.log("[demo] loadProject " + projectId);
        let callbackReload = null;

        //다시 로딩할때 시뮬레이션에 필요한 정보를 다시 머지 한다
        if (this.project.riders) {
            let copyRiders = [];
            for (const rider of this.project.riders) {
                let copyRider = MapUtil.copyObject(rider);
                copyRider.destinations = [];
                for (const dest of rider.destinations) {
                    copyRider.destinations.push(MapUtil.copyObject(dest))
                }
                copyRiders.push(copyRider);
            }

            const _this = this;
            callbackReload = {
                onSuccess: (response) => {
                    for (let rider of _this.project.riders) {
                        const findRider = RiderUtil.findRiderById(copyRiders, rider.riderId);
                        if (findRider) {
                            rider.timeUnitSum = findRider.timeUnitSum;
                            if (!rider.drivingDest && findRider.drivingDest) {
                                rider.drivingDest = PlaceUtil.findDestinationById(rider.destinations, findRider.drivingDest.deliveryId);
                            }

                            if( findRider.mpp ) {
                                this.mppMgr.copyMppRiderData( findRider, rider );
                            }

                            for (const dest of rider.destinations) {
                                const findDest = PlaceUtil.findDestinationById(findRider.destinations, dest.deliveryId);
                                if (findDest) {
                                    dest.realArrivalTime = findDest.realArrivalTime;
                                    dest.realStartTime = findDest.realStartTime;
                                    dest.readlEndTime = findDest.readlEndTime;
                                }
                            }
                        }
                    }
                }
            };
        }

        await super.loadProject(projectId, callback ? callback : callbackReload, props );

    }


    onUpdateMPPSetting(data ){//clock을 어떻게 접근할지 모르겠어서 파라미터로 받아온다
        console.log("[MPP] update mpp settings " + JSON.stringify(data) );
        PopupUtil.showNotificationPopup("설정값이 변경되었습니다. 새로운 설정값을 적용합니다");

        // let isMPPSettingsChanged = this.mppMgr.checkUpdateMPPSettings( data );
        this.saveLocalStorage( "setMPPOptions", data );

        this.refreshMPP( true );
    }

    /**
     *  MPP Interface functions
     */
    performMpp( rider , isAppended ){
        if( !this.mppMgr.isMPPEnabled() ) return;
        this.mppMgr.performMpp(rider, isAppended );
    }

    clearMppRoutePath(rider){
        this.mppMgr.clearMppRoutePath(rider);
    }

    drawMppRoutePath(rider){
        this.mppMgr.drawMppRouteLine(rider);
    }

    refreshMPP( isMppChanged = true ){
        if( !this.mppMgr.isMPPEnabled() ) return;

        this.pauseSimulationTracking();

        if( isMppChanged) {
            for (const rider of this.project.riders) {
                this.mppMgr.refreshMPP(rider);
            }
        }

        this.mppMgr.updateAbsoluteVehiclePosition();
        this.resumeSimulationTracking();

    }

    async onPerformSending( silentMode  ){
        this.mppMgr.updateAbsoluteVehiclePosition();
        await super.onPerformSending( silentMode );
    }

    startPathControlMessageInterval(){

        // const pathControlMessageIntervalMs = this.mppMgr.getSettingPathControlMessage() * 1000;
        const pathControlMessageIntervalMs = this.mppMgr.getSettingPathControlMessage() ;

        this.clearPathControlMessageInterval();
        if( pathControlMessageIntervalMs <= 0 ) return;

        // this.pathControlMessageIntervalId = setInterval(() => {
        const _this = this;
        this.pathControlMessageIntervalId = setTimeout(() => {
            if( _this.simulationRunning ) {
                console.log("[MPP-INTERVAL]startPathControlMessageInterval - run refreshMPP()")
                _this.refreshMPP();
                _this.startPathControlMessageInterval();
            }
        }, pathControlMessageIntervalMs );
    }

    /**
     * 기존 방문지에 신규 Station JOIN
     *
     * @param object rider 배차될 Rider
     */
    async joinStationOnRoutePathDynamic(rider ) {

        let order = 0;
        let lastGoingPath = undefined;
        if (rider.drivingDest) {
            let goingPlace = DemoUtil.getGoingPlace(rider);
            lastGoingPath = this.getPastRoutePath(rider, goingPlace);
            order = goingPlace.order;
            this.mapViewController.clearRemainedRouteLine(rider, order - 1); //현재 가고 있는 route도 지운다.
        } else {// MPP
            order = DemoUtil.getMaxPlaceOrderNumber(rider);
        }

        let startPoint = Util.getRouteFormatObj(rider.x, rider.y);
        let remainedDestinations = DemoUtil.getRemainedDestinations(rider.destinations);//EV_Demo 추가. 경로 탐색이 된적이 없을때
        const destPoints = remainedDestinations.map(destination => Util.getRouteFormatObj(destination.x, destination.y, destination.id));

        try {
            await this.requestRouting( rider, startPoint, destPoints, order , { prefixRoutePath:lastGoingPath} );
            const remainedDestinationIndex = rider.destinations.findIndex(destination => destination.order > order);
            if (remainedDestinationIndex >= 0) {
                const remainedDestinations = rider.destinations.splice(order, rider.destinations.length);
                const sortedRemainedDestinations = PlaceUtil.generateSortedDestinationsByOrder(remainedDestinations);
                for (const dest of sortedRemainedDestinations) {
                    rider.destinations.push(dest);
                }
            }
            // }
        } catch (error) {
            console.error(error);
            Util.alert("joinStationOnRoutePath 중 오류가 발생하였습니다. 위치를 확인해주세요.");	// TODO
            return;
        }

        rider.drivingDest = DemoUtil.findNextDestinationByOrder(rider, order);
        rider.timeUnitSum = 0;
        if( rider.drivingDest ){
            rider.drivingDest.recordPastPath = undefined;
        }

        this.calculateDeliveryTime(this.project.riders);
        this.drawRiderRouteLine(rider);

        await this.performMpp(rider);

        // this.evMgr.showWaypointEvStation(rider);
        this.evMgr.showEvStationPinByRider(rider, this.fields.isShowStationListPanel);
        this.updateMapView();


    }

    clearPathControlMessageInterval(){
        if( this.pathControlMessageIntervalId > 0 ){
            console.log("[MPP-INTERVAL]clearPathControlMessageInterval")
            clearInterval(this.pathControlMessageIntervalId);
            this.pathControlMessageIntervalId = null;
        }
    }

    publicMQTTMessageToRiderApps(riders, projectStatus) {
        console.log("[DEMO] publicMQTTMessageToRiderApps called : " + projectStatus );
    }

    updateOnDemandClusteringOption(onDemandOptions) {
        if( onDemandOptions.clusterRuleOnDemand === Constant.ON_DEMAND_CLUSTER_RULE.DISTANCE_TO_RIDER ){
            PopupUtil.alertPopup("시뮬레이션에서 GPS 기반의 '근접 거리 우선' 옵션은 정상적으로 동작히지 않을수 있습니다.")
        }

        super.updateOnDemandClusteringOption(onDemandOptions );
    }

    //override
    getContextMenuItem() {
        let menuItems = super.getCommonContextMenuItem();

        const _this = this;
        let contextMenuType = this.fields.contextMenuType;
        if (contextMenuType & Constant.CONTEXT_MENU_ITEM.ITEM_SHOW_RANGE_PROJECTION ) {
            menuItems.push ( {name: _t('주행가능범위 보기'), handler: async function (data) {
                    await _this.onShowRangeProjection(data)
                } } ) ;
        }

        if (contextMenuType & Constant.CONTEXT_MENU_ITEM.ITEM_HIDE_RANGE_PROJECTION ) {
            menuItems.push ( {name:  _t('주행가능범위 종료'), handler: function(data) { _this.onHideRangeProjection( data ) } } ) ;
        }

        if (menuItems.length > 0) {
            menuItems.push({
                name: _t('닫기'), handler: function (data) {
                }
            });
        }

        return menuItems;
    }

    async onShowRangeProjection(data) {
        if (data.info) {
            // this.rpMgr.performRangeProjectionZoom(data.info);//이거 쓰면 느리다.
            // this.processAfterMapPositionChanged();
            await this.rpMgr.performRangeProjection(data.info, null, true , true);
        }
    }

    onHideRangeProjection(data){
        if( data.info) {
            this.rpMgr.clearRangeProjection(data.info);
            this.mapViewController.updateMapView();
        }
    }


    addLocationHistoryByPosition( pos ){
        this.locationHistoryMgr.addHistoryByPosition(pos.x, pos.y);
    }

    addLocationHistoryByType( type, id ){
        this.locationHistoryMgr.addHistoryByType(type, id );
    }

    restoreLocationHistory(forward = false ){
        this.locationHistoryMgr.restoreHistory(forward, this.project );
        // this.processAfterMapPositionChanged(field);//TODO - check
    }


    /**
     * 여러가지 destination 추가하는 로직을 담는다.
     */
    async requestAddDestination(worldCoords, refs) {
        let destination = null;
        if (this.fields.mapMode === Constant.MAPMODE.EDIT) {
            destination = await this.addDestinationByClick(worldCoords.x, worldCoords.y, this.fields.pinMode  );
            this.addLocationHistoryByType(LocationHistory.TYPE.DESTINATION, destination.id);
        } else {
            if (Global.options.fulfilmentMode) {
                if (this.fields.pinMode == Constant.PINMODE.LAST_MILE_DROPOFF) {
                    let newDestination = await this.addDestinationByClick(worldCoords.x, worldCoords.y, this.fields.pinMode );
                    this.pauseSimulationTracking( refs ) ;
                    await this.addDestinationOfFullfillment(newDestination, this.fields.pinMode);
                    this.updateMapView();
                    this.startSimulationTracking( refs );
                } else if (this.fields.pinMode == Constant.PINMODE.DESTINATION) {
                    Global.options.fulfilmentMode = true;
                    let newDestination = await this.addDestinationByClick(worldCoords.x, worldCoords.y, this.fields.pinMode );
                    //this.pauseSimulationTracking( refs );
                    await this.addDestinationOfFullfillment2(newDestination, this.fields.pinMode);
                    this.updateMapView();
                    this.startSimulationTracking( refs );
                }
            } else {
                // const dests = PlaceUtil.getDestinationsByTypes(this.project.destinations, [Constant.DestinationType.INTERMEDIATE, Constant.DestinationType.FINAL]);
                let newDestination = await this.addDestinationByClick(worldCoords.x, worldCoords.y, this.fields.pinMode );
                const foundRider = await this.addDestinationOfDynamic(newDestination, this.fields.pinMode, this.getLoginUserInfo());
                this.evMgr.showEvStationPinByRider(foundRider, this.fields.isShowStationListPanel);
                this.addLocationHistoryByPosition(worldCoords);
                this.updateMapView();
            }
        }
        return destination;
    }


    /**
     * 마우스 정보 전달
     */
    getMouseEventObject( event ){
        return { event: event , isMouseDown : this.fields.isMouseDown, isMouseMove : this.fields.isMouseMove , lastMousePoint : this.fields.lastMousePoint };
    }

    async mouseUp( event, refs ) {
        const keyProcessDone = this.rtMgr.mouseUp( this.getMouseEventObject(event) );
        if( keyProcessDone ) {//픽업-드롭간의 연관 관계가 바뀌었을 때
            this.clearAllClusterAndRouteLine();
        } else if (  (this.fields.isMobile && event instanceof Touch && !this.fields.isTouchMove) ||
            ( !this.fields.isMouseMove &&  !this.fields.isShowContextMenu && event.button === 0) ) {
            let newAddedUnit = null;

            if ( this.fields.topToolbarSelectionMode != 0) {
                const worldCoords = this.callScreen2world(Math.round(event.clientX), Math.round(event.clientY));
                if ( this.fields.mapMode === Constant.MAPMODE.EDIT) {
                    DEMO_API.setRunning(true);
                    const riders = RiderUtil.findRidersByMilesType(this.project.riders, PlaceUtil.isFirstMile(this.fields.pinMode));

                    // 방문지나 Rider가 추가되므로 그룹핑 정보가 표시가 되어 있는 경우에는 지운다.
                    this.mapViewController.cleanCluster(riders, PlaceUtil.isFirstMile(this.fields.pinMode));

                    if (PlaceUtil.isLocationPinMode(this.fields.pinMode)) {
                        const newDestination = await this.requestAddDestination(worldCoords);
                        newAddedUnit = newDestination;
                    } else if (RiderUtil.isRiderPinMode(this.fields.pinMode)) {
                        let rider = await this.addRiderByClick(  worldCoords.x, worldCoords.y, this.fields.pinMode);
                        await this.loadRiderInfo(rider);
                        this.evMgr.updateDrivableDistance(rider);
                        this.addLocationHistoryByType(LocationHistory.TYPE.RIDER, rider.riderId);
                        // await this.performMpp(rider, false);
                        // this.updateMapView();
                        newAddedUnit = rider;
                    }

                    if (newAddedUnit) {
                        if( newAddedUnit && DemoUtil.isPickupOrDropoffPlace( newAddedUnit )) {//픽업/드롭이 추가되면 경로를 취소한다.
                            this.clearAllClusterAndRouteLine();
                        }else {
                            await this.reArrangeAllMapUnit();
                        }
                    }

                } else if (this.fields.mapMode  === Constant.MAPMODE.TRACKING) {
                    // if (this.fields.pinMode === Constant.PINMODE.TRUCK || this.fields.pinMode === Constant.PINMODE.EV }} ) {//EV_Demo
                    if (RiderUtil.isRiderPinMode(this.fields.pinMode)) {//모든 기사 동적 배차 가능
                        let rider = await this.addRiderByClick(worldCoords.x, worldCoords.y, this.fields.pinMode);
                        await this.loadRiderInfo(rider);
                        this.addLocationHistoryByType(LocationHistory.TYPE.RIDER, rider.riderId);
                        // await this.getVehicleInfo(rider);//EV_Demo update
                        this.evMgr.updateDrivableDistance(rider);
                        await this.performMpp(rider, false);
                        this.updateMapView();
                        // } else if (PlaceUtil.isLocationPinMode(this.fields.pinMode) && this.fields.pinMode !== Constant.PINMODE.HUB ) { //동적일 때는 Hub를 놓치 않도록 한다.
                    }else if ( DemoUtil.isPossibleToAddDestinationPinOnTracking( this.fields.pinMode )  ){
                        await this.requestAddDestination(  worldCoords, refs );
                    } else if (DemoUtil.isHailingPinMode(this.fields.pinMode)) {
                        this.performHailing(  worldCoords, this.fields.pinMode, this.hailing, this.getLoginUserInfo);
                        this.addLocationHistoryByPosition(worldCoords);
                    }
                }
            } else {
                const selectedItem = this.getDataFromPosition(  event.clientX, event.clientY);

                if (selectedItem) {
                    if (selectedItem.info && DemoUtil.isRiderType(selectedItem.info.type)) {
                        const selectedRider = selectedItem.info;
                        if( selectedRider.state ) RiderUtil.setRiderStatus(selectedRider);
                        this.riderSelected(  selectedRider, true);
                        this.showEnergyGaugeInfoBox(selectedRider, this.fields);
                        this.addLocationHistoryByType(LocationHistory.TYPE.RIDER, selectedItem.info.id);
                    } else if (selectedItem.info && DemoUtil.isDestinationType(selectedItem.info.type)) {
                        const selectedDest = selectedItem.info;
                        this.destinationSelected(  selectedDest, true);
                        this.addLocationHistoryByType(LocationHistory.TYPE.DESTINATION, selectedDest.id);
                    } else if (selectedItem.type === Constant.TP.ALL_STATION) {
                        this.showStationInfoBox(selectedItem.station, {buttonEnabled: false, selectMode: true});
                    } else if (selectedItem.type === Constant.TP.STATION_ON_ROUTE) {
                        this.showStationInfoBox(selectedItem.station, {
                            buttonEnabled: true,
                            riderId: selectedItem.riderId,
                            stationIndex: selectedItem.stationIndex,
                            selectMode: true
                        });
                    } else if (selectedItem.type === Constant.TP.WAYPOINT_STATION) {
                        this.showStationInfoBox(selectedItem.station, {
                            buttonEnabled: true,
                            riderId: selectedItem.riderId,
                            stationIndex: selectedItem.stationIndex,
                            selectMode: false
                        });
                    }
                }
                // else {
                // 	const searchAddrIndex = this.getSearchAddrPinFromPosition(event.clientX, event.clientY);
                // 	if (searchAddrIndex) {
                // 		this.setSearchAddressSelected(searchAddrIndex);
                // 	}
                // }
            }
        }
        // this.setAllEvStationListOnRoute();

        this.fields.isMouseDown = false;
        this.fields.isMouseMove = false

    }


    changeEvStationLayerMode(){
        this.fields.layerMode.evStation = !this.fields.layerMode.evStation;//위 v-model에서 값이 저절로 바뀌지만 먼저 호출된 이후에 바뀌기 때문에 이부분을 변경해준다.

        if ( this.fields.layerMode.evStation  ) {
            this.evMgr.showAllEvStationOnEntireMap();
        } else {
            this.evMgr.clearAllEvStationOnEntireMap ();
        }

        this.updateMapView();
    }

    setShowStationListPanel( isShow ){
        if( isShow ){
            this.setAllRiderEvStationListOnRoute();
            this.fields.isShowStationListPanel = true;
        }else{
            this.fields.isShowStationListPanel = false;
        }

        if( this.fields.isShowStationListPanel ){
            for (const rider of this.project.riders) {
                this.evMgr.showEvStationOnRoutePin(rider);
            }
        }else{
            for (const rider of this.project.riders) {
                this.evMgr.removeEvStationOnRoutePin(rider);
            }
        }
        this.updateMapView();
    }

    getGasStationInfoBoxStyle(station){
        const param = {popupWidth: 300, popupHeight: 230 , x: station.x, y: station.y , marginLeft: 148 , marginTop: 18 };
        const screenPosition = this.mapViewController.world2screen( param.x, param.y );
        return DemoUtil.getStylePopupPosition( param, screenPosition );
    }


    getEnergyGaugeInfoBoxStyle(rider){
        const popupHeight = this.fields.infoBoxParam.isShowStationWaypoint ?  170 : 152;
        const marginTop = this.fields.infoBoxParam.isShowStationWaypoint ?  60 : 42;
        const param = {popupWidth: 253, popupHeight: popupHeight , x: rider.x, y: rider.y , marginLeft: 123 , marginTop: marginTop };
        const screenPosition = this.mapViewController.world2screen( param.x, param.y );
        return DemoUtil.getStylePopupPosition( param, screenPosition );
    }


    clickButtonStationOnStationInfoBox(){
        this.closeAllPopup();
        let data ={};
        data.riderId = this.fields.infoBoxParam.riderId;
        data.stationIndex = this.fields.infoBoxParam.stationIndex;

        if( this.fields.infoBoxParam.buttonEnabled) {
            data.station = this.fields.focusedObject;
            if (this.fields.infoBoxParam.selectMode) {
                this.onSelectedEvStation(data);
            } else {
                this.onUnSelectedEvStation(data);
            }
        }
    }


    changeEvStationLayerMode(){
        this.fields.layerMode.evStation = !this.fields.layerMode.evStation;//위 v-model에서 값이 저절로 바뀌지만 먼저 호출된 이후에 바뀌기 때문에 이부분을 변경해준다.

        if ( this.fields.layerMode.evStation  ) {
            this.evMgr.showAllEvStationOnEntireMap();
        } else {
            this.evMgr.clearAllEvStationOnEntireMap ();
        }

        this.updateMapView();
    }

    /**
     * TODO: 이거 사용하지 않은 방법으로 생각해 보자 - 나중에 Event로 쏴주자
     */
    setAllRiderEvStationListOnRoute( project ){
        if( !project )
            project = this.project;

        let isEvRiderOnMap = false;
        for( const rider of this.project.riders ){
            if( RiderUtil.isEvRider(rider)){
                isEvRiderOnMap = true;
            }
        }

        this.fields.isShowStationListButton = isEvRiderOnMap;
        this.fields.allEvStationListOnRoute = this.evMgr.getAllEvStationListOnRoute( project.riders  );

        // if( this.allEvStationListOnRoute.length === 0 ){
        // 	this.setShowStationListPanel( false );
        // }
    }
    getDataFromStationPosition(x, y) {
        const tagId = this.evMgr.hitTestForStationPin(x, y);
        return this.getDataFromTagId(tagId);
    }


    onStationClickedOnList(station){
        let stationPopupShow = true;
        this.closeAllPopup();
        if( stationPopupShow ){
            const _this = this;
            this.moveMapCenterAndRun( station.x, station.y ,function(){
                    const screenPosition = _this.mapViewController.world2screen( station.x, station.y );
                    const selectedItem = _this.getDataFromStationPosition(screenPosition.x, screenPosition.y);
                    if( selectedItem && selectedItem.type ){
                        if ( selectedItem.type === Constant.TP.STATION_ON_ROUTE){
                            _this.showStationInfoBox( selectedItem.station , { buttonEnabled: true, riderId: selectedItem.riderId , stationIndex : selectedItem.stationIndex , selectMode : true }  );
                        }else if ( selectedItem.type === Constant.TP.WAYPOINT_STATION){
                            _this.showStationInfoBox( selectedItem.station , { buttonEnabled: true, riderId: selectedItem.riderId , stationIndex : selectedItem.stationIndex , selectMode : false }  );
                        }
                    }
                }
            );

        }else {
            this.setCenterFocus(station, true);
        }
    }

    async searchStation(){
        await this.evMgr.searchNearEvStationAllRider(this.project.riders);
        this.setAllRiderEvStationListOnRoute();
    }

    setShowStationListPanel( isShow ){
        if( isShow ){
            this.setAllRiderEvStationListOnRoute();
            this.fields.isShowStationListPanel = true;
        }else{
            this.fields.isShowStationListPanel = false;
        }

        if( this.fields.isShowStationListPanel ){
            for (const rider of this.project.riders) {
                this.evMgr.showEvStationOnRoutePin(rider);
            }
        }else{
            for (const rider of this.project.riders) {
                this.evMgr.removeEvStationOnRoutePin(rider);
            }
        }
        this.updateMapView();
    }

    stationClickedOnList(station) {
        this.closeAllPopup();
        const _this = this;
        this.moveMapCenterAndRun(station.x, station.y, function () {
                const screenPosition = _this.mapViewController.world2screen(station.x, station.y);
                const selectedItem = _this.getDataFromStationPosition(screenPosition.x, screenPosition.y);
                if (selectedItem && selectedItem.type) {
                    if (selectedItem.type === Constant.TP.STATION_ON_ROUTE) {
                        _this.showStationInfoBox(selectedItem.station, {
                            buttonEnabled: true,
                            riderId: selectedItem.riderId,
                            stationIndex: selectedItem.stationIndex,
                            selectMode: true
                        });
                    } else if (selectedItem.type === Constant.TP.WAYPOINT_STATION) {
                        _this.showStationInfoBox(selectedItem.station, {
                            buttonEnabled: true,
                            riderId: selectedItem.riderId,
                            stationIndex: selectedItem.stationIndex,
                            selectMode: false
                        });
                    }
                }
            }
        );
    }

    getCenterPosition(location = null) {
        let centerPos;
        if(location) {
            centerPos = {'x' : location.x, 'y' : location.y};
        } else {
            centerPos = this.callScreen2world ( window.innerWidth/2, window.innerHeight/2);
        }
        return centerPos;
    }

    showEnergyGaugeInfoBox(rider ){
        const _this = this;
        this.moveMapCenterAndRun( rider.x, rider.y , function(){
            _this.fields.centerPos = _this.getCenterPosition();
            _this.fields.focusedObject = rider;
            _this.fields.infoBoxParam.isShowStationWaypoint = DemoUtil.isExistStationOnRoute(rider);
            _this.fields.infoBoxParam.evMode = RiderUtil.isEvRider(rider);
            _this.fields.popUp = Constant.POPUP_TYPE.ENERGY_GAUGE;
        } );
    }

    showStationInfoBox(station , params ){
        const _this = this;
        this.moveMapCenterAndRun( station.x , station.y , function(){
            _this.fields.centerPos = _this.getCenterPosition();
            _this.fields.focusedObject = station;
            _this.fields.infoBoxParam = params;
            _this.fields.popUp = Constant.POPUP_TYPE.GAS_STATION;
        });
    }


    moveMapCenterAndRun( x, y , callBack ){
        //if(  this.fields.mapMode  !== Constant.MAPMODE.TRACKING) // 트래킹 중에는 차량이 움직이므로 center이동을 하지 말자
        this.mapViewController.setCenterFocus(x, y, -1, true );

        let preZoomBound = this.callGetZoomBound();
        const _this = this;
        const checkMoveCompleteTimer = setInterval(function () {
            const zoomBound = _this.callGetZoomBound();
            if( zoomBound.left === preZoomBound.left && zoomBound.top === preZoomBound.top ){
                clearInterval( checkMoveCompleteTimer );
                // _this.setCenterPosition();
                if( callBack ){
                    callBack();
                }
            }
            preZoomBound = zoomBound;
        } , 300);

    }

    getDistanceFromRider(rider, point) {
        const INIT_MAX_DISTANCE = 999999999;

        //1) 현재 이동중인 방문지 경로와 point사이의 거리
        let distance = INIT_MAX_DISTANCE;
        let remainedDestinations = DemoUtil.getRemainedDestinations(rider.destinations, null);
        if (remainedDestinations && remainedDestinations.length > 0) {
            let remainedRoutePath = remainedDestinations.flatMap(destination => destination.routePath);
            const nearbyInfo = this.mapViewController.getMinPtDistanceOnPolyline(MapUtil.parsePtsArrayFromCoordinates(remainedRoutePath), point.x, point.y);
            distance = parseInt(nearbyInfo.dist);
            if (distance < 0) distance = INIT_MAX_DISTANCE;
        } else {
            distance = Util.getDistance(rider, point) * 1000; // km -> meter
        }

        return distance;
    }

    findNearestRider2(riders, point) {
        let _this = this;
        let riderListWithDistance = [];
        riders.forEach(rider => {
            let distance = _this.getDistanceFromRider(rider, point);
            riderListWithDistance.push({rider: rider, distance: distance})
        });

        let sortedRiderListWithDistance = riderListWithDistance.sort(function (a, b) {
            return a.distance > b.distance ? 1 : a.distance < b.distance ? -1 : 0;
        });

        const selectedRider = sortedRiderListWithDistance[0].rider;
        console.info("findNearestRider -  " + selectedRider.id);
        return selectedRider;
    }

    async addDestinationOfFullfillment2(newDestination, pinMode) {
        // 트럭 경로상에서 최종방문지와 가장 근접한 트력을 찾는다.
        let trucks = RiderUtil.findRidersByMilesType(this.project.riders, true);
        let truck = this.findNearestRider2(trucks, newDestination);
        if (!truck) {console.log('트럭을 찾을수 없습니다.'); return;}

        // 오토바이 경로상에서 최종방문지와 가장 근접한 오토바이를 찾는다.
        let bikes = RiderUtil.findRidersByMilesType(this.project.riders, false);
        let bike = this.findNearestRider2(bikes, newDestination);
        if (!bike) {console.log('오토바이를 찾을수 없습니다.'); return;}

        // '최종방문지' 와 가장 가까운 트럭의 거점을 찾는다.
        let pickupDestination = {};
        let noCompletedDestinations = truck.destinations
            .filter(d => d.deliveryStatus !== Constant.DELIVERY_STATUS.COMPLETED)
            .filter(d => d.type === Constant.DestinationType.INTERMEDIATE);
        if (noCompletedDestinations && noCompletedDestinations.length > 0) {
            // '최종방문지' 와 가장 가까운 트럭의 거점을 찾는다.
            let minimumDistance = noCompletedDestinations.map(d =>{
                return {'destination': d, 'distance' : Util.getDistance(newDestination, d)};
            }).reduce((p,c) => p.distance > c.distance ? c:p);

            console.log(`최종방문지와 가장 가까운 거점 : ${minimumDistance.destination.id}`);
            // pickupDestination = minimumDistance.destination; //목적지를 공유하면 경로가 바뀌므로 복사한다.
            Object.assign( pickupDestination, minimumDistance.destination );
        } else {
            console.log('거점이 없습니다.');
        }

        // 남은 경로 삭제
        this.mppMgr.clearMppRouteLine(bike);
        // this.mapViewController.clearRemainedRouteLine(bike);
        this.mapViewController.clearRiderAllRoutePath(bike);
        this.evMgr.removeEvStationPinByRider(bike);

        // fullfillment 관련 설정
        pickupDestination.isArrivedTruck = false;
        pickupDestination.fullfillmentTruckId = truck.id;
        if (!pickupDestination.waitingBikeIds) pickupDestination.waitingBikeIds = [];

        let newDestinations = [];
        const alreadyBikeId = pickupDestination.waitingBikeIds.find(id => id === bike.id);
        if (!alreadyBikeId) {
            pickupDestination.waitingBikeIds.push(bike.id);
            newDestinations.push(pickupDestination);
        }

        newDestination.pickupDestinationId = pickupDestination.id;
        newDestinations.push(newDestination);

        // 찾은 Rider에게 신규 방문지 목록 그룹핑
        this.joinCluster(bike, newDestinations );

        if (alreadyBikeId) {
            // 경로탐색 (fullfillment)
            await this.joinRoutePathOfFullfillment(bike);
        } else {
            // 경로탐색 (픽업/드랍)
            await this.joinRoutePathOfHailing(bike, pickupDestination, newDestination);
        }

        await this.performMpp(bike);

        // 경로 그리기
        this.drawRiderRouteLine(bike);

        // 방문지 아이콘 변경 (fix)
        this.updateDestinationDisplayId(bike);
        this.mapViewController.changeDestinationPin(bike, newDestinations.map(d => d.id), Constant.DESTINATION_PIN_MODE.SET);
        this.mapViewController.updateDestinationPinLabel(bike, Constant.DESTINATION_PIN_MODE.SET);
        bike.isCompletedAllDriving = false;
    }

    async addDestinationOfFullfillment(newDestination, pinMode) {
        // 트럭경로상에서 최종방문지와 가장 근접한 트력을 찾는다.
        let trucks = RiderUtil.findRidersByMilesType(this.project.riders, true);
        let truck = this.findNearestRider(trucks, newDestination, true);
        console.log(`가장 근접한 트럭 : ${truck.name}`);

        // 트럭의 남아 있는 전체 경로
        let remainedTotalRoutePath = RiderUtil.getRemainedTotalRoutePath(truck);

        // 오토바이 목록 조회
        let bikes = RiderUtil.findRidersByMilesType(this.project.riders, false);


        // 오토바이별 거리 계산을 위한 루프
        let selectedMeetingPlace = null;
        let minimumDistance = 999999999;
        for (const bike of bikes) {

            // 오토바이 위치에서 배송중인 마지막 방문지까지의 거리
            let remainedTotalDeliveryDistance = RiderUtil.getRemainedTotalDeliveryDistance(bike);
            console.log(`${bike.name} - 오토바이 남은 거리 : ${remainedTotalDeliveryDistance}`)

            // 오토바이의 마지막 방문지 위치 기준으로 트럭경로상에 가장 근접한 접점 반환
            const lastDest = RiderUtil.getLastPosition(bike);
            await DEMO_API.getRouteNearestInfo(remainedTotalRoutePath, {x:lastDest.x, y:lastDest.y}, {
                onSuccess: (response) => {
                    console.log(`기사이름:${bike.name} - ${JSON.stringify(response.data)}`);
                    // [sample] meetingPlace : {"distance":606.5696639023994,"point":{"x":127.02464,"y":37.50451}}
                    let meetingPlace = response.data;
                    meetingPlace.bikeId = bike.id;
                    meetingPlace.point.x = Math.round(meetingPlace.point.x * 100000) / 100000;
                    meetingPlace.point.y = Math.round(meetingPlace.point.y * 100000) / 100000;

                    // 만남장소에서 신규방문지 까지의 거리
                    let distanceFromMeetingPlaceToDestination = Util.getDistance(meetingPlace.point, newDestination) * 1000;

                    // {오토바이 기사 남은 목적지 거리} + {마지막 목적지에서 접선장소까지 거리} + {접선상소에서 신규 목적지까지 거리}
                    let bikeTotalDistance = remainedTotalDeliveryDistance + meetingPlace.distance + distanceFromMeetingPlaceToDestination;

                    if (minimumDistance > bikeTotalDistance) {
                        minimumDistance = bikeTotalDistance;
                        meetingPlace.riderId = bike.id;
                        selectedMeetingPlace = meetingPlace;
                    }

                    // // 현재 트럭위치에서 만남장소 까지의 거리
                    // let truckDrivingPositionToMeetingPositionDistance = RiderUtil.getDistanceFromDrivingPositionToPoint(truck, meetingPlace.point);
                    //
                    // // 트럭이 만남장소까지 가는 거리와 오토바이가 만남장소까지 가는 거리 차이 계산
                    // let diffDistance = Math.abs(truckDrivingPositionToMeetingPositionDistance - bikeTotalDistance);
                    // console.log(`'${bike.name}'님 만남장소 거리 - 오토바이(${bikeTotalDistance}), 트럭(${truckDrivingPositionToMeetingPositionDistance})`);
                    //
                    // // 총 거리가 가장 짧은 경우
                    // if (minimumDistance > diffDistance) {
                    //     minimumDistance = diffDistance;
                    //     meetingPlace.riderId = bike.id;
                    //     selectedMeetingPlace = meetingPlace;
                    // }
                },
                onError: (e) => {
                    console.error(JSON.stringify(e));
                }
            });
        }

        if (!selectedMeetingPlace) {
            console.error(`배차 실패`);
            return;
        }

        this.mapViewController.changeTruckRiderPin( truck, Constant.TRUCK_COLOR.MINT ); //선택된 트럭의 색을 바꾼다

        // 선택된 오토바이 조회
        let selectedBike = RiderUtil.findRiderById(bikes, selectedMeetingPlace.bikeId);
        console.log(`${selectedBike.name} 님에게 배차되었습니다.`);

        // 남은 경로 삭제
        this.mppMgr.clearMppRouteLine(selectedBike);
        this.mapViewController.clearRemainedRouteLine(selectedBike);
        this.evMgr.removeEvStationPinByRider(selectedBike);

        // 만남장소(픽업지) 추가
        let meetingDestination = await this.addDestinationByClick(selectedMeetingPlace.point.x, selectedMeetingPlace.point.y, Constant.PINMODE.LAST_MILE_PICKUP );
        meetingDestination.isMeetingPlace = true;
        meetingDestination.isArrived = false;
        meetingDestination.meetingPlaceId = truck.id + '_' + truck.meetingPlaceList.length;

        // 찾은 Rider에게 신규 방문지 목록 그룹핑
        this.joinCluster(selectedBike, [meetingDestination, newDestination] );

        // 트럭에 만남장소 추가
        truck.meetingPlaceList.push(PlaceUtil.findDestinationById(selectedBike.destinations, meetingDestination.id));
        // 만남장소들 짧은 거리순 정렬
        RiderUtil.sortMeetingPlaceByShortestDistance(truck);

        if (truck.currentMeetingPlaceId === undefined)  {
            truck.currentMeetingPlaceId = meetingDestination.meetingPlaceId;
        } else {
            truck.currentMeetingPlaceId = truck.meetingPlaceList[0].meetingPlaceId;
        }


        // 경로탐색 (픽업/드랍)
        await this.joinRoutePathOfHailing(selectedBike, meetingDestination, newDestination);
        await this.performMpp(selectedBike);

        // 경로 그리기
        this.drawRiderRouteLine(selectedBike);

        // 방문지 아이콘 변경 (fix)
        this.updateDestinationDisplayId(selectedBike);
        this.mapViewController.changeDestinationPin(selectedBike, [meetingDestination.id, newDestination.id], Constant.DESTINATION_PIN_MODE.SET);
        this.mapViewController.updateDestinationPinLabel(selectedBike, Constant.DESTINATION_PIN_MODE.SET);
        selectedBike.isCompletedAllDriving = false;

        // 맵 화면 업데이트
        //this.updateMapView();
    }

    updateDestinationDisplayId(rider) {
        let count = 1;

        for (let destination of rider.destinations) {
            if (destination.type == Constant.DestinationType.HUB) {
                continue;
            }
            if( destination.order ){
                destination.label = destination.order;
            }else{
                destination.label = count;
            }
            count++;
        }
    }

    /**
     * 풀필먼트 방문지 경로탐색
     * 하나의 픽업지에 여러 드랍오프지 경로탐색을 수행한다.
     *
     * @param rider
     * @returns {Promise<void>}
     */
    async joinRoutePathOfFullfillment(rider) {

        const pickupDestination = rider.destinations.find(d => d.type === Constant.DestinationType.INTERMEDIATE);
        const dropoffDestinations = rider.destinations.filter(d => d.pickupDestinationId);
        const startPoint = Util.getRouteFormatObj(pickupDestination.x, pickupDestination.y);
        const viaPoints = dropoffDestinations.map(d => Util.getRouteFormatObj(d.x, d.y, d.id));
        try {
            await this.requestRouting(rider, startPoint, viaPoints, pickupDestination.order);
        } catch (e) {
            console.error(e);
            Util.alert("풀필먼트 방문지 경로탐색 오류")
        }

        this.calculateDeliveryTime([rider]);
        // 경로 순서로 정렬
        rider.destinations = PlaceUtil.generateSortedDestinationsByOrder(rider.destinations);
    }

    async requestRouting(rider, startPoint, destPoints, pickupPlaceOrder, options = {}) {
        //set default options
        if( Util.isNull(options.isFindEvStation ) )     options.isFindEvStation = true;

        const isExistPickupDropoff = DemoUtil.hasPickupDropoffDestinations(this.project.destinations) && this.rtMgr.needReservationTimeRouting(rider);
        const isExistDestWithStartTime = DemoUtil.hasReservationTimeAllDestinations(this.project.destinations);

        if( isExistPickupDropoff || isExistDestWithStartTime ) {//예약 배송 API로 경로 탐색 시작
            console.debug("[route] request Route With Reservation Time - destPoints : " + destPoints.length  + " rider Id : " + rider.riderId );
            await this.requestRoutingOrderByTimeSync(rider, startPoint, destPoints, pickupPlaceOrder, options);
        }else {
            console.debug("[route] request Normal Route (No Reservation Time )- destPoints :  " +  destPoints.length + " rider Id : " + rider.riderId );
            await this.requestRoutingNormal(rider, startPoint, destPoints, pickupPlaceOrder, options);
        }
    }

    /**
     * 예약 시간 고려한 경로 탐색
     */
    async requestRoutingOrderByTimeSync(rider, startPoint, destPoints, pickupPlaceOrder, options ) {
        // const hasPickupPlace = options.hasPickupPlace;

        this.setRiderWorkStartTime(rider);
        const workStartTimeEpoch =   TimeUtil.convertEpochSeconds( rider.workingStartTime );

        console.log("[route] workStartTime: ( " +  workStartTimeEpoch +" )" + " rider : " + rider.riderId + "(" + rider.name + ")");
        const vehicleInfo = this.getVehicleInfoWithWorkStartTime(startPoint.dx, startPoint.dy, workStartTimeEpoch );

        //const defaultTime = TimeUtil.toDateTimeStrByTimeStr( rider.workingStartTime );//현재 시간을 디폴트로 넣을 경우 예약 시간 있느 장소를 제일 나중에 방문하는 버그가 있다. 가장 늦은 시간으로 넣어야 한다.
        const defaultTime = TimeUtil.getLatestTodayTime();
        this.rtMgr.modifyReservationTime( defaultTime );//예약시간 넣기전에 예약 시간을 보정한다.
        const nDataInfoList = this.rtMgr.makeReservationTimeRouteDataList(destPoints, this.fields.defaultServiceDuration );


        let routeReqObj = {
            'country': this.fields.selectedCountry,
            'startPoint': startPoint,
            'destPoints': nDataInfoList,
            'routeOption': this.fields.routeOption,
            'riderId': rider.riderId,
            'startTime' : workStartTimeEpoch
        };

        let response = await ROUTE_API.getRouteInfoSyncReservationTime(routeReqObj);

        if( response ) {
            const path = response.data;
            for (let i = 0; i < path.order.length; i++) {
                const destinationType = path.order[i].destinationType;
                let prefixRoutePath = undefined;
                if (options.prefixRoutePath && path.order[i].visit == 1) { //처음 좌표에 경로를 이어 붙이는 기능
                    prefixRoutePath = options.prefixRoutePath;
                }
                if (destinationType === Constant.ROUTE_DESTINATION_TYPE.GENERAL) {
                    this.updateRouteInfo(i, rider.destinations, path, pickupPlaceOrder, prefixRoutePath);
                } else if (destinationType === Constant.ROUTE_DESTINATION_TYPE.EV_STATION) {
                    this.updateWaypointRouteInfo(i, path, rider, pickupPlaceOrder);
                }
            }
        }
    }


    updateWaypointRouteInfo(i, routeData, rider, startOrder, prefixRoutePath) {
        let findStation = DemoUtil.findWaypointByStationId(rider.state.waypointStationList, routeData.order[i].nid);//nid는 sid값이 들어가 있다.

        if( !findStation){//waypoint에서 찾을수 없으면 backend에서 추가된 waypoint로 볼수 있다.
            findStation = DemoUtil.findWaypointByStationId(rider.state.waypointStationList, -1 /* 새로 추가된  id */ );
        }

        if( !findStation ){//waypoints에 존재하지 않고 경로 탐색으로 받은 주유소
            findStation = DemoUtil.findWaypointByStationId(routeData.waypointStationList, null );
        }

        let waypoint = DemoUtil.findWaypointByName(rider.waypoints, findStation.name);

        if (!waypoint) {
            let newWayPoint = {};
            newWayPoint.name = findStation.name;
            newWayPoint.x = findStation.x;
            newWayPoint.y = findStation.y;
            rider.waypoints.push(newWayPoint);
            waypoint = newWayPoint;
            waypoint.id = findStation.name;//나중에 station의 고유한 Id 값을 생성을 해야 한다.

        }

        const order = routeData.order[i].visit;
        waypoint.order = startOrder + order;
        waypoint.drawingOrder = waypoint.order == 1 ? 2 : 1;
        waypoint.estimatedSeconds = routeData.order[i].estimate;
        waypoint.totalDistance = routeData.order[i].dist;
        if (prefixRoutePath) {
            waypoint.drawingRoutePath = prefixRoutePath;
        }
        waypoint.routePath = [];
        routeData.pnts[order - 1].forEach(pnt => {
            waypoint.routePath.push({x: pnt.dx, y: pnt.dy}); //dx,dy=>x,y
        });

        waypoint.type = Constant.DestinationType.EV_STATION;//EV_Demo
        waypoint.chargeStatus = Constant.STATION_CHARGE_STATE.NOT_ARRIVED;
        waypoint.chargingCount = 0;
        // const stationPos = routeData.pnts[order-1][routeData.pnts[order-1].length-1];//위치를 넣어준다.
        // waypoint.x = stationPos.x;
        // waypoint.y = stationPos.y;
        waypoint.estimatedServiceDuration = Constant.WAYPOINT_STATION_SERVICE_DURATION;

        return waypoint;
    }

    /**
     * Rider의 destinations에 경로 정보 업데이트
     */
    updateRouteInfo( i, destinations, routeData, startOrder, prefixRoutePath) {
        const foundDestination = PlaceUtil.findDestinationById(destinations, routeData.order[i].nid);

        if (foundDestination) {
            const order = routeData.order[i].visit;
            foundDestination.order = order + startOrder;
            foundDestination.drawingOrder = foundDestination.order == 1 ? 2 : 1;
            foundDestination.estimatedSeconds = routeData.order[i].estimate;
            foundDestination.totalDistance = routeData.order[i].dist;
            foundDestination.estimatedServiceDuration = this.fields.defaultServiceDuration;
            if (prefixRoutePath) {
                foundDestination.drawingRoutePath = prefixRoutePath;
            } else {
                foundDestination.drawingRoutePath = null;
            }


            foundDestination.routePath = [];
            //EV_Demo 예외처리 routeData.order 갯수와 routeData가 일치하지 않는 경우가 있다. 두개의 목적지가 거의 붙어 있는 경우이다.
            if (routeData.pnts.length > order - 1) {
                routeData.pnts[order - 1].forEach(pnt => {
                    foundDestination.routePath.push({x: pnt.dx, y: pnt.dy}); //dx,dy=>x,y
                });
            } else {
                console.warn("[exception] routeData.pnts.length is  " + routeData.pnts.length + ", But order is " + order + "  check this codes.");
                routeData.pnts[routeData.pnts.length - 1].forEach(pnt => {
                    foundDestination.routePath.push({x: pnt.dx, y: pnt.dy}); //dx,dy=>x,y
                });
            }

        }
        return foundDestination;
    }


}
