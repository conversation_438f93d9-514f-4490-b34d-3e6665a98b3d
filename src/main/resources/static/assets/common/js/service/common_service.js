class CommonService {
    constructor() {
        this.mapViewController = null;//mapViewController는 prod,demo 임시로 각각 가져간다. - 나중에 UI가 통일되면 하나로 통일 가능 하다.
        this.workStartLocationNewConcept = false;      // redmine-#935 기사 근무 시작 주소 컨셉 원복
    }


    /**
     * MapViewController Redirect functions
     */
    updateMapView() {
        if (app && app.$refs) {
            const aloaMap = app.$refs.aloaMap;
            aloaMap.$emit(EVENT.MAP.UPDATE_MAP_VIEW);
        }
    }

    /**
     * MapViewController direct functions
     */
    callUpdateMapView() {
        this.mapViewController.updateMapView();
    }

    callUpdateMapViewSync() {
        console.debug("callUpdateMapViewSync");
        this.mapViewController.updateMapViewSync();
    }

    callGetZoomLevel(){
        return this.mapViewController.getZoomLevel();
    }

    callSetZoomIn(){
        this.mapViewController.setZoomIn();
    }

    callSetZoomOut(){
        this.mapViewController.setZoomOut();
    }

    cleanCluster(riders){
        this.mapViewController.cleanCluster(riders);
    }

    callGetMapLevel(){
        return this.mapViewController.getMapLevel();
    }

    callGetZoomLevel(){
        return this.mapViewController.getZoomLevel();
    }

    callAddRiderTag(rider){
        this.mapViewController.addRiderTag(rider);
    }

    callSetZoomBound(rect){
        this.mapViewController.setZoomBound(rect);
    }

    callSetRiderTagVisible(id, show ){
        this.mapViewController.setRiderTagVisible(id,show);
    }


    callSetDestinationPinTagVisible(id, show ){
        this.mapViewController.setDestinationPinTagVisible(id, show);
    }

    callDeleteLabelByTag( tagName){
        this.mapViewController.deleteLabelByTag( tagName );
    }

    callLoadImageFiles() {
        this.mapViewController.loadImageFiles();
    }

    callScreen2world(x,y){
        return this.mapViewController.screen2world(x,y);
    }

    callHitImageMapPositionsByScrPoint(scrX, scrY) {
        return this.mapViewController.hitImageMapPositionsByScrPoint(scrX, scrY);
    }

    callSetCenterFocus(x, y, zoomlvl = -1, bStepMove = true ){
        this.mapViewController.setCenterFocus(x, y, zoomlvl, bStepMove);
    }

    callGetZoomBound(){
        return this.mapViewController.getZoomBound();
    }

    callWorld2screen(x,y){
        return this.mapViewController.world2screen(x,y);
    }

    addRiderPin(rider) {
        this.mapViewController.addRiderPin(rider);
    }

    updatePinAndRouteColor(rider, destination) {
        this.mapViewController.updatePinAndRouteColor( rider, destination );
    }
}
