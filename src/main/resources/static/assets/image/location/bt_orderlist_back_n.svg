<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="47" height="47" viewBox="0 0 47 47"><defs><style>.a,.c{fill:#fff;}.a{stroke:#707070;}.b{clip-path:url(#a);}.d{fill:none;stroke:#083338;stroke-linejoin:round;stroke-width:2px;}.e{fill:#083338;}.f{filter:url(#b);}</style><clipPath id="a"><rect class="a" width="47" height="47" transform="translate(25.359 519)"/></clipPath><filter id="b" x="0" y="0" width="47" height="47" filterUnits="userSpaceOnUse"><feOffset dy="3" input="SourceAlpha"/><feGaussianBlur stdDeviation="2" result="c"/><feFlood flood-opacity="0.161"/><feComposite operator="in" in2="c"/><feComposite in="SourceGraphic"/></filter></defs><g class="b" transform="translate(-25.359 -519)"><g class="f" transform="matrix(1, 0, 0, 1, 25.36, 519)"><rect class="c" width="35" height="35" rx="3" transform="translate(6 3)"/></g><g transform="translate(-957.24 518.9)"><path class="d" d="M9.036,2V9.612c0,3.594-2.183,4.736-4.442,4.743S0,13.228,0,9.639,0,2,0,2" transform="translate(999.148 24.792) rotate(-90)"/><path class="e" d="M3.259,0,6.518,5H0Z" transform="translate(998.3 19.018) rotate(-90)"/></g></g></svg>