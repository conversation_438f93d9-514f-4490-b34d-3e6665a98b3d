var eventBus = new Vue();

// 컴포넌트 등록
regComponents({
    'modal-popup': modalPopup
    ,'login': login
});

var app = new Vue({
	el: '#login_main',
	props: {

	},
	
	data: function() {
		return {
		}
	},

	beforeCreate: function() {

    },
	
	created: function() {

	},
	
	mounted() {

	},
    
	methods: {

		/**
		 * 로그인 성공시 호출
		 * 
		 * @param data 고객 정보
		 */
		onLoginSuccess(customer) {
            console.log('지도 메인 페이지 이동');
            window.location.href="/"
		},
	}
});