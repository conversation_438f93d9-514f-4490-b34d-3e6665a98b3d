@charset "EUC-KR";
@import url("../assets/css/font.css");


* {	
    margin: 0px;
    padding: 0px;
    font-family: Noto Sans KR;
    font-weight: 500;

}

/*=======================���==========================*/
#notification-detail-popup-container .modal_full{
	position: absolute;
	width: 100vw;
	height: 100vh;
	background-color: rgba(0,0,0,0.3);
}


/*=================�˾� ������==========================*/
#notification-detail-popup-container .noti_popup{
	position: absolute;
	top:50%;
	left: 50%;
	margin-top: -175px;
	margin-left: -185px;
	width: 370px;
	height: auto;
	/* border: 1px solid red; */
}
#notification-detail-popup-container .noti_popup_area_l{
	position: relative;
	top:0px;
	width: 100%;
	padding-top: 50px;
	height: 300px;
	background-color: #fff;	
	border-radius: 3px;
	/*border: 1px solid red;*/
}


#notification-detail-popup-container .noti_icon_memo{
	position: relative;
	margin: auto;
	width: 26px;
	height: 26px;
	background-image: url("/assets/image/prod/toptoolbar/ic_alarm_message.svg");
	background-size: 100%;
	/*border: 1px solid red;*/

}

#notification-detail-popup-container .noti_icon_fail{
	position: relative;
	margin: auto;
	width: 26px;
	height: 26px;
	background-image: url("/assets/image/prod/toptoolbar/ic_alarm_fail.svg");
	background-size: 100%;
	/*border: 1px solid red;*/
}


#notification-detail-popup-container .noti_icon_accident{
	position: relative;
	margin: auto;
	width: 26px;
	height: 26px;
	background-image: url("/assets/image/prod/toptoolbar/ic_alarm_accident.svg");
	background-size: 100%;
	/*border: 1px solid red;*/
}

#notification-detail-popup-container .noti_icon_start{
	 position: relative;
	 margin: auto;
	 width: 26px;
	 height: 26px;
	 background-image: url("/assets/image/prod/toptoolbar/ic_alarm_start.svg");
	 background-size: 100%;
	 /*border: 1px solid red;*/
}

#notification-detail-popup-container .noti_icon_pause{
	position: relative;
	margin: auto;
	width: 26px;
	height: 26px;
	background-image: url("/assets/image/prod/toptoolbar/ic_alarm_pause.svg");
	background-size: 100%;
	/*border: 1px solid red;*/
}

#notification-detail-popup-container .noti_icon_complete{
	position: relative;
	margin: auto;
	width: 26px;
	height: 26px;
	background-image: url("/assets/image/prod/toptoolbar/ic_alarm_complete.svg");
	background-size: 100%;
	/*border: 1px solid red;*/
}

#notification-detail-popup-container .noti_icon_delay{
	position: relative;
	margin: auto;
	width: 26px;
	height: 26px;
	background-image: url("/assets/image/prod/toptoolbar/ic_alarm_delay.svg");
	background-size: 100%;
	/*border: 1px solid red;*/
}

#notification-detail-popup-container .noti_icon_dutyoff{
	position: relative;
	margin: auto;
	width: 26px;
	height: 26px;
	background-image: url("/assets/image/prod/toptoolbar/ic_alarm_dutyoff.svg");
	background-size: 100%;
	/*border: 1px solid red;*/
}

#notification-detail-popup-container .noti_icon_work_completion{
	position: relative;
	margin: auto;
	width: 26px;
	height: 26px;
	background-image: url("/assets/image/prod/toptoolbar/ic_alarm_work_completion.svg");
	background-size: 100%;
	/*border: 1px solid red;*/
}

#notification-detail-popup-container .noti_icon_attendance{
	position: relative;
	margin: auto;
	width: 26px;
	height: 26px;
	background-image: url("/assets/image/prod/toptoolbar/ic_alarm_attendance.svg");
	background-size: 100%;
	/*border: 1px solid red;*/
}

#notification-detail-popup-container .noti_icon_no_attendance{
	position: relative;
	margin: auto;
	width: 26px;
	height: 26px;
	background-image: url("/assets/image/prod/toptoolbar/ic_alarm_no_attendance.svg");
	background-size: 100%;
	/*border: 1px solid red;*/
}

#notification-detail-popup-container .noti_icon_late_attendance{
	position: relative;
	margin: auto;
	width: 26px;
	height: 26px;
	background-image: url("/assets/image/prod/toptoolbar/ic_alarm_late_attendance.svg");
	background-size: 100%;
	/*border: 1px solid red;*/
}

#notification-detail-popup-container .noti_icon_on_duty{
	position: relative;
	margin: auto;
	width: 26px;
	height: 26px;
	background-image: url("/assets/image/prod/toptoolbar/ic_alarm_on_duty.svg");
	background-size: 100%;
	/*border: 1px solid red;*/
}

#notification-detail-popup-container .noti_icon_dispatch_rejection{
	position: relative;
	margin: auto;
	width: 26px;
	height: 26px;
	background-image: url("/assets/image/prod/toptoolbar/ic_alarm_dispatch_rejection.svg");
	background-size: 100%;
	/*border: 1px solid red;*/
}

#notification-detail-popup-container .noti_icon_dispatch_acceptance{
	position: relative;
	margin: auto;
	width: 26px;
	height: 26px;
	background-image: url("/assets/image/prod/toptoolbar/ic_alarm_dispatch_acceptance.svg");
	background-size: 100%;
	/*border: 1px solid red;*/
}

#notification-detail-popup-container .noti_text_subject_b{
	position: relative;
	width: 100%;
	height: 24px;
	margin-top: 17px;
	font-weight: 500;
	text-align: center;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 16px;
	color: #113E46;
	border: 0px solid red;
}
#notification-detail-popup-container .noti_text_subject_r{
	position: relative;
	width: 100%;
	height: 24px;
	margin-top: 17px;
	font-weight: 500;
	text-align: center;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 16px;
	color: #FF6077;
	border: 0px solid red;
}
#notification-detail-popup-container .noti_text_driver{
	position: relative;
	width: 100%;
	height: 19px;
	margin-top: 10px;
	font-weight: 500;
	text-align: center;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #113E46;
	border: 0px solid red;
}

#notification-detail-popup-container .noti_text_memo{
	position: relative;
	width: 270px;
	height: 134px;
	padding: 10px 20px 10px 20px;
	word-break: break-all;
	margin: auto;
	margin-top: 15px;
	text-align: left;
	font-weight: 500;
	text-align: left;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	/*color: #97A0A2;*/
	background-color: #F4F4F4;
	border: 0px solid red;
}

#notification-detail-popup-container .noti_button_b{
		position: relative;
		margin-top: 10px;
		left: 50%;
		margin-left: -87.5px;
		width: 175px;
		height: 45px;
		border: 0px;
		border-radius: 4px;
		outline: 0px;
		background-color: #C3C3C3;
		font-weight: 500;
		text-align: center;
		font-family: 'Noto Sans KR', sans-serif;
		font-size: 16px;
		color: #fff;
	}

#notification-detail-popup-container .noti_button_b:hover{
		background-color: #113E46;
	}

#notification-detail-popup-container .noti_button_r{
		position: relative;
		margin-top: 10px;
		left: 50%;
		margin-left: -87.5px;
		width: 175px;
		height: 45px;
		border: 0px;
		border-radius: 4px;
		outline: 0px;
		background-color: #C3C3C3;
		font-weight: 500;
		text-align: center;
		font-family: 'Noto Sans KR', sans-serif;
		font-size: 16px;
		color: #fff;
	}

#notification-detail-popup-container .noti_button_r:hover{
		background-color: #FF6077;
	}

#notification-detail-popup-container .noti_popup_area_s{
	position: relative;
	top:0px;
	width: 100%;
	padding-top: 50px;
	height: 170px;
	background-color: #fff;	
	border-radius: 3px;
	/*border: 1px solid red;*/
}

#notification-detail-popup-container .noti_text_memo_s{
	position: relative;
	width: 100%;
	margin: auto;
	font-weight: 500;
	text-align: center;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #97A0A2;
	border: 0px solid red;
}

