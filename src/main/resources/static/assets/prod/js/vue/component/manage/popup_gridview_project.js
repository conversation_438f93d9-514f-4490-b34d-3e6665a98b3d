var gridviewProjectPopup = {
    template: `
    <div id="manage_list_body">
        <div class="project_manage bg-[#113E46]">
            <div class="project_grid_title_tool">
                <div class="project_title_tool">
                    <div class=delivery_project_manage_inputdate1> 
                        <jqx-datetime-input ref="fromDateTimeInput" style="float: left"
                                @valueChanged="onFromValueChanged($event)" 
                                @open="onOpen()" 
                                @close="onClose()"
                                :formatString="'yyyy-MM-dd'"
                                :value ="fromMoment"
                                :width="120" 
                                :height="34">
                        </jqx-datetime-input>
                    </div>
                    <div class="project_manage_date_middle">~</div>
                    <div class=delivery_project_manage_inputdate2> 
                        <jqx-datetime-input ref="toDateTimeInput" style="float: left"
                                @valueChanged="onToValueChanged($event)" 
                                @open="onOpen()" 
                                @close="onClose()"
                                :formatString="'yyyy-MM-dd'"
                                :value ="toMoment"
                                :width="120" 
                                :height="34">
                        </jqx-datetime-input>
                    </div>
                    
                    <select class="manage_warehouse_list_select" ref="warehouseCode" v-model="warehouseCode" @change="onWarehouseCodeChange($event)">
                        <option value="" disabled>{{$t("지점 선택")}}</option>
                        <option v-for="option in warehouseCodeList" :value="option">
                            {{option}}
                        </option>
                    </select>
                    <div>
                        <button class="project_delivery_exited" @click="deliveryDownload">{{ $t("다운로드") }}  </button>
                    </div>
                    <div>
                        <button class="project_delivery_exited" @click="deliveryEditPopupClosed">{{ $t("닫기") }}  </button>
                    </div>
                    <!-- 전체 배송 현황 파악용 데이터 표출 -->
                    <div class="project_delivery_count_area">
                        <div class="project_delivery_count">
                            {{$t('전체')}} ({{countAll}})
                        </div>
                        <div class="project_delivery_count_line" ></div>
                        <div class="project_delivery_count">
                            {{$t('배송중')}} ({{countGoing}})
                        </div>
                        <div class="project_delivery_count_line" ></div>
                        <div class="project_delivery_count">
                            {{$t('배송완료')}} ({{countCompleted}})
                        </div>
                        <div class="project_delivery_count_line" ></div>
                        <div class="project_delivery_count">
                            {{$t('배송실패')}} ({{countFailure}})
                        </div>
                    </div>
                </div>                    
            </div>
            <div class="project_grid_body_tool">
                <jqx-grid ref="destsGrid"  
                    :width="'100%'" :height="'96%'" :source="dataAdapter" :columns="columns"
                    @rowclick="destinationslistevent($event)"  
                    :editable="false" :columnsresize="true"
                    :selectionmode="'multiplecellsadvanced'" :sortable="true"
                    :theme="'aloagridtheme'">
                </jqx-grid>
            </div>
        </div>
    </div>
    `, props: {},

    data: function () {
        return {
            columns: [{
                text: _t('구분'),
                align: 'center',
                dataField: 'serialNo',
                width: 50,
                cellsalign: 'center',
                cellsrenderer: this.cellsrenderer
            }, {
                text: _t('센터코드'),
                align: 'center',
                dataField: 'warehouseCode',
                width: 80,
                cellsalign: 'center',
                cellsrenderer: this.cellsrenderer
            }, {
                text: _t('배송일자'),
                align: 'center',
                dataField: 'deliveryTime',
                width: 100,
                cellsalign: 'center',
                cellsrenderer: this.cellsrenderer
            }, {
                text: _t('호차번호'),
                align: 'center',
                dataField: 'dispatchNo',
                width: 100,
                cellsalign: 'center',
                cellsrenderer: this.cellsrenderer
            }, {
                text: _t('배송순번'),
                align: 'center',
                dataField: 'orderNo',
                width: 80,
                minWidth: 80,
                cellsalign: 'center',
                cellsrenderer: this.cellsrenderer
            }, {
                text: _t('기사명'),
                align: 'center',
                dataField: 'driverName',
                width: 120,
                cellsalign: 'center',
                cellsformat: 'T',
                cellsrenderer: this.cellsrenderer
            }, {
                text: _t('고객사 주문번호'),
                align: 'center',
                dataField: 'customerOrderId',
                width: 135,
                cellsalign: 'center',
                cellsformat: 'T',
                cellsrenderer: this.cellsrenderer
            }, {
                text: _t('배송상태'),
                align: 'center',
                dataField: 'deliveryStatusKor',
                width: 100,
                cellsalign: 'center',
                cellsformat: 'T',
                cellsrenderer: this.cellsrenderer
            }, {
                text: _t('배송시작시간'),
                align: 'center',
                dataField: 'deliveryStartTime',
                width: 100,
                cellsalign: 'center',
                cellsrenderer: this.cellsrenderer
            }, {
                text: _t('배송종료시간'),
                align: 'center',
                dataField: 'deliveryEndTime',
                width: 100,
                cellsalign: 'center',
                cellsrenderer: this.cellsrenderer
            }, {
                text: _t('법정동'),
                align: 'center',
                dataField: 'eupMyeonDong',
                width: 80,
                cellsalign: 'center',
                cellsrenderer: this.cellsrenderer
            }, {
                text: _t('주소'),
                align: 'center',
                dataField: 'address',
                width: 540,
                cellsalign: 'center',
                cellsrenderer: this.cellsrenderer
            }, {
                text: _t('프로젝트명'),
                align: 'center',
                dataField: 'projectName',
                width: 320,
                cellsalign: 'center',
                cellsrenderer: this.cellsrenderer
            }],

            dataAdapter: null,

            fromValue: new Date(),
            fromMoment: null,
            toMoment: null,
            warehouseCode: null,
            warehouseCodeList: [],

            dests: null,
        };
    },

    computed: {
        countAll() {
            return _.isEmpty(this.dests) ? 0 : this.dests.length;
        },
        countGoing() {
            return _.isEmpty(this.dests) ? 0 : this.dests.filter(item => ['배송전', '배송중', '서비스중'].includes(item.deliveryStatusKor)).length;
        },
        countCompleted() {
            return _.isEmpty(this.dests) ? 0 : _.filter(this.dests, {deliveryStatusKor: '배송완료'}).length;
        },
        countFailure() {
            return _.isEmpty(this.dests) ? 0 : _.filter(this.dests, {deliveryStatusKor: '배송실패'}).length;
        },
    },

    beforeCreate: function () {
    },

    created: function () {
        if (this.eventHandlers) {
            EVENT.registerEventHandler(this, this.eventHandlers);
        }
    },

    mounted() {
        this.getWareHouseList();
    },

    methods: {

        deliveryEditPopupClosed: function () {
            window.parent.app.$emit(EVENT.MAIN.CLOSE_PROJECT_GRIDVIEW_POPUP);
        },

        deliveryDownload: function () {
            // Get grid data
            const gridData = this.destsSource; // Use the localdata in your adapter
            console.log(gridData);
            if (!gridData || !gridData.localdata || gridData.localdata.length === 0) {
                PopupUtil.alertPopup(_t("배송목록이 없습니다."));
            } else {
                const gridDataChanged = gridData.localdata.map((item) => ({
                    "구분": item.serialNo,
                    "센터코드": item.warehouseCode,
                    "배송일자": item.deliveryTime,
                    "호차번호": item.dispatchNo,
                    "배송순번": item.orderNo,
                    "기사명": item.driverName,
                    "고객사 주문번호": item.customerOrderId,
                    "배송상태": item.deliveryStatusKor,
                    "배송시작시간": item.deliveryStartTime,
                    "배송종료시간": item.deliveryEndTime,
                    "법정동": item.eupMyeonDong,
                    "주소": item.address,
                    "프로젝트명": item.projectName,
                }));

                // Convert to worksheet using SheetJS
                const worksheet = XLSX.utils.json_to_sheet(gridDataChanged);

                // Set column widths
                const colWidths = [
                    {wch: 5},  // "구분"
                    {wch: 10}, // "센터코드"
                    {wch: 12}, // "배송일자"
                    {wch: 8},  // "호차번호"
                    {wch: 10}, // "배송순번"
                    {wch: 12}, // "기사명"
                    {wch: 18}, // "고객사 주문번호"
                    {wch: 10}, // "배송상태"
                    {wch: 15}, // "배송시작시간"
                    {wch: 15}, // "배송종료시간"
                    {wch: 10}, // "법정동"
                    {wch: 50}, // "주소"
                    {wch: 40}, // "프로젝트명"
                ];
                worksheet['!cols'] = colWidths;

                const workbook = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(workbook, worksheet, "DeliveryData");

                const setToday = TimeUtil.getDateFormattedString(new Date(), "YYYY-MM-DD");
                const setTime = TimeUtil.getDateFormattedString(new Date(), "HH:mm:ss");

                // Export to Excel
                XLSX.writeFile(workbook, `${this.fromMoment}_${this.toMoment}_배송 엑셀_(${setToday}_${setTime}).xlsx`);
            }
        },

        onOpen: function () {

        },

        onClose: function () {

        },

        onFromValueChanged: function (event) {
            if (event.args.date != null) {
                this.fromMoment = this.getFormatDate(event.args.date);
            } else
                this.fromMoment = null;
            this.onChangeData();
        },

        onToValueChanged: function (event) {
            if (event.args.date != null) {
                this.toMoment = this.getFormatDate(event.args.date);
            } else
                this.toMoment = null;
            this.onChangeData();
        },

        onChangeData: function () {
            if (this.fromMoment != null && this.toMoment != null) {
                if (!_.isNil(this.warehouseCode)) {
                    this.loadDeliveryProjectList();
                }
            }
        },

        isSearchDate(fromDate, toDate) {
            const dateDiff = moment(toDate).diff(moment(fromDate), 'days');
            if(dateDiff < 31)
                return true;
            else
                return false;
        },

        getFormatDate: function (date) {
            var year = date.getFullYear();
            var month = (1 + date.getMonth());
            month = month >= 10 ? month : '0' + month;
            var day = date.getDate();
            day = day >= 10 ? day : '0' + day;
            return year + '-' + month + '-' + day;
        },

        onWarehouseCodeChange: function (event) {
            if (this.fromMoment !== null && this.toMoment !== null && this.warehouseCode !== null) {
                this.loadDeliveryProjectList();
            }
        },

        getWareHouseList: function () {
            const _this = this;
            DELIVERY_API.getDeliveriesWareHouseList({
                onSuccess: (response) => {
                    console.log(response);
                    _this.warehouseCodeList = response.data;
                },
                onError: (error) => {
                    console.log(error);
                }
            })

        },

        loadDeliveryProjectList: function () {
            const _this = this;

            if (this.fromMoment != null && this.toMoment != null) {
                let fromDt = this.fromMoment + "T00:00:00";
                let toDt = this.toMoment + "T23:59:59";
                if ((!moment(toDt).isAfter(fromDt)) && (!moment(toDt).isSame(fromDt))) {
                    PopupUtil.alertPopup("기간을 잘못 설정하였습니다.");
                    return;
                }
                if (!this.isSearchDate(this.fromMoment, this.toMoment)) {
                    PopupUtil.alertPopup(`검색기간은 최대 31일까지입니다. </br> 기간을 다시 입력해주세요.`);
                    return;
                }
            }

            PopupUtil.showLoadingPopup("로딩 중...", "잠시만 기다려 주세요.");
            let toDateTime = TimeUtil.changedAddDays(this.toMoment, 1);
            DELIVERY_API.getDeliveriesProjectList(this.fromMoment, toDateTime, this.warehouseCode, {
                onSuccess: (response) => {
                    PopupUtil.dismissLoadingPopup();
                    console.log(response);
                    _this.dests = response.data;
                    _this.deliveriesProjectForm();
                },
                onError: (error) => {
                    PopupUtil.dismissLoadingPopup();
                    console.log(error);
                    PopupUtil.alertPopup(error.response.data);
                }
            });
        },

        cellsrenderer: function (row, columnsfield, value, defaulthtml, columnproperties, rowdata) {
            let changedValue = value.toString();
            if (changedValue) {
                changedValue = changedValue.replaceAll("<", "&lt;");
                changedValue = changedValue.replaceAll(">", "&gt;");
                changedValue = changedValue.replaceAll("\\(", "&#40;")
                changedValue = changedValue.replaceAll("\\)", "&#41;");
                changedValue = changedValue.replaceAll("'", "&#x27;");
            }
            return `<div style="position:absolute; top: 10px; text-align: center; font-family: 'Noto Sans KR', sans-serif; vertical-align: middle; width: 100%; height:100%; font-size: 13px;">${changedValue}</div>`;
        },

        //////////////////////////////////////////////////////////////////

        deliveriesProjectForm: function () {
            const _this = this;

            var resultDest = [];
            _this.dests.forEach((dest) => {

                resultDest.push({
                    deliveryId: dest.deliveryId,
                    serialNo: dest.serialNo,
                    warehouseCode: dest.warehouseCode,
                    deliveryTime: TimeUtil.getDateFormattedString(dest.deliveryTime, "YYYY-MM-DD"),
                    dispatchNo: dest.dispatchNo,
                    orderNo: dest.orderNo,
                    driverName: dest.driverName,
                    customerOrderId: dest.customerOrderId,
                    deliveryStatusKor: dest.deliveryStatusKor,
                    deliveryStartTime: dest.deliveryStartTime,
                    deliveryEndTime: dest.deliveryEndTime,
                    eupMyeonDong: dest.eupMyeonDong,
                    address: dest.address,
                    projectName: dest.projectName
                });
            });

            this.destsSource = {
                datatype: "json",
                datafields: [
                    {name: 'deliveryId', type: 'string'},
                    {name: 'serialNo', type: 'string'},
                    {name: 'warehouseCode', type: 'string'},
                    {name: 'deliveryTime', type: 'string'},
                    {name: 'dispatchNo', type: 'number'},
                    {name: 'orderNo', type: 'number'},
                    {name: 'driverName', type: 'string'},
                    {name: 'customerOrderId', type: 'string'},
                    {name: 'deliveryStatusKor', type: 'string'},
                    {name: 'deliveryStartTime', type: 'string'},
                    {name: 'deliveryEndTime', type: 'string'},
                    {name: 'eupMyeonDong', type: 'string'},
                    {name: 'address', type: 'string'},
                    {name: 'projectName', type: 'string'},
                ],
                localdata: resultDest
            };
            _this.dataAdapter = new $.jqx.dataAdapter(this.destsSource);
////////////////////////////
            _this.$refs.destsGrid.source = _this.dataAdapter;
            _this.$refs.destsGrid.refresh();
        },

        destinationslistevent: function (event) {
            //event.args.row.bounddata.deliveryId
            console.log(event.args.row.bounddata.deliveryId);
        },
        //
        // editLocation(deliveryId) {
        //     const _this = this;
        //
        //     DELIVERY_API.getDeliveryForManagement(deliveryId, {
        //         onSuccess: (response) => {
        //             console.log(response);
        //             _this.showDeliveryDetailPop(response.data);
        //         },
        //         onError: (error) => {
        //             console.log(error);
        //         }
        //     })
        // },
        //
        // showDeliveryDetailPop: function (response) {
        //     let dest = {};
        //     dest.delivery = response;
        //     dest.rider = {};
        //     if (response.riderId) {
        //         dest.rider.riderId = response.riderId;
        //         dest.rider.name = response.riderName;
        //         dest.rider = response.riderInfo;
        //         dest.riderInfoList = [{
        //             riderId: dest.rider.riderId,
        //             riderName: dest.rider.name,
        //             groupName: dest.delivery.groupName
        //         }];
        //         dest.groupNameList = dest.delivery.groupName ? [dest.delivery.groupName] : [];
        //     }
        //     dest.projectId = response.projectId;
        //     dest.projectStatus = response.projectStatus;
        //     dest.projectUserId = response.projectUserId;
        //
        //     //안쓸것 같아서 우선 값만 채워 넣음
        //     dest.delivery.x = 127.04485;
        //     dest.delivery.y = 37.5431;
        //
        //     this.sendEventToMap(EVENT.MAP.SHOW_DELIVERY_DETAIL_POPUP, {dest: dest, viewMode: 'r'});
        // },
        //
        // sendEventToMap(event, data) {
        //     try {
        //         const aloaMap = document.getElementById('iframe-map').contentWindow.app.$refs.aloaMap;
        //         aloaMap.$emit(event, data);
        //     } catch (e) {
        //         console.error("sendEventToMap exception: " + e);
        //     }
        // },
    },
};