@charset "UTF-8";
@import url("/assets/common/css/font.css");

* {
    margin: 0px;
    padding: 0px;
    font-family: Noto Sans KR,sans-serif;
    font-weight: 500;
    /*    border: 1px solid;*/

}


/*=====================================================*/
/*=======================sr03==========================*/
/*=====================================================*/

.reservation_setting{
    position: absolute;
    top:50%;
    left: 50%;
    margin-top: -195px;
    margin-left: -240px;
    width: 480px;
    height: 390px;
    background-color: #fff;
    border-radius: 4px;
    /*	border: 1px solid red;*/
}


.reservation_setting .window_close{
    position: absolute;
    top:10px;
    right: 10px;
    width: 30px;
    height: 30px;
    border: 0px solid;
    outline: 0px;
    background-color: #fff;
    background-image: url(/assets/image/prod/popup/bt_popup_close_n.svg);

    /*	border: 1px solid;
    */}


.reservation_setting .title{
    position: relative;
    top: 40px;
    height:30px;
    padding-left: 40px;
    font-weight: 500;
    text-align: left;
    font-family: 'Noto Sans KR', sans-serif;
    font-size: 20px;
    /*	border: 1px solid red;*/
    letter-spacing: -0.8px;
    color: #113E46;

}

.reservation_setting .title_line{
    position: relative;
    height: 1px;
    background-color: #D5D5D5;
}

.reservation_setting .detail_button{
    position: relative;
    top:50px;
    left: 42px;
    width: 80px;
    height: 24px;
    border-radius: 12px;
    line-height: 0px;
    font-weight: 'Noto Sans kr',sans-serif;
    font-size: 12px;
    font-weight: 500;
    background-color: #FF5A00;
    color: #fff;
    outline: none;
    border: 0px solid red;
}

.reservation_setting .detail_button:active{
    opacity: 0.5;
}

.reservation_setting .input_section{
    position: relative;
    width: 420px;
    top: 60px;
    margin: 0 auto;

}
.reservation_setting .left_list_section{
    position: relative;
    width: 390px;
    height: 35px;
    margin: 15px;

}

.reservation_setting .left_list_section_title{
    position: absolute;
    left: 0px;
    width: 80px;
    height: 23px;
    padding-top: 7px;
    padding-right: 5px;
    font-weight: 500;
    text-align: left;
    font-family: 'Noto Sans KR', sans-serif;
    font-size: 13px;
    color: #113E46;
}

.reservation_setting .left_list_text1_line{
    position: absolute;
    top:32px;
    right: 0px;
    width: 290px;
    height: 1px;

    background-color: #D5D5D5;
}


.reservation_setting .left_list_text{
    position: absolute;
    top:1px;
    right: 0px;
    width: 280px;
    height: 35px;
    outline: 0px;
    font-weight: 400;
    font-family: 'Noto Sans KR', sans-serif;
    font-size: 13px;
    color: #113E46;
    padding-left: 10px;
    overflow: hidden;
    background-color: rgba(0,0,0,0);
    border: 1px solid #D5D5D5;
    border-radius: 3px;
}


.reservation_setting select{ -moz-appearance:none; /* Firefox */  -webkit-appearance:none; /* Safari and Chrome */  appearance:none; }




.reservation_setting .left_list_select{
    position: absolute;
    top:1px;
    right: 0px;
    width: 292px;
    height: 36px;
    outline: 0px;
    font-weight: 400;
    font-family: 'Noto Sans KR', sans-serif;
    font-size: 13px;
    color: #113E46;
    padding-left: 10px;
    overflow: hidden;
    background-color: rgba(0,0,0,0);
    border: 1px solid #D5D5D5;
    border-radius: 3px;
    background-image: url(/assets/image/prod/popup/bt_popup_pick_rolldown_n.svg);
    background-repeat: no-repeat;
    background-position: 255px;


}

.reservation_setting .left_list_select .option1{
    height: 30px;
    color: #9F9F9F;
    background-color: #F4F4F4;
}

.reservation_setting .left_list_select .option2{
    height: 30px;
    color: #113E46;
    background-color: #F4F4F4;
}

.reservation_setting .left_list_select option:hover{
    background-color: #E6E6E6;
}





.reservation_setting .submit_button{
    position: absolute;
    bottom: 30px;
    left: 50%;
    margin-left: -130px;
    width: 260px;
    height: 45px;
    border: 0px;
    border-radius: 4px;
    outline: 0px;
    background-color: #C3C3C3;
    font-weight: 500;
    text-align: center;
    font-family: 'Noto Sans KR', sans-serif;
    font-size: 16px;
    color: #fff;
}


.reservation_setting .submit_button:hover{
    background-color: #FF5A00;
}


.reservation_setting .sequence_button {
    position: relative;
    top:1px;
    width: 37px;
    height: 37px;
    float: right;
    outline: none;
    border:0px solid;
    background-image: url(../assets/images/bt_popup_pick_date_n.svg);
    background-size: 100%;

}














.reservation_setting .window_logo{
    position: absolute;
    top:90px;
    left: 60px;
    width: 202px;
    height: 50.5px;
    border: 0px solid;
    outline: 0px;
    background-color: #fff;
    background-image: url("../assets/images/img_logo_login.svg");
}

.reservation_setting .window_email{
    position: absolute;
    top:180px;
    left: 60px;
    width: 460px;
    height: 55px;
    border:1px solid #97A1A3;
    border-radius: 5px;
    padding-left: 20px;
    text-align: left;
    font-weight: 400;
    font-family: 'Noto Sans KR', sans-serif;
    font-size: 14px;
    color: #113E46;
}
.reservation_setting .window_email::placeholder{
    position: absolute;
    text-align: left;
    font-weight: 400;
    font-family: 'Noto Sans KR', sans-serif;
    font-size: 14px;
    color: #97A1A3;
    background-image: url("../assets/images/ic_login_input_email.svg");
    background-repeat: no-repeat;
    background-position: 0px -2px;
}

.reservation_setting .window_pw{
    position: absolute;
    top:240px;
    left: 60px;
    width: 460px;
    height: 55px;
    border:1px solid #97A1A3;
    border-radius: 5px;
    padding-left: 20px;
    text-align: left;
    font-weight: 400;
    font-family: 'Noto Sans KR', sans-serif;
    font-size: 14px;
    color: #113E46;
}

.reservation_setting .window_pw::placeholder{
    position: absolute;
    text-align: left;
    font-weight: 400;
    font-family: 'Noto Sans KR', sans-serif;
    font-size: 14px;
    color: #97A1A3;
    background-image: url("../assets/images/ic_login_input_pw.svg");
    background-repeat: no-repeat;
    background-position: 0px -4px;

}


.reservation_setting .window_button{
    position: absolute;
    top:335px;
    left: 50%;
    width: 280px;
    height: 55px;
    margin-left: -140px;
    background-color: #AEC1C4;
    outline: none;
    border:0px solid;
    border-radius: 4px;
    font-weight: 400;
    font-family: 'Noto Sans KR', sans-serif;
    font-size: 16px;
    color: #fff;
}

.reservation_setting .window_button:hover{
    background-color: #FF5A00;
}