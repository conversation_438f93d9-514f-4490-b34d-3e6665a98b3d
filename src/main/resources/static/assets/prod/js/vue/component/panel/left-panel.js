var leftPanel = {
    template: `
<div class="left_panel_all" v-bind:style="{width : panelWidth + 'px'}">
    <div class="left_panel_area_mask"></div>
    <div class="left_panel_area">
        <div class="left_panel_tabmenu">
            <ul>
                <li id="tab1" v-bind:class="['btnCon', 'btnCon_target_left', tabMode == 1 ? 'btnCon_target' : '']">
                    <div class="btn" @click="clickTabMode(1)">
                        <div class="left_panel_driver_tap"><span>{{ $t("기사") }} {{calculateRiderCount}}</span></div>
                    </div>
                </li>
                <div>
                    <li id="tab2" v-bind:class="['btnCon', 'btnCon_target_right', tabMode == 2 ? 'btnCon_target' : '']">
                        <div class="btn" @click="clickTabMode(2)">
                            <div v-if="calculateDestinationCount < 1000" class="left_panel_stop_tap_1"><span>{{ $t("방문지(shorten)") }} {{calculateDestinationCount}}</span></div>
                            <div v-if="calculateDestinationCount >= 1000" class="left_panel_stop_tap_2"><span style="left:32px">{{ $t("방문지(shorten)") }} {{calculateDestinationCount}}</span></div>
                        </div>
                    </li>
                </div>
            </ul>
        </div>

        <div v-bind:class="[ 'content-info']"  > <!--, { 'content-info-disabled' : !isLogin } -->
            <div class="sidebar_toolbar" v-if="tabMode==1" > <!-- rider 탭 -->
                <div :class="['sidebar_toolbar_search', isRiderSearchButtonEnabled ? 'select' : 'disable']" @click="selectToolbar(1)"  ></div>
                
<!--                로그인 전에는 핀으로 기사를 지도에서 찍을수 있게 한다 -->
                <div v-if = "isAnonymousUser" :class="['sidebar_toolbar_picker_rider', toolbarMode == 2   ? 'select' : '']" @click="selectAddRiderPin()" ></div>                
                <div v-if = "!isAnonymousUser" :class="['sidebar_toolbar_picker_rider', isAddRiderButtonEnabled  ? 'select' : 'disable']" @click="selectAddRider()" ></div>
                                
                <div :class="['sidebar_toolbar_delete', isDeledtedRiderButtonEnabled ? 'select' : 'disable']" @click="selectToolbar(3)" @click="riderDelete()"></div>
                
                <div :class="['sidebar_toolbar_dispatch_rider', isDispatchRiderButtonEnabled ? 'select' : 'disable']" @click="selectDispatchRiderExcel()" :title='$t("엑셀로 가배차 기사를 실배차 기사로 이동")'></div>
                
                <div :class="['sidebar_toolbar_delete', 'disable']"></div>
                <div class="sidebar_toolbar_full_sel_text">{{$t("전체선택")}}</div>
                <input type="checkbox" value="false" class="sidebar_toolbar_full_sel" v-model="checkAllRiders" :disabled="!isEnableToCheckAllRiders" :checked="checkAllRiders == true">
  			        <input type="file" @change="onSelectRiderFiles" style="display: none;" id="selectDispatchRiderExcel" accept=".xls, .xlsx, .csv" />
            </div>
            <div class="sidebar_toolbar" v-if="tabMode==2" > <!-- 방문지 탭 -->
                <div :class="['sidebar_toolbar_search', isDestSearchAndPinButtonEnabled ? 'select': 'disable']" @click="selectToolbar(1)" ></div>
                <div :class="['sidebar_toolbar_picker', toolbarMode == 2 ? 'select': '']" @click="selectAddDestination()" ></div>                
                <div :class="['sidebar_toolbar_delete', isDeledtedDestButtonEnabled ? 'select': 'disable']" @click="selectToolbar(3)" @click="deliveryDelete()"></div>                
                <div :class="['sidebar_toolbar_delete', 'disable']"></div>
                <div class="sidebar_toolbar_full_sel_text">{{$t("전체선택")}}</div>
                <input type="checkbox" value="false" class="sidebar_toolbar_full_sel" v-model="checkAllDestinations" :disabled="!isEnableToCheckAllDestinations" :checked="checkAllDestinations == true">
            </div>

            <!-- Rider Search -->
            <rider-search ref="riderSearch" v-if="tabMode==1 && toolbarMode == 1" :project="project"></rider-search>

            <!-- Rider Pin -->
<!--
            <div class="toolbar_rider_pin_area" v-if="tabMode==1 && toolbarMode == 2">
                <div class="toolbar_top_icon pin"></div>
                    <div>
                <label><input type="radio" name="pinMode" @change="setPinMode(Constant.PINMODE.TRUCK)"/> <div class="toolbar_rider_pin truck"></label>
                <label><input type="radio" name="pinMode" @change="setPinMode(Constant.PINMODE.EV)"/> <div class="toolbar_rider_pin ev"></label>
                <label><input type="radio" name="pinMode" @change="setPinMode(Constant.PINMODE.BIKE)"/> <div class="toolbar_rider_pin bike"></label>
                <label><input type="radio" name="pinMode" @change="setPinMode(Constant.PINMODE.PEDESTRIAN)"/> <div class="toolbar_rider_pin pede"></label>
            </div>
                    <div>
                        <div class="toolbar_rider_pin text">&nbsp화물</div>
                        <div class="toolbar_rider_pin text1">&nbspEV승용</div>
                        <div class="toolbar_rider_pin text">이륜</div>
                        <div class="toolbar_rider_pin text">도보</div>
                    </div>
            </div>
-->

            <!-- Destination Search -->
            <address-search ref="addressSearch" v-if="tabMode==2 && toolbarMode == 1" :project="project"></address-search>



<!-- ==================픽업 드롭오프 핀 Ui======================= -->
<!--
            <div class="toolbar_delivery_pin_area" v-if ="isNewSelectDestinationPinMode && isAnonymousUser && tabMode==2 && toolbarMode == 2">    
              <div class="curser"></div>    
                <div class="truck_type_area">
                  <button class="truck_icon_n_1" ></button>
                  <button class="truck_icon_n_2"></button>
                  <button class="truck_icon_n_3"></button>
                  <button class="truck_icon_n_4"></button>
                </div>
                <div class="pickup_function_area">
                  <button :class="selectedPin == Constant.PINMODE.HUB ? 'function_bt_p_1' : 'function_bt_n_1' " @click="setPinMode(Constant.PINMODE.HUB)"></button>
                  <button :class="selectedPin == Constant.PINMODE.BRANCH ? 'function_bt_p_2' : 'function_bt_n_2' "  @click="setPinMode(Constant.PINMODE.BRANCH)"></button>
                  <button :class="selectedPin == Constant.PINMODE.DESTINATION ? 'function_bt_p_3' : 'function_bt_n_3' "  @click="setPinMode(Constant.PINMODE.DESTINATION)"></button>
                  <button :class="selectedPin == Constant.PINMODE.FIRST_MILE_PICKUP || selectedPin == Constant.PINMODE.HAILING_PICKUP ? 'function_bt_p_4' : 'function_bt_n_4' " @click="setPinMode(Constant.PINMODE.FIRST_MILE_PICKUP)"></button>
                  <button :class="selectedPin == Constant.PINMODE.FIRST_MILE_DROPOFF || selectedPin == Constant.PINMODE.HAILING_DROPOFF ? 'function_bt_p_5' : 'function_bt_n_5'"  @click="setPinMode(Constant.PINMODE.FIRST_MILE_DROPOFF)"></button>
                </div>
                
                <div class="etc_type_area">
                  <button class="bike_icon_n_1"></button>
                  <button class="bike_icon_n_2"></button>
                </div>
                <div class="pickup_function_area_2">
                  <button :class="selectedPin == Constant.PINMODE.LAST_MILE_PICKUP ? 'function_bt_p_4' : 'function_bt_n_4' "  @click="setPinMode(Constant.PINMODE.LAST_MILE_PICKUP)"></button>                    
                  <button :class="selectedPin == Constant.PINMODE.LAST_MILE_DROPOFF ? 'function_bt_p_5' : 'function_bt_n_5' " @click="setPinMode(Constant.PINMODE.LAST_MILE_DROPOFF)"></button>                    
                </div>
            </div>   
-->
<!-- ==================픽업 드롭오프 핀 Ui======================= -->
            <!-- Destination Pin -->
<!--
            <div :class=" isAnonymousUser ? 'toolbar_dest_pin_area_demo' :  'toolbar_dest_pin_area'" v-if="!isNewSelectDestinationPinMode && tabMode==2 && toolbarMode == 2"   >
                <div class="toolbar_top_icon pin"></div>
                    <div>
                    <label><input type="radio" name="pinMode" @change="setPinMode(Constant.PINMODE.HUB)"/> <div class="toolbar_dest_pin whouse"></label>
                    <label><input type="radio" name="pinMode" @change="setPinMode(Constant.PINMODE.BRANCH)"/> <div class="toolbar_dest_pin branch"></label>
                    <label><input type="radio" name="pinMode" @change="setPinMode(Constant.PINMODE.DESTINATION)"/> <div class="toolbar_dest_pin dest"></label>
                    </div>
                    <div>
                        <div class="toolbar_dest_pin text">&nbsp&nbsp물류센터</div>
                        <div class="toolbar_dest_pin text">물류거점</div>
                        <div class="toolbar_dest_pin text">방문지</div>
                    </div>    
                    <div>
                    <label v-if="isAnonymousUser" ><input type="radio" name="pinMode" @change="setPinMode(Constant.PINMODE.HAILING_PICKUP)"/> <div class="toolbar_dest_pin pickup"></label>
                    <label v-if="isAnonymousUser" ><input type="radio" name="pinMode" @change="setPinMode(Constant.PINMODE.HAILING_DROPOFF)"/> <div class="toolbar_dest_pin dropoff"></label>
            </div>
                    <div>
                        <div v-if="isAnonymousUser" class="toolbar_dest_pin text1">픽업</div>
                        <div v-if="isAnonymousUser" class="toolbar_dest_pin text2">드롭오프</div>
                    </div>                    
                    
            </div>
-->
            <!-- Excel File Drop -->
            <drop @drop="onDropFiles" class="drop">
                <div v-bind:class="[left_panel_dragNdrop , isDragging ? 'drag-over' : ''  ]" v-if="Util.isAdmin() == false && (tabMode == 1 && project.destinations.length === 0 || tabMode == 2 && project.destinations.length === 0)"
                    v-on:dragover="isDragging=true"
                    v-on:dragleave="isDragging=false" >
                    <div class="left_panel_dragNdrop_icon">
                        <img src="/assets/image/leftpanel/img_list_xls.svg">
                    </div>
                    <div v-bind:class="left_panel_dragNdrop_text1">
                        {{ $t("여기로 파일을 끌어놓으세요.") }}
                    </div>
                    <div>
                        <input type="file" @change="onSelectFiles" style="display: none;" id="selectExcel" multiple="multiple" accept=".xls, .xlsx, .csv" />
                    </div>
                    <div v-bind:class="left_panel_dragNdrop_text2" @click="document.getElementById('selectExcel').click();">
                        {{ $t("내 컴퓨터에서 찾기") }}
                    </div>
                    <button @click="onDownloadSample"><img src="/assets/image/leftpanel/icon_list_down.svg"> {{ $t("샘플 다운로드") }}</button>
                </div>
            </drop>

            <!-- driver list panel -->
            <div class="left_panel_list_area_rider" v-show="tabMode==1 && (project.destinations.length !== 0 || project.riders.length  !== 0)">
                <jqx-tree-rider ref="riderTree" @checkChange="onCheckChange($event)" 
                        @dragStart="onDragStart($event)" @dragEnd="onDragEnd($event)"
                        @itemClick="onItemClick($event)"
                        @expand="onTreeItemExpand($event)"
                        :width="'232px'" 
                        :height="'calc(100vh - 216px)'"
                        :dragEnd="dragEnd"
                        :checkboxes="true"
                        :allowDrag="true"
                        :allowDrop="true"
                        :source="treeSource">
                </jqx-tree-rider>
            </div>

            <!-- destination list panel -->
            <div class="left_panel_list_area_dest"  v-show="tabMode==2 && project.destinations.length !== 0">
                <jqx-tree-dest ref="destTree" 
                        @rowCheck="onDestItemCheck($event)" 
                        @rowUncheck="onDestItemUncheck($event)" 
                        @rowExpand="onDestItemExpand($event)"
                        @rowCollapse="onDestItemCollapse($event)"
                        @rowClick="onDestItemClick($event)"
                        @rowSelect="onDestItemSelect($event)"
                        @rowUnselect="onDestItemUnselect($event)"
                        @rowDoubleClick="onRowDoubleClick($event)"
                        :width="'232px'" 
                        :height="'calc(100vh - 216px)'"
                        :enableHover="false"
                        :checkboxes="true"
                        :hierarchicalCheckboxes="true"
                        :selectionMode="'singleRow'"
                        :showHeader="false"
                        :columns="treeDestColumns"
                        :source="treeDestAdapter">
                </jqx-tree-dest>
            </div>
            
<!--            <div class="left_panel_bottom1">-->
<!--                <jqx-buttons width=100 height=30 :value="buttonText" theme="darkblue" @click="showDeliveryEditPopup(project.riders, project.destinations)"/> -->
<!--            </div>-->
            
            <!-- bottom display -->
            <div class="left_panel_bottom">
                <div class="left_panel_bottom_text1">{{calculateCompletedDestinationCount}}/{{calculateDestinationCount}}</div>
                <div class="left_panel_bottom_text3" style="left: 70px;">{{$t("완료")}}</div>
                <div class="left_panel_bottom_text2">{{calculateFailureDestinationCount}}</div>
                <div class="left_panel_bottom_text3" style="left: 165px;">{{$t("실패")}}</div>
                <div v-if="project.destinations.length !== 0" class="show_grid_popup_button" style="left: 200px;" @click="showDeliveryEditPopup()"></div>
            </div>
        </div>
    </div>
                
    <div class="left_panel_top_menuSlide" v-bind:style="toggleBtnContainerStyle" v-if="isShowDestListPanelToggleButton" >
        <image-button v-bind:image = "toggleButtonImage"   @button-clicked="toggleDestListPanel"/>
    </div>

    <transition name="slide">
        <destination-list-panel ref="destinationListPanel" v-if="viewMode.isShowDestListPanel" v-bind:riders = "selectedRiders"  />
    </transition>
</div>
    `,

    props: {
        isShow: false,
    },

    data: function () {
        return {
            eventHandlers: [
                // {event: EVENT.PANEL.UPDATE, handler: this.updateProject}, //watch제거
                {event: EVENT.PANEL.SHOW, handler: this.showLeftPanel},
                {event: EVENT.PANEL.HIDE, handler: this.hideLeftPanel},
                {event: EVENT.PANEL.HIDE_DEST_LIST, handler: this.hideDestListLeftPanel},
                {event: EVENT.PANEL.INIT_LEFT_PANEL_TAB, handler: this.initLeftPanelTab},
                {event: EVENT.PANEL.INIT_TOOLBARMODE, handler: this.initToolbarMode},
                {event: EVENT.PANEL.UPDATE_DATA, handler: this.updateData},
                {event: EVENT.PANEL.SELECT_FILES, handler: this.onSelectFiles},
                {event: EVENT.PANEL.SHOW_DELIVERY_DETAIL_POPUP, handler: this.showDeliveryDetailPopupByDestList},
                {event: EVENT.PANEL.INIT_LEFT_PANEL_TREE_SOURCES, handler: this.initTreeSource},
                {event: EVENT.PANEL.SET_CHANGE_RIDER_STATUS, handler: this.setChangeRiderStatus},
                {event: EVENT.PANEL.SET_CHANGE_DELIVERY_STATUS, handler: this.setChangeDeliveryStatus},
                {event: EVENT.PANEL.STOP_CALCULATING_LATENCY, handler: this.stopCalculatingLatency},
                {event: EVENT.PANEL.SELECTION_MODE_CHANGED, handler: this.onSelectionModeChanged},
                {event: EVENT.PANEL.IS_ROUTE_LINE_DISPLAY, handler: this.onIsRouteLineDisplay},
            ],
            project: {
                "riders": [],
                "destinations": [],
                "hubTotalCount": 0,
                "branchTotalCount": 0,
                "lastDestinationTotalCount": 0,
                "riderTotalCount": 0,
                "isAnonymous": true,
            },
            tabMode: 1,         // (1: 기사, 2: 방문지)
            toolbarMode: 0,     // (0: 선택안함, 1: 검색, 2:pin)
            isDragging: false,
            viewMode: {
                isShowDestListPanel: false,  //방문지 리스트 보이기 모드 //ywlee2020.04.03
                isHide: false,       // Full Map 모드
                NORMAL: {
                    panelWidth: Constant.LEFT_PANEL_CONTENT_INFO_WIDTH + Constant.LEFT_PANEL_TOGGLE_BUTTON_WIDTH,
                    contentWidth: Constant.LEFT_PANEL_CONTENT_INFO_WIDTH
                },
                SHOW_DEST_LIST: {
                    panelWidth: Constant.LEFT_PANEL_CONTENT_INFO_WIDTH + Constant.LEFT_PANEL_DEST_LIST_PANEL_WIDTH + Constant.LEFT_PANEL_TOGGLE_BUTTON_WIDTH,
                    contentWidth: Constant.LEFT_PANEL_CONTENT_INFO_WIDTH + Constant.LEFT_PANEL_DEST_LIST_PANEL_WIDTH
                },
                HIDE: {panelWidth: 0, contentWidth: 0}
            },
            isDisabled: false,

            demoRoutingMode: Constant.DEFAULT_SETTING.ROUTING_MODE,//사용 금지 - Global로 사용하자
            selectedPin: -1,

            language: Constant.DEFAULT_SETTING.LANGUAGE,

            isNewSelectDestinationPinMode: false,
            buttonText: "DeliveryEdit",

            unCheckChildren: [],
            unCheckRiderChildren: [],
            unCheckDestChildren: [],

            treeSource: null,

            treeDestAdapter: new jqx.dataAdapter(this.treeDestSource),
            treeDestColumns: [
                {text: 'Label', dataField: 'label', width: 200, cellsRenderer: this.cellsrender}
            ],

            gridTreeSource: null,

            latencyIntervalId: null,

            zipCodesSort: [],
            isFullCheckedFlag: false,

            isNotAssignmentDestinationsData: [],

            isRouteLineDisplay : false,
        };
    },

    beforeCreate: function () {
        this.treeDestSource = {
            dataType: 'json',
            dataFields: [
                {name: 'id', type: 'string'},
                {name: 'label', type: 'string'},
                {name: 'customerOrderId', type: 'string'},
                {name: 'items', type: 'array'}
            ],
            hierarchy: {
                root: "items"
            },
            id: 'id',
            localData: []
        };
    },

    watch: {
        isShow: function (value) {
            console.log('[watch] isShow : ' + value);
            this.$emit('change-width', this.contentWidth);
        }
    },

    computed: {
        isAnonymousUser: function () {
            return this.project.isAnonymous;
        },

        panelWidth: function () {
            return this.getViewMode().panelWidth;
        },

        contentWidth: function () {
            return this.getViewMode().contentWidth;
        },

        toggleBtnContainerStyle: function () {
            let objStyle;
            let left = this.viewMode.isShowDestListPanel ? Constant.LEFT_PANEL_CONTENT_INFO_WIDTH + Constant.LEFT_PANEL_DEST_LIST_PANEL_WIDTH : Constant.LEFT_PANEL_CONTENT_INFO_WIDTH;
            objStyle = {left: left + 'px'};
            if (!this.isShowDestListPanelToggleButton) {
                objStyle.visibility = 'hidden';
            }
            return objStyle;
        },
        toggleButtonImage: function () {
            return this.viewMode.isShowDestListPanel ? "/assets/image/leftpanel/bt_leftlist_close.svg" : "/assets/image/leftpanel/bt_leftlist_open.svg";
        },

        riders: function () {
            console.log("leftPanel : computed : riders");
            let resultRider = [];
            let _this = this;

            this.project.riders.forEach((rider) => {
                const destinationsLength = rider.destinations == undefined ? 0 : rider.destinations.length;

                resultRider.push({
                    riderId: rider.riderId,
                    name: rider.name,
                    status: rider.status,
                    destSize: destinationsLength
                    ,
                    statusDesc: rider.status
                    ,
                    workStatus: rider.workStatus
                    ,
                    time: destinationsLength > 0 ? rider.destinations.map(d => d.totalTime).reduce((p, c) => p + c) : 0
                    ,
                    distance: destinationsLength > 0 ? rider.destinations.map(d => d.totalDistance).reduce((p, c) => p + c) : 0
                    ,
                    x: rider.x,
                    y: rider.y
                    ,
                    isSelected: rider.isSelected
                    ,
                    milesType: rider.milesType
                    ,
                    type: Util.getVehicleTypeName(rider.type)
//                    , completedDestinationCount: _this.getRiderCompletedDestinationCount(rider)
                    ,
                    completeDeli: RiderUtil.getCompleteDeliveryCount(rider)//rider.completeDeliveryCount
                    ,
                    totalDeli: RiderUtil.getTotalDeliveryCount(rider)   //rider.totalDeliveryCount
                    ,
                    note: rider.note
                    ,
                    gid: rider.gid        // remine-#1138
                    ,
                    vehicle: rider.vehicle
                    ,
                    mobile: rider.mobile
                    ,
                    groupName: rider.groupName
                    ,
                    projectId: this.project.id
                    ,
                    workingStartAddress: rider.workingStartAddress
                    ,
                    workingEndAddress: rider.workingEndAddress
                    ,
                    workingStartTime: rider.workingStartTime
                    ,
                    workingEndTime: rider.workingEndTime
                    ,
                    lunchStartTime: rider.lunchStartTime
                    ,
                    lunchEndTime: rider.lunchEndTime
                    ,
                    skillLevel: rider.skillLevel
                    ,
                    position: rider.position
                    ,
                    destinations: rider.destinations
                    ,
                    latency: (rider.latency === null || rider.latency === undefined) ? 0 : rider.latency
                    ,
                    isConnectedToMobileApp: rider.isConnectedToMobileApp
                    ,
                    colorIndex: rider.colorIndex
                    ,
                    isWorkCompleted: rider.isWorkCompleted
                    ,
                    dispatchStatus: rider.dispatchStatus
                    ,
                    projectPushedAt: rider.projectPushedAt
                    ,
                });
            });

            return resultRider;

        },

        alloctions: function () {
            let destinations = this.project.destinations;
            let resultDests = [];
            this.project.riders.forEach((rider) => {
                resultDests.push({isRider: true, id: rider.riderId, x: rider.x, y: rider.y, type: rider.type});
                Array.prototype.push.apply(resultDests, rider.destinations);
            });

            const noMatchDests = destinations.filter(dest => {
                let isNotFound = true;
                resultDests.forEach(rDest => {
                    if (rDest.id == dest.id) {
                        isNotFound = false;
                    }
                });
                return isNotFound;
            });

            if (noMatchDests.length > 0) {
                resultDests.push({isRider: true, id: 'unknown', x: null, y: null, type: null});
                Array.prototype.push.apply(resultDests, noMatchDests);
            }
            return resultDests;
        },

        selectedRiders: function () {
            return this.project.riders == undefined ? [] : this.project.riders.filter(rider => rider.isSelected);
        },

        selectedDestinations: function () {
            return this.project.destinations == undefined ? [] : this.project.destinations.filter(destination => destination.isSelected);
        },

        calculateRiderCount: function () {
            return this.project.riders == undefined ? 0 : this.project.riders.length;
        },

        calculateCompletedDestinationCount: function () {
            let sum = 0;

            for (let rider of this.project.riders) {
                sum += this.getRiderDestinationCountByDeliveryStatus(rider, Constant.DELIVERY_STATUS.COMPLETED);
            }
            return sum;
        },

        calculateFailureDestinationCount: function () {
            let sum = 0;

            for (let rider of this.project.riders) {
                sum += this.getRiderDestinationCountByDeliveryStatus(rider, Constant.DELIVERY_STATUS.FAILURE);
            }
            return sum;
        },

        calculateDestinationCount: function () {
            return this.project.destinations == undefined ? 0 : this.project.destinations.length;
        },

        /**
         * 프로젝트 전체 누적 시간 계산
         */
        calculateAccumulatedHours: function () {
            let accumulatedHours = 0;

            for (let rider of this.project.riders) {
                const destinationsLength = rider.destinations == undefined ? 0 : rider.destinations.length;
                accumulatedHours += destinationsLength > 0 ? rider.destinations.map(d => d.totalTime).reduce((p, c) => p + c) : 0;
                accumulatedHours += destinationsLength > 0 ? rider.destinations.map(d => d.serviceDuration).reduce((p, c) => p + c) : 0;
            }
            return Util.roundNumber(accumulatedHours / 3600, 1);
        },

        /**
         * 프로젝트 전체 누적 거리 계산
         */
        calculateAccumulatedDistance: function () {
            let accumulatedDistance = 0;

            for (let rider of this.project.riders) {
                const destinationsLength = rider.destinations == undefined ? 0 : rider.destinations.length;
                accumulatedDistance += destinationsLength > 0 ? rider.destinations.map(d => d.totalDistance).reduce((p, c) => p + c) : 0;
            }

            let precision = 0;
            if (accumulatedDistance < 1000 * 100) //100 km 이하 일때만 2째자리까지 표현함
                precision = 2;
            return Util.roundNumber(accumulatedDistance / 1000, precision);
        },

        isEnableToCheckAllRiders: function () {
            return this.project.riders && this.project.riders.length > 0;
        },

        checkAllRiders: {
            get() {
                return this.isEnableToCheckAllRiders && (this.selectedRiders.length == this.project.riders.length);
            },
            set(on) {

                // tree view에서 체크 박스 필요함.
                if (on == true)
                    this.checkAll();
                else
                    this.uncheckAll();

                this.project.riders.filter(rider => rider.isSelected != on).forEach((rider) => {
                    rider.isSelected = on;
                });

                this.sendEventToMap(EVENT.MAP.ALL_RIDERS_SELECTED, on);

                //top tool bar 값을 전달함.
                this.sendToTopToolbarRiderSelect();
            },
        },

        isEnableToCheckAllDestinations: function () {
            return this.project.destinations && this.project.destinations.length > 0;
        },

        checkAllDestinations: {
            get() {
                return this.isEnableToCheckAllDestinations && (this.selectedDestinations.length == this.project.destinations.length);
            },
            set(on) {

                // dest view에서 체크 박스 필요함.
                if (on == true)
                    this.checkDestAll();
                else
                    this.uncheckDestAll();

                this.sendEventToMap(EVENT.MAP.ALL_DESTINATIONS_SELECTED, on);
            },
        },

        isShowDestListPanelToggleButton: function () {
            return (this.viewMode.isShowDestListPanel ||
                (this.selectedRiders.length > 0 && this.getSelectedRiderDestCount() > 0) ||
                (this.project.destinations != undefined && this.project.destinations.length > 0)
            );
        },

        //현재 운행중인 차량의 갯수를 구한다.
        vehicleCountOnDuty: function () {
            let onDutyCount = 0;
            this.project.riders.forEach(rider => {
                if (rider.status === Constant.RIDER_STATUS.DISPATCHED || rider.status === Constant.RIDER_STATUS.GOING)
                    onDutyCount++;
            });

            return onDutyCount;
        },

        left_panel_dragNdrop: function () {
            return this.addSuffixToClassInKorean('left_panel_dragNdrop');
        },
        left_panel_dragNdrop_text1: function () {
            return this.addSuffixToClassInKorean('left_panel_dragNdrop_text1');
        },
        left_panel_dragNdrop_text2: function () {
            return this.addSuffixToClassInKorean('left_panel_dragNdrop_text2');
        },

        isLogin: function () {
            const userInfo = this.$store.getters.getLoginUserInfo;
            let isLogin = false;
            if (userInfo) {
                isLogin = true;
            }

            return isLogin;
        },

        isAddRiderButtonEnabled: function () {
            return (!this.isDisabled /*&& this.project.destinations.length > 0*/);
        },

        isRiderSearchButtonEnabled: function () {
            return (!this.isDisabled /* && this.project.destinations.length>0 */);
        },

        isDeledtedRiderButtonEnabled: function () {
            if (!this.isDisabled) {
                var isEnabled = false;
                this.project.riders.forEach((rider) => {
                    if (rider.isSelected) {
                        isEnabled = true;
                    }
                });

                if (isEnabled)
                    return true;
                else
                    return false;
            } else
                return false;
        },

        isDispatchRiderButtonEnabled:function () {
            if (Util.isHyundaiUserCompany() ||  Util.isWelStoryUserCompany() || Util.isToHomeUserCompany()) {
                if (_.every(this.project.riders, {isSimulationRider: false}))
                    return false;
                else if (this.project.riderTotalCount === 0)
                    return false;
                else
                    return true;
            } else {
                return false;
            }
        },

        isDestSearchAndPinButtonEnabled: function () {
            return (!this.isDisabled /*&& this.project.destinations.length>0 */);
        },

        isDeledtedDestButtonEnabled: function () {
            if (!this.isDisabled) {
                var isEnabled = false;
                this.project.destinations.forEach(destination => {
                    if (destination.isSelected) {
                        isEnabled = true;
                    }
                });

                if (isEnabled)
                    return true;
                else
                    return false;
            } else
                return false;
        },
    },

    created: function () {
        EVENT.registerEventHandler(this, this.eventHandlers);
        Vue.use(VueDragDrop);
    },

    mounted() {
        console.log('left-panel mounted');
    },

    beforeUpdate: function () {
    },

    updated() {
    },

    methods: {
        getDriverListIconWidth(completeDeli, totalDeli) {
            let txt = completeDeli + '/' + totalDeli;
            let offsetWidth = 8 * txt.length;
            let percent = (90 - offsetWidth - 18) * 100 / 90;
            return percent;
        },

        toggleDestListPanel: function () {
            this.viewMode.isShowDestListPanel = !this.viewMode.isShowDestListPanel;
            this.$emit('change-width', this.contentWidth);
        },

        clickTabMode: function (tabMode) {
            this.tabMode = tabMode;
            const _this = this;

            if (this.tabMode == 2) {
                this.destinationsTreeDisplay();
            }
            ////////////////////////////////////////////////////////////////////////////////////////////////////
            else {
                this.riderTreeDisplay();
            }

            this.clearToolbarSelect();
        },

        initLeftPanelTab: function () {
            this.tabMode = 1;
            this.clearToolbarSelect();
        },

        initToolbarMode: function () {
            this.toolbarMode = 0;
        },

        clearToolbarSelect: function () {
            this.toolbarMode = 0;
            this.clearPinMode();
        },

        clearPinMode: function () {
            this.sendEventToMap(EVENT.MAP.SELECTION_MODE_CHANGED, 0);
            this.sendEventToMap(EVENT.MAP.SET_PIN_MODE, Constant.PINMODE.DEFAULT);
        },

        selectAddRider: function () {
            if (this.isAddRiderButtonEnabled) {
                this.sendEventToMap(EVENT.MAP.LEFT_PANEL_ADD_RIDER, null);
            }
        },

        selectAddRiderPin: function () {
            if (this.toolbarMode !== 2) {
                this.toolbarMode = 2;
                this.setPinMode(Constant.PINMODE.RIDER_LAST);
                PopupUtil.showNotificationPopup("추가할 기사 위치를 지도 위에 클릭해주세요.");
            } else {
                this.toolbarMode = 0;
                this.clearPinMode();
                PopupUtil.showNotificationPopup("기사 추가 모드가 해지되었습니다.");
            }
        },

        selectAddDestination: function () {
            if (!this.isDestSearchAndPinButtonEnabled)
                return;

            if (this.toolbarMode !== 2) {
                this.toolbarMode = 2;
                this.setPinMode(Constant.PINMODE.DESTINATION);
                PopupUtil.showNotificationPopup("추가 배송지의 위치를 지도 위에 클릭해주세요.");
            } else {
                this.toolbarMode = 0;
                this.clearPinMode();
                PopupUtil.showNotificationPopup("배송지 추가 모드가 해지되었습니다.");
            }

        },

        selectToolbar: function (toolbarMode) {
            if (this.toolbarMode !== toolbarMode) {
                this.toolbarMode = toolbarMode;
            } else {
                this.toolbarMode = 0;
            }

            // toolbarMode가 pin이 아닐 경우 pin 관련 값 초기화
            if (this.toolbarMode !== 2) {
                this.clearPinMode();
            }
        },


        updateProject: function (project) {

            console.log("updateProject left-panel " + project);
            this.project.riders = []; // riders업데이트를 위해서 참조값 자체가 바뀌어야 업데이트가 일어나서 값 자체를 한번 바꿔준다.
            // MapUtil.mergeProject(this.project, project);
            MapUtil.copyProject(this.project, project);//this.project의 기사/배송지는 속성값만 복사해서 가지고 있어야 한다.

            this.isDisabled = false;
            if (Util.isAdmin() || !Util.isThisProjectMine(project) || this.project.status === Constant.PROJECT_STATUS.DONE || this.project.attribute.isReadOnly) {
                this.isDisabled = true;
            }

            if (this.project.isAnonymous)
                this.isDisabled = false;
        },

        getRiderDestinationCount: function (rider) {
            return rider ? rider.destSize : 0;
        },

        getRiderDestinationCountByDeliveryStatus: function (rider, deliveryStatus) {
            if (rider.destinations != null) {
                try {
                    const completedDestinations = rider.destinations.filter(destination => destination.deliveryStatus === deliveryStatus);
                    return completedDestinations.length;
                } catch (e) {
                    console.error("getRiderCompletedDestinationCount(): " + e);
                }
            }
            return 0;
        },

        onDropFiles(data, event) {
            event.preventDefault();
            const files = event.dataTransfer.files;
            this.dropOrSelectFiles(files);
        },

        onSelectFiles(e) {
            const files = e.target.files;
            this.dropOrSelectFiles(files);
            Util.clearFileInput(e.target);  // #1422
        },

        dropOrSelectFiles(files) {
            const firstFilename = files[0].name;
            let firstFileExt = firstFilename.substring(firstFilename.lastIndexOf('.'));
            if (firstFileExt.toLowerCase() === '.xls' || firstFileExt.toLowerCase() === '.xlsx') {
                if (files.length > 1) {
                    PopupUtil.alertPopup('2개 이상 엑셀 파일 업로드는 지원하지 않습니다.');
                } else {
                    this.sendEventExcelDrop(files[0]);
                }
            } else if (firstFileExt.toLowerCase() === '.csv') {
                if (files.length === 1) {
                    if (firstFilename.toLowerCase() !== "기사.csv" && firstFilename.toLowerCase() !== "방문지.csv") {
                        PopupUtil.alertPopup('CSV 파일의 이름은 기사.csv이거나 방문지.csv여야 합니다.');
                    } else {
                        this.sendEventCsvDrop(files);
                    }
                } else if (files.length > 2) {
                    PopupUtil.alertPopup('3개 이상 CSV 파일 업로드는 지원하지 않습니다.');
                } else {
                    const secondFilename = files[1].name;
                    let secondFileExt = secondFilename.substring(secondFilename.lastIndexOf('.'));
                    if (secondFileExt.toLowerCase() !== '.csv') {
                        PopupUtil.alertPopup('엑셀 파일은 1개, CSV 파일은 2개의 파일 업로드만 지원합니다.');
                    } else if ((firstFilename.toLowerCase() !== "기사.csv" || secondFilename.toLowerCase() !== "방문지.csv")
                        && (firstFilename.toLowerCase() !== "방문지.csv" || secondFilename.toLowerCase() !== "기사.csv")) {
                        PopupUtil.alertPopup('CSV 파일의 이름은 각각 기사.csv, 방문지.csv여야 합니다.');
                    } else {
                        this.sendEventCsvDrop(files);
                    }
                }
            } else {
                PopupUtil.alertPopup('엑셀이나 CSV가 아닌 파일 업로드는 지원하지 않습니다.');
            }
        },

        onDownloadSample() {
            API.downloadFileByGet(Url.WEB.PROJECT_EXCEL, null, null, null, {
                onSuccess: (response) => {
                },
                onError: (error) => {
                    if (error.response.data.errorMessage) {
                        PopupUtil.alertPopup(error.response.data.errorHeader, error.response.data.errorMessage, () => {
                        });
                    } else {
                        PopupUtil.alertPopup(error.response.data);
                    }
                }
            });
        },

        sendEventExcelDrop(file) {
            this.isDragging = false;

            const aloaMap = document.getElementById('iframe-map').contentWindow.app.$refs.aloaMap;

            let data = {
                'projectId': this.project.id,
                'file': file,
                'tabMode': this.tabMode,
                'riderCount': this.project.riders.length,
                'destinationCount': this.project.destinations.length,
                'name': this.project.name
            };
            aloaMap.$emit(EVENT.MAP.PROJECT_EXCEL_DROP, data);
        },

        sendEventCsvDrop(files) {
            this.isDragging = false;

            const aloaMap = document.getElementById('iframe-map').contentWindow.app.$refs.aloaMap;

            let data = {
                'projectId': this.project.id,
                'files': files,
                'tabMode': this.tabMode
                ,
                'riderCount': this.project.riders.length,
                'destinationCount': this.project.destinations.length,
                'name': this.project.name
            };
            aloaMap.$emit(EVENT.MAP.PROJECT_CSV_DROP, data);
        },

        getViewMode() {
            let viewMode = this.viewMode.NORMAL;
            if (this.viewMode.isHide)
                viewMode = this.viewMode.HIDE;
            else if (this.viewMode.isShowDestListPanel)
                viewMode = this.viewMode.SHOW_DEST_LIST;
            return viewMode;
        },
        showLeftPanel() {
            this.viewMode.isHide = false;
            this.$emit('change-width', this.contentWidth);
        },
        hideLeftPanel() {
            this.viewMode.isHide = true;
            this.$emit('change-width', this.contentWidth);
        },

        hideDestListLeftPanel() {
            this.viewMode.isShowDestListPanel = false;
            this.$emit('change-width', this.contentWidth);
        },

        vehicleTypeClassObject: function (type) {
            let objectClass;
            if (type === Util.getVehicleTypeName(Constant.RiderType.TRUCK))
                objectClass = 'left_panel_driver_driverType1';
            else if (type === Util.getVehicleTypeName(Constant.RiderType.BIKE))
                objectClass = 'left_panel_driver_driverType2';
            else if (type === Util.getVehicleTypeName(Constant.RiderType.PEDE))
                objectClass = 'left_panel_driver_driverType3';
            else if (type === Util.getVehicleTypeName(Constant.RiderType.EV))
                objectClass = 'left_panel_driver_driverType4';

            return objectClass;
        },

        selectRider: function (rider) {
            rider.isSelected = !rider.isSelected;

            if(this.isRouteLineDisplay)
                this.sendEventToMap(EVENT.MAP.RIDER_SELECTED, rider);
        },

        selectDestination: function (dest) {
            dest.isSelected = !dest.isSelected;
            this.sendEventToMap(EVENT.MAP.DESTINATION_SELECTED, dest);
        },

        showDriverDetailPop: function (rider, viewMode) {
            if (!rider) {
                rider = new Object();
            }
            this.sendEventToMap(EVENT.MAP.SHOW_DRIVER_DETAIL_POPUP, {rider: rider, viewMode: viewMode, projectName : this.project.name});
        },
        showDeliveryDetailPop: function (dest, viewMode) {
            let selectedRnD = {};

            if (viewMode == 'r') {
                selectedRnD.delivery = dest;
                let riderInfo = null;
                for (rider of this.project.riders) {
                    if (rider.riderId == dest.riderId) {
                        riderInfo = rider;
                        break;
                    }
                }
                selectedRnD.rider = riderInfo;
            }

            this.sendEventToMap(EVENT.MAP.SHOW_DELIVERY_DETAIL_POPUP, {dest: selectedRnD, viewMode: viewMode});

        },
        riderDelete: function () {
            let selectedRiderList = [];

            this.project.riders.forEach((rider) => {
                if (rider.isSelected) {
                    selectedRiderList.push(rider.riderId);
                }
            });

            if (selectedRiderList.length > 0) {
                let _this = this;
                PopupUtil.confirmPopup("선택한 기사의 경로와 배차를 취소하고 현재 프로젝트에서 제외합니다.", {
                    onConfirm: () => {
                        _this.sendEventToMap(EVENT.MAP.REMOVE_SELECTED_RIDERS, selectedRiderList);

                        // TMS_WEB_API.deleteRiderBelongToProject(selectedRiderList, _this.project.id, {
                        // 	onSuccess: (response) => {
                        // 		//삭제후 project갱신..... 나중에는 rider만 갱신하도록 바꾸자....
                        // 		_this.sendEventToMap(EVENT.MAP.PROJECT_LOAD, _this.project.id)
                        // 		PopupUtil.showNotificationPopup( "삭제 하였습니다.");
                        // 	},
                        // 	onError: (error) => {
                        // 		//alert('롸이더및 vehicle 저장 실패');
                        // 	}
                        // });
                    },
                });
            } else {
                PopupUtil.showNotificationPopup("선택 목록이 없습니다.");
            }
        },


        deliveryDelete: function () {
            let selectedDeliveryList = [];

            this.project.destinations.forEach(destination => {
                if (destination.isSelected) {
                    selectedDeliveryList.push(destination.deliveryId);
                }
            });

            if (selectedDeliveryList.length > 0) {
                let _this = this;
                PopupUtil.confirmPopup("계획된 배송/픽업 업무를 취소하고 선택한 방문지들을 삭제합니다.", {
                    onConfirm: () => {
                        _this.sendEventToMap(EVENT.MAP.REMOVE_SELECTED_DESTINATIONS, selectedDeliveryList);

                        // TMS_WEB_API.deleteDelivery(selectedDeliveryList, _this.project.id, {
                        // 	onSuccess: (response) => {
                        // 		//삭제후 project갱신..... 나중에는 delivery만 갱신하도록 바꾸자....
                        // 		_this.sendEventToMap(EVENT.MAP.PROJECT_LOAD, _this.project.id)
                        // 		alert("삭제 하였습니다.");
                        // 	},
                        // 	onError: (error) => {
                        // 		//alert('실패');
                        // 	}
                        // });
                    },
                });
            } else {
                PopupUtil.alertPopup("선택 목록이 없습니다.");
            }
        },

        sendEventToMap: function (event, data) {
            try {
                const aloaMap = document.getElementById('iframe-map').contentWindow.app.$refs.aloaMap;
                aloaMap.$emit(event, data);
            } catch (e) {
                console.error("sendEventToMap Exception: " + e);
            }
        },

        getSelectedRiderDestCount: function () {
            let destCnt = 0;
            let _this = this;
            this.project.riders.forEach((rider) => {
                if (rider.isSelected) {
                    destCnt += rider.destSize;
                }
            });
            return destCnt;
        },

        addSuffixToClassInKorean: function (className) {
            // return ( this.language  === Constant.LANGUAGE.KOREAN )
            //     ? className +"_kr" : className;
            return className + "_kr";
        },

        getRiderShowStatus: function (rider) {
            return RiderUtil.getRiderShowStatus(rider);
        },

        setPinMode: function (pinMode) {

            this.selectedPin = pinMode;
            this.sendEventToMap(EVENT.MAP.SELECTION_MODE_CHANGED, this.tabMode);
            this.sendEventToMap(EVENT.MAP.SET_PIN_MODE, pinMode);
        },

        onSelectionModeChanged: function (toolbarMode) {
            this.toolbarMode = toolbarMode;//0 을 주면 기사/방문지 선택 창을 닫는다
        },

        onIsRouteLineDisplay: function(data){
            this.isRouteLineDisplay = data;
        },

        onSelectionDemoModeChanged: function (data) {
            this.demoRoutingMode = data.mode;
        },

        showDeliveryEditPopup: function () {

            // this.$emit('show-gridview-edit-popup', riders, dests);
            this.sendEventToMap(EVENT.MAP.SHOW_GRID_VIEW_PROJECT);

        },

        updateData: function (data) {
            if (data.language !== undefined) {
                this.language = data.language;
            }

            if (Global.options.fulfilmentMode || Global.options.pickUpDropOffWithReservationMode) {
                this.isNewSelectDestinationPinMode = true;
            } else {
                this.isNewSelectDestinationPinMode = false;
            }

        },

        showDisplayGroupAndId: function (dest) {
            let text = "";
            if (dest.groupName) {
                text = (DemoUtil.getShortWord(dest.groupName, 5) + ":" + PlaceUtil.getDestinationDisplayId(dest, 17));
            } else {
                text = PlaceUtil.getDestinationDisplayId(dest, 24);
            }
            return text;
        },

        showDeliveryDetailPopupByDestList: function (data) {
            if (data) {
                //project.riders 밑 배송 정보가 최신으로 update 되어서 그것을 사용하게 기능 구헌함.
                const dest = PlaceUtil.findDestinationInRider(this.project.riders, data.destination.delivery);
                if (dest) {
                    this.showDeliveryDetailPop(dest, data.viewMode);
                }else{
                   let destList = this.project.destinations.filter(dest => dest.deliveryId === data.destination.delivery.deliveryId);
                    if (destList) {
                        for (let dest of destList) {
                            this.showDeliveryDetailPop(dest, data.viewMode);
                            break;
                        }
                    }
                }
            }
        },

        showDisplayRiderGroupAndId: function (rider) {
            let text = "";
            if (rider.groupName && rider.groupName !== rider.name) {
                text = " - " + (DemoUtil.getShortWord(rider.groupName, 5));
            }
            return text;
        },

        ///tree source function

        setChangeRiderStatus: function (rider) {
            let element = {};
            element.id = 'rider' + rider.riderId;
            let item = this.$refs.riderTree.getItem(element);
            console.log("[setChangeRiderStatus] riderId: " + rider.riderId);

            let gpa = null;

            gpa = document.getElementById(element.id);
            if (gpa != null) {
                if (gpa.getElementsByClassName('left_panel_driverList_circle')[0]) {
                    gpa.getElementsByClassName('left_panel_driverList_circle')[0].style.backgroundColor = RiderUtil.getRiderShowStatusStyle(rider);
                }
                if (gpa.getElementsByClassName('left_panel_driverList_ride_status')[0]) {
                    gpa.getElementsByClassName('left_panel_driverList_ride_status')[0].innerText = RiderUtil.getRiderShowStatusText(rider);
                }

//                if (gpa.getElementsByClassName('left_panel_driverList_ride_latency')[0]) {
//                    gpa.getElementsByClassName('left_panel_driverList_ride_latency')[0].innerText = RiderUtil.getRiderShowLatency(rider);
//                }

                if (gpa.getElementsByClassName('left_panel_driverList_ride_text')[0]) {
                    gpa.getElementsByClassName('left_panel_driverList_ride_text')[0].innerText = this.getRiderNameAndDeliveryCount(rider);
                }
            }

            Util.timeCheckStart();
            this.$refs.riderTree.refresh();
            Util.timeCheckEnd("riderTree.refresh() - riderTreeDisplay");
        },

        setChangeDeliveryStatus: function (obj) {
            const rider = obj.rider;
            const destination = obj.destination;

            let element = {};
            element.id = 'rider' + rider.riderId;
            let gpa = document.getElementById(element.id);
            if (gpa != null) {

                //인어 교주 해적단 // 기사의 마지막 배송을 완료 후 실시간으로 배송완료 시간 update 시킴
                if (rider.destinations.length == destination.label && Util.isTpiratesCompany()) {
                    let elementParent = {};
                    elementParent = gpa.getElementsByClassName("left_panel_driverList_ride_rname");
                    let reiderHtml = `<div>
                                <div class="left_panel_driverList_ride_rname"   >
                                    <div class = "left_panel_driverList_circle"  style="background-color: ${RiderUtil.getRiderShowStatusStyle(rider)}; float: left; margin-top: 5px; margin-right: 5px"></div>
                                    <div class = "left_panel_driverList_ride_text" style="font-weight: bold; color : ${this.getRiderFontColor(rider)}; " > ${this.getRiderNameAndDeliveryCount(rider)} </div>
                                    <div class="left_panel_driverList_ride_status">
                                        ${RiderUtil.getRiderShowStatusText(rider)}
                                    </div>
                                    <div class="left_panel_driverList_ride_completed_time">
                                            ${RiderUtil.getRiderCompletedTime(rider)}
                                    </div>
                                </div>
                            </div>`;
                    elementParent[0].outerHTML = reiderHtml;
                }

                const findClassName = "did_" + destination.deliveryId
                let findElement = gpa.getElementsByClassName(findClassName);
                if (findElement && findElement[0]) {

                    //인어 교주 해적단 //기사의 배송지 완료 시간을 update 시킴
                    if (Util.isTpiratesCompany()) {
                        let html = `<div class="did_${destination.deliveryId}" style="color : ${DeliveryUtil.getDestinationFontColor(destination)} "; >${destination.label}-${PlaceUtil.getDestinationDisplayId(destination, 18)}${DeliveryUtil.getDeliveryStartTime(destination)}   </div>`;
                        findElement[0].outerHTML = html;
                    } else
                        findElement[0].style.color = DeliveryUtil.getDestinationFontColor(destination);

                    Util.timeCheckStart();
                    this.$refs.riderTree.refresh();
                    Util.timeCheckEnd("riderTree.refresh() - setChangeDeliveryStatus");

//                    if (destination.deliveryStatus === Constant.DELIVERY_STATUS.GOING || destination.deliveryStatus === Constant.DELIVERY_STATUS.SERVICING) {
//                        this.startCalculatingLatency();
//                    } else {
//                        this.stopCalculatingLatency();
//                    }

                }
            }
            ///destination panel 함수 호출
//            if (window.parent.app.$refs.leftPanel.$refs.destinationListPanel !== undefined) {
//                window.parent.app.$refs.leftPanel.$refs.destinationListPanel.refreshDestinationsList();
//            }
        },

        getRiderNameAndDeliveryCount(rider) {
            return Util.escape_HTML(rider.name)  + "(" + RiderUtil.getCompleteDeliveryCount(rider) + "/" + RiderUtil.getTotalDeliveryCount(rider) + ")" + this.showDisplayRiderGroupAndId(rider);
        },

        getRiderNameAndRiderId(rider) {
            return Util.escape_HTML(rider.name) + "(" + RiderUtil.getCompleteDeliveryCount(rider) + "/" + RiderUtil.getTotalDeliveryCount(rider) + ")" + "  ID:" + rider.riderId;
        },

        //현재 펼쳐지 있는 기사 아이템의 id를 구한다
        getExtendItemRiderIdList: function () {
            let extendItemRiderIdList = []
            for (const rider of this.project.riders) {
                let element = {};
                element.id = 'rider' + rider.riderId;
                let item = this.$refs.riderTree.getItem(element);
                if (item && item.isExpanded) {
                    extendItemRiderIdList.push(rider.riderId);
                }

            }
            return extendItemRiderIdList;
        },

        initTreeSource: function (data) {

            console.log("[initTreeSource]");

            this.treeSource = null;
            this.gridTreeSource = null;

            if (data == null) {
                this.project.riders = [];
                this.project.destinations = [];
                this.project.hubTotalCount = 0;
                this.project.branchTotalCount = 0;
                this.project.lastDestinationTotalCount = 0;
                this.project.riderTotalCount = 0;
                this.project.isAnonymous = true;
            } else {

                this.updateProject(data.project);

                if (this.tabMode == 1) {
                    this.riderTreeDisplay();
                }
                ////////////////////////////////////////////////////////////////////////////////////////////////////
                else {
                    this.destinationsTreeDisplay();
                }
                //////////////////////////////////////////////////////////////////////////////////////////////////

                //지연 시간을 update 하기 위해서 5분 timer를 설정하여 주기적으로 update 하기 위한 코드
                // this.startCalculatingLatency();
            }
            //top tool bar 값을 전달함.
            this.sendToTopToolbarRiderSelect()
        },

        riderTreeDisplay: function () {
            let resultTree = [];
            this.unCheckChildren = [];
            let extendItemRiderIdList = this.getExtendItemRiderIdList();
            this.unCheckRiderChildren = [];
            let loadingIndex = 1;
            const _this = this;

            if (_this.treeSource == null) {
                let riderListData = null
                if(Util.isWelStoryUserCompany()){
                    riderListData = _.sortBy(_this.project.riders, "dispatchNumber");
                }else
                    riderListData = _this.project.riders;

                riderListData.forEach((rider) => {
                    let item = {};

                    if (Util.isAdmin()) {
                        if (_this.project.attribute.isAutoCreated === true) {
                            item.html = `<div>
                            <div class="left_panel_driverList_ride_rname"   >
                                <div class = "left_panel_driverList_circle"  style="background-color: ${RiderUtil.getRiderShowStatusStyle(rider)}; float: left; margin-top: 5px; margin-right: 5px"></div>
                                <div class = "left_panel_driverList_ride_text" style="font-weight: bold; color :  ${this.getRiderFontColor(rider)}; " > ${_this.getRiderNameAndRiderId(rider)} </div>
                                <div class="left_panel_driverList_ride_status">
                                    ${RiderUtil.getRiderShowStatusText(rider)}                                        
                                </div>
                                 <div class="left_panel_driverList_rider_dispatch_number truncate">
                                    ${RiderUtil.getRiderDispatchNumberText(rider)}
                                </div>                                
                            </div>
                        </div>`;
                        } else {
                            item.html = `<div>
                              <div class="left_panel_driverList_ride_rname"  >
                                <div class = "left_panel_driverList_circle"  style="background-color: ${RiderUtil.getRiderShowStatusStyle(rider)}; float: left; margin-top: 5px; margin-right: 5px"></div>
                                <div class = "left_panel_driverList_ride_text" style="font-weight: bold; color :  ${this.getRiderFontColor(rider)}; " > ${_this.getRiderNameAndRiderId(rider)} </div>
                                <div class="left_panel_driverList_ride_status">
                                    ${RiderUtil.getRiderShowStatusText(rider)}                                        
                                </div>
                                 <div class="left_panel_driverList_rider_dispatch_number truncate">
                                    ${RiderUtil.getRiderDispatchNumberText(rider)}
                                </div>                                
                           </div>
                        </div>`;
                        }
                    } else {
                        if (_this.project.attribute.isAutoCreated === true) {
                            item.html = `<div>
                            <div class="left_panel_driverList_ride_rname"   >
                                <div class = "left_panel_driverList_circle"  style="background-color: ${RiderUtil.getRiderShowStatusStyle(rider)}; float: left; margin-top: 5px; margin-right: 5px"></div>
                                <div class = "left_panel_driverList_ride_text" style="font-weight: bold; color : ${this.getRiderFontColor(rider)}; " > ${_this.getRiderNameAndDeliveryCount(rider)} </div>
                                <div class="left_panel_driverList_ride_status">
                                    ${RiderUtil.getRiderShowStatusText(rider)}                                        
                                </div>
                                 <div class="left_panel_driverList_rider_dispatch_number truncate">
                                    ${RiderUtil.getRiderDispatchNumberText(rider)}
                                </div>                                
                            </div>
                        </div>`;
                        } else if (Util.isTpiratesCompany()) {
                            item.html = `<div>
                                <div class="left_panel_driverList_ride_rname"   >
                                    <div class = "left_panel_driverList_circle"  style="background-color: ${RiderUtil.getRiderShowStatusStyle(rider)}; float: left; margin-top: 5px; margin-right: 5px"></div>
                                    <div class = "left_panel_driverList_ride_text" style="font-weight: bold; color : ${this.getRiderFontColor(rider)}; " > ${_this.getRiderNameAndDeliveryCount(rider)} </div>
                                    <div class="left_panel_driverList_ride_status">
                                      ${RiderUtil.getRiderShowStatusText(rider)}                                        
                                    </div>
                                    <div class="left_panel_driverList_ride_completed_time">
                                      ${RiderUtil.getRiderCompletedTime(rider)}
                                    </div>
                                </div>
                            </div>`;
                        } else {
                            item.html = `<div>
                            <div class="left_panel_driverList_ride_rname"   >
                                <div class = "left_panel_driverList_circle"  style="background-color: ${RiderUtil.getRiderShowStatusStyle(rider)}; float: left; margin-top: 5px; margin-right: 5px"></div>
                                <div class = "left_panel_driverList_ride_text" style="font-weight: bold; color : ${this.getRiderFontColor(rider)}; " > ${_this.getRiderNameAndDeliveryCount(rider)} </div>
                                <div class="left_panel_driverList_ride_status">
                                    ${RiderUtil.getRiderShowStatusText(rider)}                                        
                                </div>
                                <div class="left_panel_driverList_rider_dispatch_number truncate">
                                    ${RiderUtil.getRiderDispatchNumberText(rider)}
                                </div>
                            </div>
                        </div>`;
                        }
                    }


                    item.id = 'rider' + rider.riderId;
                    if (rider.isSelected)
                        item.checked = true;
                    else
                        item.checked = false;

                    //이전 아이템이 펼쳐진 상태이면 이것으로 복구함
                    if (extendItemRiderIdList.indexOf(rider.riderId) >= 0) {

                        let count = 1;
                        item.items = [];

                        rider.destinations.forEach(dest => {
                            let elemnet = _this.setRiderChildDestination(dest, count);
                            item.items.push(elemnet);
                            count++;
                        });

                        item.expanded = true;
                    } else {
                        if (rider.destinations.length > 0) {
                            item.items = [];
                            item.items.push(_this.makeLoadingItem("Reider", loadingIndex));
                            loadingIndex = loadingIndex + 1;
                        }
                    }

                    resultTree.push(item);
                });
                ///////////////////////////////////////////
                //할당 안된 배송지에 대한 기능 구현
                _this.isNotAssignmentDestinations();

                if(true){
                    Util.timeCheckStart();
                    let item = {};
                    item.html = `<div>
                            <div class="left_panel_driverList_ride_rname">
                                <div class = "left_panel_driverList_ride_text" style="font-weight: bold; color : ${_this.getNotAssignmentFontColor(_this.isNotAssignmentDestinationsData.length)};" > ${_this.changedLanguage("미배차배송지")} (${_this.isNotAssignmentDestinationsData.length})</div>
                                <div class="left_panel_driverList_ride_status" style="color : ${_this.getNotAssignmentFontColor(_this.isNotAssignmentDestinationsData.length)};">
                                    ${_this.changedLanguage("배차 필요함")}                                        
                                </div>
                            </div>
                        </div>`;

                    item.checked = true;
                    item.items = [];
                    item.id = 'unassigned';

                    //로딩할때 속도개선. 미배차 배송지를 굳이 300개 이상 보여줄 필요 없을 듯 하다 .
                    const MAX_SHOW_DESTINATION_COUNT = 300;
                    for( let index = 0 ; index < _this.isNotAssignmentDestinationsData.length ; index++ ){
                        const destination = _this.isNotAssignmentDestinationsData[index];
                        let subItem = _this.setRiderChildDestination(destination, index+1);
                        item.items.push(subItem);
                        if( index > MAX_SHOW_DESTINATION_COUNT ){
                            break;
                        }
                    }

                    // _.forEach(_this.isNotAssignmentDestinationsData, function (destination, index) {
                    //     let subItem = _this.setRiderChildDestination(destination, index+1);
                    //     item.items.push(subItem);
                    // })
                    item.expanded = false;
                    resultTree.push(item);
                    Util.timeCheckEnd("resultTree.push");
                }
                ///////////////////////////////////////////
                _this.treeSource = resultTree;
                Util.timeCheckStart();
                _this.$refs.riderTree.source = _this.treeSource;
                Util.timeCheckEnd("set riderTree.source");
            }

            this.$nextTick(() => {
                _this.setCheckBoxHidden(_this.unCheckRiderChildren);
                _this.setCheckBoxHidden(_this.unCheckChildren);
                Util.timeCheckStart();
                _this.$refs.riderTree.refresh();
                Util.timeCheckEnd("riderTree.refresh() - riderTreeDisplay");
            });
        },

        isNotAssignmentDestinations: function () {
            const _this = this;

            _this.isNotAssignmentDestinationsData = _.filter(_this.project.destinations, function (destination) {
                return destination.riderId == undefined
            })
        },

        destinationsTreeDisplay: function () {

            if (this.gridTreeSource == null) {

                let zipCodes = [];
                this.zipCodesSort = [];

                this.project.destinations.forEach((dest) => {

                    let zip = zipCodes.find(function (z) {
                        return z.id == dest.zipCode;
                    })

                    let item = {};
                    if (zip == null) {
                        item.label = _t("우편번호") + " : " + dest.zipCode;
                        item.id = dest.zipCode;

                        /////////////////////////////////
                        //Loading item 만듬
                        item.items = [];
                        const searchZipcode = this.project.destinations.filter(d => d.zipCode == dest.zipCode);
                        searchZipcode.forEach(dest => {
                            //const html = `<div><div>${PlaceUtil.getDestinationDisplayId(dest, 18)}</div><div>${DemoUtil.getShortWord(dest.parsedAddr, 25)}</div></div>`;
                            const html = `${DemoUtil.getShortWord(dest.parsedAddr, 25)}`;
                            const childId = "dest_" + dest.deliveryId;
                            item.items.push({
                                id: childId,
                                label: html,
                                customerOrderId: dest.customerOrderId ? dest.customerOrderId : dest.deliveryId
                            });
                        });

                        // item.items = [];
                        // item.items.push({id: "Loading_"+dest.zipCode, label: "Loading"});
                        /////////////////////////////////

                        zipCodes.push(item);
                    }
                });

                //sort를 위한 함수 // 오름차순으로 sorting.
                this.zipCodesSort = zipCodes.sort(function (before, after) {
                    return before.id < after.id ? -1 : before.id > after.id ? 1 : 0;
                });

                this.gridTree = {
                    dataType: 'json',
                    dataFields: [
                        {name: 'id', type: 'string'},
                        {name: 'label', type: 'string'},
                        {name: 'customerOrderId', type: 'string'},
                        {name: 'items', type: 'array'}
                    ],
                    hierarchy: {
                        root: "items"
                    },
                    id: 'id',
                    localData: this.zipCodesSort
                };

                this.gridTreeSource = new $.jqx.dataAdapter(this.gridTree);

                this.$refs.destTree.source = this.gridTreeSource;
                Util.timeCheckStart();
                this.$refs.destTree.refresh();
                Util.timeCheckEnd("riderTree.refresh() - destinationsTreeDisplay");

                const _this = this;
                this.$nextTick(() => {
                    _this.checkDestAll();
                });
            }
            // this.treeDestSource.localData = this.zipCodesSort;
            // this.treeDestAdapter = new $.jqx.dataAdapter(this.treeDestSource);

            // this.$refs.destTree.source = this.treeDestAdapter;
        },

        //Loading 아이콘 만듬
        makeLoadingItem: function (tickString, loadingIndex) {
            let LoadingItem = {};

            LoadingItem.html = `<div>Loading..</div>`;
            LoadingItem.id = tickString + "_Loading_" + loadingIndex;

            this.unCheckRiderChildren.push(LoadingItem.id);

            // if (tickString == "Dest")
            //     this.unCheckDestChildren.push(LoadingItem.id);
            // else
            //     this.unCheckRiderChildren.push(LoadingItem.id);

            return LoadingItem;
        },

        setCheckBoxHidden: function (unCheckList) {
            let gpa = null;
            for (var n = 0; n < unCheckList.length; n += 1) {
                gpa = document.getElementById(unCheckList[n]);
                if (gpa != null) {
                    gpa.getElementsByClassName('jqx-checkbox')[0].style.visibility = 'hidden';
                    gpa.getElementsByClassName('jqx-tree-item')[0].style.marginLeft = "0px";
                }
            }
        },

        //Tree event function ///////////////////////////////
        onItemClick: function (event) {
            let selected = this.$refs.riderTree.getSelectedItem();
            //rider에 대한 정보를 보여줘야함
            if (selected.parentId == 0 || selected.parentId == null) {
                let riderId = selected.id.replace("rider", "");
                if (selected.hasItems == false || selected.subtreeElement.firstElementChild.innerText == "Loading..") {
                    let rider = this.findRider(riderId);
                    if(_.isNil(rider.isSimulationRider) || !rider.isSimulationRider) {
                        this.showDriverDetailPop(rider, 'r');
                    }else{
                        if(this.project.attribute.isRouteExecute)
                            window.parent.app.$emit(EVENT.MAIN.OPEN_RIDER_CHANGED_POPUP, rider);
                        else
                            PopupUtil.alertPopup("가배차 경로 탐색 후 실배차 차량으로 변경이 가능합니다.");
                    }
                } else {
                    let rider = this.findRider(riderId);
                    if(_.isNil(rider.isSimulationRider) || !rider.isSimulationRider) {
                        this.showDriverDetailPop(rider, 'r');
                    }else{
                        if(this.project.attribute.isRouteExecute)
                            window.parent.app.$emit(EVENT.MAIN.OPEN_RIDER_CHANGED_POPUP, rider);
                        else
                            PopupUtil.alertPopup("가배차 경로 탐색 후 실배차 차량으로 변경이 가능합니다.");
                    }
                }
            }
            //destination에 대한 정보를 보여줘야함.
            else {
                let riderId = selected.parentId.replace("rider", "");
                let rider = this.findRider(riderId);

                let destId = selected.id.replace("dest", "");
                let dest = {};

                //할당된 기사가 있을 경우와 없을 경우에 따른 분기
                if(rider)
                    dest = this.findDestination(rider, destId);
                else
                    dest = this.findDestinations(destId);

                this.showDeliveryDetailPop(dest, 'r')
            }
        },

        onTreeItemExpand: function (event) {
            let element = $(event.args.element);
            let children = element.find('ul:first').children();

            if (children[0].innerText == "Loading..") {
                this.$refs.riderTree.removeItem(children[0]);
                let riderId = children.context.id.replace("rider", "");
                let rider = this.findRider(riderId);

                if (rider.destinations.length > 0) {
                    this.makeRiderChildrenElement(rider, children[0], children.context);
                }
            }
        },

        makeRiderChildrenElement: function (rider, removeElement, parentElement) {
            let count = 1;
            this.$refs.riderTree.removeItem(removeElement);

            rider.destinations.forEach(dest => {
                let elemnet = this.setRiderChildDestination(dest, count);
                this.$refs.riderTree.addTo(elemnet, parentElement);
                count++;
            });

            /////////////////////////////////////
            //children check box 삭제 하기 위한 code
            this.setCheckBoxHidden(this.unCheckChildren);
            ////////////////////////////////////////

            this.$refs.riderTree.render();
            this.$refs.riderTree.expandItem(parentElement);
        },

        setRiderChildDestination: function (dest, count) {
            let destItem = {};
            // destItem.icon = '/assets/image/leftpanel/bt_list_add_map_driver_p.svg';
            if (Util.isTpiratesCompany())
                destItem.html = `<div class="did_${dest.deliveryId}" style="color : ${DeliveryUtil.getDestinationFontColor(dest)} "; >${count}-${PlaceUtil.getDestinationDisplayId(dest, 18)}${DeliveryUtil.getDeliveryStartTime(dest)}   </div>`;
            else
                destItem.html = `<div class="did_${dest.deliveryId}" style="color : ${DeliveryUtil.getDestinationFontColor(dest)} "; >${count}-${PlaceUtil.getDestinationDisplayId(dest, 18)}${DeliveryUtil.getDeliveryLocality(dest)}</div>`;

            // destItem.label = PlaceUtil.getDestinationDisplayId(dest, 18);
            destItem.id = 'dest' + dest.deliveryId;

            if (dest.isSelected)
                destItem.checked = true;
            else
                destItem.checked = false;

            this.unCheckChildren.push(destItem.id);

            return destItem;
        },

        findRider: function (riderId) {
            return this.project.riders.find(r => r.riderId == riderId);
        },

        findDestination: function (rider, destId) {
            return rider.destinations.find(d => d.deliveryId == destId);
        },

        findDestinations(destId) {
            let destination = this.project.destinations.find(d => d.deliveryId == destId);
            return destination;
        },

        findDestinationsFindRiderId(destId) {
            let destination = this.project.destinations.find(d => d.deliveryId == destId);
            return destination ? destination.riderId : null;
        },

        onCheckChange: function (event) {
            var args = event.args;
            var element = args.element;
            var riderId;
            var destId;

            if (args.checked != null) {
                if (element.id) {
                    // console.log(element.id);
                    // console.log(element.id.indexOf("rider"));
                    // console.log(element.id.indexOf("dest"));

                    if (element.id.indexOf("rider") != -1) {
                        riderId = element.id.replace("rider", "");
                        let rider = this.findRider(riderId);
                        rider.isSelected = event.args.checked;

                        //top tool bar 값을 전달함.
                        this.sendToTopToolbarRiderSelect()

                        this.sendEventToMap(EVENT.MAP.RIDER_SELECTED, rider);
                    }else{
                        let destinations = {
                            "destinations" : {},
                            "isSelected" : false,
                        };

                        destinations.isSelected = event.args.checked;
                        destinations.destinations = this.isNotAssignmentDestinationsData;
                        console.log(destinations);

                        this.isNotAssignmentDestinationsData.forEach(dest =>{
                            dest.isSelected = event.args.checked;
                        });

                        console.log(this.project.destinations);

                        this.sendEventToMap(EVENT.MAP.DESTINATIONS_SELECTED, destinations);
                    }
                }
            }

            ///destination panel 함수 호출
            if (window.parent.app.$refs.leftPanel.$refs.destinationListPanel !== undefined) {
                window.parent.app.$refs.leftPanel.$refs.destinationListPanel.refreshDestinationsList();
            }
        },

        dragEnd: function (dragItem, dropItem, args, dropPosition, tree) {
            // return false; 조건에 맞지 않을 경우 return false 할 경우 drag 안됨
            // 이 부분도 dropPosition 조건에 맞게 클러스트링 변수들 위치를 변경해야함.
            const aloaMap = document.getElementById('iframe-map').contentWindow.app.$refs.aloaMap;

            if (dropPosition == undefined)
                return false;

            //배송지가 기사 이름과 동일한 트리에 만들수 없게 조건 처리함.
            if (dragItem.level == 1) {
                if (dropItem.level == 0 && dropPosition == 'after')
                    return false;
            }

            //이동한 아이템이 기사 이전으로 등록 안되게 수정함.
            // if(dropItem.level == 0  && (dropPosition == 'before' || dropPosition =='inside'))
            if (dropItem.level == 0 && (dropPosition == 'before'))
                return false;

            // //같은 곳에 옮길때에는 이동 안되게 구현
            if (dragItem.id == dropItem.id)
                return false;

            //기사 이동에서 기사로 이동시 전체 변경하는 API
            if (dragItem.level == 0 && dropItem.level == 0 && dropPosition == 'inside') {
                let dragDestRiderId = dragItem.id.replace("rider", "");
                let dropItemRiderId = dropItem.id.replace("rider", "");

                console.log(dragDestRiderId);
                console.log(dropItemRiderId);


                aloaMap.$emit(EVENT.MAP.RIDERS_SWITCH_CLUSTERING, {
                    dragDestRiderId: dragDestRiderId,
                    dropItemRiderId: dropItemRiderId
                });
            }
            //방문지를 기사에 추가하는 API
            else if (dropPosition == 'inside' && dropItem.level == 0) {
                let dragDestId = dragItem.id.replace("dest", "");
                let dropoffRiderId = dropItem.id.replace("rider", "");


                //dropoff 가 미배송 배송지로 주게 되면 delivery를 미할당으로 변경해야함.

                aloaMap.$emit(EVENT.MAP.CHANGE_DESTINATION_CLUSTER, {
                    dragDestId: dragDestId,
                    dropoffRiderId: dropoffRiderId
                });

            }
            //방문지를 방문지로 추가하는 API
            else {
                let dragDestId = dragItem.id.replace("dest", "");
                let dropoffDestId = dropItem.id.replace("dest", "");
                let isDragUnassigned = false;
                let isDropOffUnassigned = false;

                //배송지가 할당이 안되어 있을 경우에 대한 조건이 필요하여 사용함.
                //isDragUnassigned true : 미배차 배송지를 기사 배송지로 이동
                //isDropOffUnassigned true : 기사 배송지를 미배차 배송지로 이동
                if(_.includes(dragItem.parentId, 'unassigned'))
                    isDragUnassigned = true;
                if(_.includes(dropItem.parentId, 'unassigned'))
                    isDropOffUnassigned = true;

                aloaMap.$emit(EVENT.MAP.CHANGE_DESTINATION_CLUSTER, {
                    dragDestId: dragDestId,
                    dropoffDestId: dropoffDestId,
                    isDragUnassigned : isDragUnassigned,
                    isDropOffUnassigned : isDropOffUnassigned,
                    isNotAssignmentDestinationsData : this.isNotAssignmentDestinationsData,
                });
            }

            return false;// false를 리턴해야 리스트가 변하지 않는다
        },

        onDragStart: function (event) {
            console.log(event);
        },

        onDragEnd: function (event) {
            let selected = this.$refs.riderTree.getSelectedItem();

            if (event.args.label) {
                const ev = event.args.originalEvent;
                let x = ev.pageX;
                let y = ev.pageY;
                if (ev && ev.originalEvent && ev.originalEvent.touches) {
                    const touch = ev.originalEvent.changedTouches[0];
                    x = touch.pageX;
                    y = touch.pageY;
                }
            }
        },

        checkAll: function () {
            this.$refs.riderTree.checkAll();
        },
        uncheckAll: function () {
            this.$refs.riderTree.uncheckAll();
        },

        ///////////////////////////////////////////////////////////////
        // Dest tree checking
        ///////////////////////////////////////////////////////////////
        onDestItemClick: function (event) {
            console.log("onDestItemClick");
            console.log(event.args.originalEvent.target.innerText);
            if (event.args.originalEvent.target.innerHTML.length > 0) {
                if (!event.args.originalEvent.target.innerHTML.includes("<div")) {
                    let selected = event.args.row;
                    var riderId;

                    //destination에 대한 정보를 보여줘야함.
                    if (selected.parent != null) {
                        let destId = selected.data.id.replace("dest_", "");

                        riderId = this.findDestinationsFindRiderId(parseInt(destId));
                        riderId = parseInt(riderId);
                        if (riderId) {
                            let rider = this.findRider(riderId);
                            let dest = this.findDestination(rider, destId);
                            this.showDeliveryDetailPop(dest, 'r')
                        } else {
                            let originDest = PlaceUtil.findDestinationByDeliveryId(this.project.destinations, parseInt(destId));
                            this.showDeliveryDetailPop(originDest, 'r')
                        }
                    } else {
                        //여기에서 child 연결해야 될것 같음.
                        //if (selected.hasItems == false || selected.subtreeElement.firstElementChild.innerText == "Loading..") {
                        //    this.makeDestChildrenElement(selected.id, selected.subtreeElement.firstElementChild, selected.element );
                        //}
                    }
                }
            }
        },

        onDestItemSelect: function (event) {
            console.log("onDestItemSelect");
        },

        onDestItemUnselect: function (event) {
            console.log("onDestItemUnselect");
        },

        onRowDoubleClick: function (event) {
            console.log("onRowDoubleClick");

            // //let selected = this.$refs.destTree.getSelectedItem();
            // let selected = event.args.row;
            // var riderId;
            //
            // //destination에 대한 정보를 보여줘야함.
            // if (selected.parent != null) {
            //     let destId = selected.data.id.replace("dest_", "");
            //
            //     riderId = this.findDestinationsFindRiderId(parseInt(destId));
            //     riderId = parseInt(riderId);
            //     if (riderId) {
            //         let rider = this.findRider(riderId);
            //         let dest = this.findDestination(rider, destId);
            //         this.showDeliveryDetailPop(dest, 'r')
            //     } else {
            //         let originDest = PlaceUtil.findDestinationByDeliveryId(this.project.destinations, parseInt(destId));
            //         this.showDeliveryDetailPop(originDest, 'r')
            //     }
            // } else {
            //     //여기에서 child 연결해야 될것 같음.
            //     //if (selected.hasItems == false || selected.subtreeElement.firstElementChild.innerText == "Loading..") {
            //     //    this.makeDestChildrenElement(selected.id, selected.subtreeElement.firstElementChild, selected.element );
            //     //}
            // }

        },

        onDestItemExpand: function (event) {
            console.log("onDestItemExpand");
            //     let element = $(event.args.element);
            //     let children = element.find('ul:first').children();
            //
            //     if (children[0].innerText == "Loading..") {
            //         this.makeDestChildrenElement(children.context.id, children[0], children.context );
            //     }
        },

        onDestItemCollapse: function (event) {
            console.log("onDestItemCollapse");
        },

        makeDestChildrenElement: function (destId, children, parent) {
            this.$refs.destTree.removeItem(children);

            const searchZipcode = this.project.destinations.filter(d => d.zipCode == destId);

            searchZipcode.forEach(dest => {
                let elemnet = this.setDestZipCodeChildAddress(dest);
                this.$refs.destTree.addTo(elemnet, parent);
            });

            this.$refs.destTree.render();
            this.$refs.destTree.expandItem(parent);
        },

        //방문지 Tab에 child 추가하는 함수
        setDestZipCodeChildAddress: function (dest) {
            let destItem = {};
            destItem.html = `<div><div>${PlaceUtil.getDestinationDisplayId(dest, 18)}</div><div>${DemoUtil.getShortWord(dest.parsedAddr, 25)}</div></div>`;
            destItem.id = "dest_" + dest.deliveryId;

            if (dest.isSelected)
                destItem.checked = true;
            else
                destItem.checked = false;

            return destItem;
        },

        onDestItemCheck: function (event) {
            // console.log("onDestItemCheck");
            if (this.$refs.destTree.updating()) {
                return;
            }
            let keyId = event.args.key;

//            if (keyId.includes("dest_") && this.isFullCheckedFlag == false) {
            if (keyId.includes("dest_")) {
                this.setDestSelectedValue(keyId, true);
            }
        },

        onDestItemUncheck: function (event) {
            console.log("onDestItemUncheck");
            if (this.$refs.destTree.updating()) {
                return;
            }
            let keyId = event.args.key;

//            if (keyId.includes("dest_") && this.isFullCheckedFlag == false) {
            if (keyId.includes("dest_")) {
                this.setDestSelectedValue(keyId, false);
            }
        },

        setDestSelectedValue: function (keyId, isChecked) {
            var riderId;
            let destId = keyId.replace("dest_", "");

            riderId = this.findDestinationsFindRiderId(parseInt(destId));
            if (riderId) {
                let rider = this.findRider(riderId);
                let dest = this.findDestination(rider, parseInt(destId));
                if (dest) {
                    dest.isSelected = isChecked;
                }
            }

            let originDest = PlaceUtil.findDestinationByDeliveryId(this.project.destinations, parseInt(destId));
            if (originDest) {
                originDest.isSelected = isChecked;
            }

        },

        setZipCodeDestSelected: function () {

        },

        checkDestAll: function () {
            if (this.zipCodesSort) {
                this.$refs.destTree.beginUpdate();
                this.zipCodesSort.forEach(zipCodes => {
                    const id = zipCodes.id;
                    this.$refs.destTree.checkRow(id);
                });
                this.$refs.destTree.endUpdate();
                Util.timeCheckStart();
                this.$refs.destTree.refresh();
                Util.timeCheckEnd("riderTree.refresh() - checkDestAll");

                this.project.riders.forEach(r => {
                    r.destinations.forEach(d => {
                        d.isSelected = true;
                    });
                });

                this.project.destinations.forEach(d => {
                    d.isSelected = true;
                });
            }
        },

        uncheckDestAll: function () {
            if (this.zipCodesSort) {
                this.$refs.destTree.beginUpdate();
                this.zipCodesSort.forEach(zipCodes => {
                    const id = zipCodes.id;
                    this.$refs.destTree.uncheckRow(id);
                });
                this.$refs.destTree.endUpdate();
                this.$refs.destTree.refresh();

                this.project.riders.forEach(r => {
                    r.destinations.forEach(d => {
                        d.isSelected = false;
                    });
                });

                this.project.destinations.forEach(d => {
                    d.isSelected = false;
                });
            }
        },

        getRiderFontColor: function (rider) {
            let fontColor = "#22394d";
            if (MapUtil.isClusteringStatus(this.project) || MapUtil.isRoutingDoneStatus(this.project) || MapUtil.isProjectStatusInProgress(this.project)) {
                if (rider.colorIndex != null) {
                    const color = Util.getClusteringColor(rider.colorIndex);
                    fontColor = Util.getColorHexByRGB(color);
                }
            }

            return fontColor;
        },

        getNotAssignmentFontColor: function (count) {
            let fontColor = "#808080";
            if(count > 0) {
                fontColor = "#FF6077";
            }
            return fontColor;
        },

        startCalculatingLatency: function () {
            this.stopCalculatingLatency();

            const _this = this;
            const intervalSeconds = 5 * 60 * 1000; // 5분

            try {
                this.latencyIntervalId = setInterval(function () {
                    _this.project.riders.forEach((rider) => {
                        console.log("[startCalculatingLatency] rider.latency: " + rider.latency);
                        _this.setChangeRiderStatus(rider);
                    });
                }, intervalSeconds);
            } catch (e) {
                console.error("[startCalculatingLatency] Error: " + e);
                this.stopCalculatingLatency();
            }
        },

        stopCalculatingLatency: function () {
            if (this.latencyIntervalId) {
                console.log("[stopCalculatingLatency] clearInterval");
                clearInterval(this.latencyIntervalId);
                this.latencyIntervalId = null;
            }
        },

        cellsrender: function (row, columnsfield, value, defaulthtml, columnproperties, rowdata) {
            if (defaulthtml.customerOrderId != undefined) {
                return `<div style="width: 150px; height:10px; position: relative; top:-25px; left: 50px"><div>${DemoUtil.getShortWord(defaulthtml.customerOrderId, 12)}</div><div>${Util.escape_HTML(value)}</div></div>`;
            }
        },

        isRiderDispatchNumber(rider){
            return !_.isNil(rider.dispatchNumber);
        },

        onSelectRiderFiles(e) {
            if (this.project.deleted) {
                PopupUtil.alertPopup("임시 프로젝트에서는 기사를 추가할수 없습니다");
                return;
            }
            const files = e.target.files;
            this.selectExcelSimulationRidersFiles(files);
            Util.clearFileInput(e.target);
        },

        selectExcelSimulationRidersFiles(files) {
            const firstFilename = files[0].name;
            let firstFileExt = firstFilename.substring(firstFilename.lastIndexOf('.'));
            if (firstFileExt.toLowerCase() === '.xls' || firstFileExt.toLowerCase() === '.xlsx') {
                if (files.length > 1) {
                    PopupUtil.alertPopup('2개 이상 엑셀 파일 업로드는 지원하지 않습니다.');
                } else {
                    this.sendEventSimulationRidersExcelDrop(files[0]);
                }
            } else {
                PopupUtil.alertPopup('엑셀이나 CSV가 아닌 파일 업로드는 지원하지 않습니다.');
            }
        },

        sendEventSimulationRidersExcelDrop(file) {
            const aloaMap = document.getElementById('iframe-map').contentWindow.app.$refs.aloaMap;

            let data = {
                'projectId': this.project.id,
                'file': file,
                'name': this.project.name
            };
            aloaMap.$emit(EVENT.MAP.SIMULATION_RIDERS_TO_DISPATCH_RIDERS_PROJECT_EXCEL, data);
        },

        sendToTopToolbarRiderSelect(){
            const selectRiders =_.filter(this.project.riders, {isSelected:true});
            const selectRiderIds = _.map(selectRiders, "riderId");
            window.parent.app.$refs.topToolBar.$emit(EVENT.TOP_TOOLBAR.LEFT_PANEL_CHECK_RIDERS, selectRiderIds);
        },

        selectDispatchRiderExcel(){
            if(this.project.attribute.isRouteExecute)
                document.getElementById('selectDispatchRiderExcel').click();
            else
                PopupUtil.alertPopup("가배차 경로 탐색 후 실배차 차량으로 변경이 가능합니다.");
        },

        changedLanguage(text){
            return _t(text);
        }
    },
};
