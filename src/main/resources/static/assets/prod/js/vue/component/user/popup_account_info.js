var popupAccountInfo = {
    template: `
    <div id="popup-account-info-container">
        <div class="modal-mask">
            <div class="modal_full"></div>
            <div class="popup_account_info" v-if="!isChangePwSuccess">
                <div class="account_info_title">{{$t("내 계정 정보")}}</div>
                <button class="account_info_window_close" @click="hideAccountInfoPopup"></button>
                <div class="account_info_title_line"></div>
        
                <div class="account_info_area1">
                    <div class="account_info_driver_pic_area">
                        <div class="account_info_driver_pic" @click="clickPictureArea">
                            <img :src="profileImgFileUrl" v-if="profileImgFileUrl" style="width: 100%; height: 100%;">
                        </div>
                        <div class="account_pic_edit_area" v-if="isShowPictureMenu">
                            <div class="account_pic_edit_area_bt1" @click="clickPictureBtn">
                                {{$t("사진 업로드")}}
                                <input type="file" @change="uploadProfileImg" ref="userImgFileTag" hidden>
                            </div>
                            <div class="account_pic_edit_area_bt2" @click="deletePicture">{{$t("사진 삭제")}}</div>
                        </div>
                    </div>
        
                    <div class="account_info_name_area">
                        <div class="account_info_name_area_1">
                            <div class="account_info_name"> {{userName}} </div>
                            <input type="checkbox" name="" id="name_edit" v-model="nameChecked">
                            <label class="account_info_name_edit" for="name_edit">{{$t("이름 편집")}}</label>
                            <div class="account_info_name_area_1_1">
                                <div class="account_info_name_alert" v-if="isUserNameEmpty">{{$t("이름을 입력해주세요.")}}</div>
                                <input type="text" name="" class="name_edit_input" v-model="userName" :maxlength="13" ref="userNameInput">
                                <label class="account_info_name_edit_submit" for="name_edit" @click="changeName">{{$t("확인")}}</label>
                            </div>
                        </div>		
                        <div class="account_info_name_area_1">
                            <div class="account_info_email">{{userEmail}}</div>	
                        </div>
                    </div>
                </div>
                
                <div class="account_info_title_line"></div>	
                <div class="account_info_pw_area1">
                    <div class="account_info_index">{{$t("현재 비밀번호")}}</div>
                    <button class="account_info_find">{{$t("비밀번호를 잊어버렸습니까?")}}</button>
                    <div class="account_info_pw_icon"></div>
                    <input type="password" name="" :placeholder="$t('현재 비밀번호')" class="account_info_pw_input" ref="orgPassword" v-model="password.orgPassword">
                </div>
        
                <div class="account_info_pw_area2">
                    <div class="account_info_index">{{$t("새 비밀번호")}}</div>
                    <div class="account_info_pw_icon"></div>
                    <input type="password" name=""  :placeholder="$t('비밀번호')" class="account_info_pw_input" ref="newPassword" v-model="password.newPassword">
                </div>
                
                <div class="account_info_pw_area2">
                    <div class="account_info_index">{{$t("새 비밀번호 다시입력")}}</div>
                    <div class="account_info_pw_icon"></div>
                    <input type="password" name=""  :placeholder="$t('비밀번호 확인')" class="account_info_pw_input" ref="newPasswordConfirm" v-model="password.newPasswordConfirm">
                </div>
        
                <div class="account_info_pw_alert" v-if="isError">{{errorMessage}}</div>
                <button class="account_info_button_r" @click="changePassword" v-if="isAllInputPassword" style="cursor: pointer">{{$t("저장")}}</button>
                <button class="account_info_button_g" v-else style="cursor: text">{{$t("저장")}}</button>
            </div>
            
            <div v-if="isChangePwSuccess">
                <div class="modal_full"></div>        
                <div class="popup_account_check">
                    <div class="account_check_icon"></div>
                    <div class="account_check_text1">{{$t("비밀번호 변경")}}</div>
                    <div class="account_check_text2">{{$t("비밀번호가 변경되었습니다.")}}</div>
                </div>
            </div>
        </div>
    </div>
    `,
    props: {
    	profile : null,
    },
    data: function() {
        return {
            nameChecked: false,
            isUserNameEmpty: false,
            isChangePwSuccess: false,
            isShowPictureMenu: false,
            userName: "Guest",
            userEmail: '',
            password: {
                orgPassword: null,
                newPassword: null,
                newPasswordConfirm: null,
            },
            isError: false,
            errorMessage: '',
            profileImgFileUrl: null,
        };
    },
    created: function() {

    },
    mounted() {
        const userInfo = this.$store.getters.getLoginUserInfo;
        if (userInfo && !userInfo.isAnonymous) {
            this.userName = userInfo.name;
            this.userEmail = userInfo.email;
            this.profileImgFileUrl = userInfo.profileImgFileUrl;
        }

        //this.$refs.orgPassword.focus();
    },
    computed: {
        isAllInputPassword : function() {
            if (this.password.orgPassword && this.password.newPassword && this.password.newPasswordConfirm) {
                return true;
            }
            return false;
        },

        getProfileImgFileUrl : function() {
            return this.profileImgFileUrl;
        }
    },
    methods: {
        clickPictureArea() {
            this.isShowPictureMenu = !this.isShowPictureMenu;
        },

        changeName() {
            if (!this.userName || this.userName.trim() == '') {
                this.isUserNameEmpty = true;
                this.nameChecked = !this.nameChecked;
                this.userName = '';
                this.$refs.userNameInput.focus();
                return;
            }

            let userInfo = this.$store.getters.getLoginUserInfo;
            if (this.userName === userInfo.name) {
                return;
            }

            const _this = this;
            const body = {
                email : this.userEmail,
                name: this.userName,
            }

            USER_API.changeUserName(body, {
                onSuccess: (response) => {
                    this.isUserNameEmpty = false;
                    userInfo.name = _this.userName;
                    _this.$store.dispatch('setLoginUserInfo', userInfo);
                }
                ,onError: (error) => {
                    PopupUtil.alertPopup(_t("사용자 이름 변경 실패.") + "\n" + _t("관리자에게 문의바랍니다."));
                }
            });
        },

        changePassword() {
            if (!this.password.orgPassword) {
                this.showErrorMessage(_t('현재 비밀번호를 입력해주세요.'), 5);
                this.$refs.orgPassword.focus();
                return;
            }

            if (!this.password.newPassword) {
                this.showErrorMessage(_t('새 비밀번호를 입력해주세요.'), 5);
                this.$refs.newPassword.focus();
                return;
            }

            if (!this.password.newPasswordConfirm) {
                this.showErrorMessage(_t('새 비밀번호를 다시 입력해주세요.'), 5);
                this.$refs.newPasswordConfirm.focus();
                return;
            }

            if (this.password.newPassword !== this.password.newPasswordConfirm) {
                this.showErrorMessage(_t('새 비밀번호와 새 비밀번호 다시입력 두 값이 다릅니다.'), 5);
                return;
            }

            if (this.password.orgPassword === this.password.newPassword) {
                this.showErrorMessage(_t('현재 비밀번호와 새 비밀번호가 달라야 합니다.'), 5);
                return;
            }

            if (this.invalidPassword(this.password.newPassword)) {
                this.showErrorMessage(_t('숫자, 특수문자, 영어 대/소문자 조합의 8~20 자리를 입력해주세요.'), 5);
                return;
            }

            const _this = this;
            const body = {
                email : this.userEmail,
                orgPassword : this.password.orgPassword,
                newPassword : this.password.newPassword,
            }

            USER_API.changeUserPassword(body, {
                onSuccess: (response) => {
                    _this.isChangePwSuccess = true;
                    setTimeout(() => {
                        _this.hideAccountInfoPopup();
                    }, 2000);
                },
                onError: (error) => {
                    _this.showErrorMessage(error.response.data);
                }
            })
        },

        clickPictureBtn() {
            this.isShowPictureMenu = false;
            this.$refs.userImgFileTag.click();
        },

        uploadProfileImg(event) {
            let input = event.target;
            if (!input.files || !input.files[0]) {
                console.warn('선택된 파일이 없습니다.');
                return;
            }

            let file = input.files[0];
            const validExts = new Array(".png", ".jpg");
            let fileExt = file.name.substring(file.name.lastIndexOf('.'));
            if(fileExt && validExts.indexOf(fileExt) < 0) {
                PopupUtil.alertPopup(_t("PNG, JPG 이미지 파일만 지원 가능합니다."));
                return;
            }

            const _this = this;
            USER_API.uploadUserProfileImg(file, {
                onSuccess: (response) => {
                    console.log(`사용자 프로파일 이미지 업로드 성공 : ${JSON.stringify(response.data)}`);

                    let reader = new FileReader();
                    reader.onload = (e) => {
                        // Note: arrow function used here, so that "this.imageData" refers to the imageData of Vue component
                        // Read image as base64 and set to imageData
                        _this.profileImgFileUrl = e.target.result;
                    };
                    reader.readAsDataURL(file);

                    //_this.profileImgFileUrl = response.data.fileUrl;

                    // Store 저장
                    let userInfo = this.$store.getters.getLoginUserInfo;
                    userInfo.profileImgFileUrl = response.data.fileUrl;
                    _this.$store.dispatch('setLoginUserInfo', userInfo);
                },
                onError: (error) => {
                    console.error(`사용자 프로파일 이미지 업로드 실패 : ${JSON.stringify(error)}`);
                    PopupUtil.alertPopup(_t("프로파일 이미지 업로드 실패.") + "\n" + _t("관리자에게 문의바랍니다."));
                }
            });
        },

        deletePicture() {
            this.isShowPictureMenu = false;

            const _this = this;
            USER_API.deleteUserProfileImg({
                onSuccess: () => {
                    console.log(`사용자 프로파일 이미지 삭제 성공`);
                    _this.profileImgFileUrl = null;

                    // Store 에서 프로파일 이미지 URL 제거
                    let userInfo = this.$store.getters.getLoginUserInfo;
                    userInfo.profileImgFileUrl = null;
                    _this.$store.dispatch('setLoginUserInfo', userInfo);
                },
                onError: (error) => {
                    console.error(`사용자 프로파일 이미지 삭제 실패 : ${JSON.stringify(error)}`);
                    PopupUtil.alertPopup(_t("프로파일 이미지 삭제 실패.") + "\n" + _t("관리자에게 문의바랍니다."));
                }
            });
        },

        hideAccountInfoPopup() {
            this.$emit('show-popup-account-info', false);
        },

        showErrorMessage(msg, hideDelaySecond) {
            const _this = this;
            _this.isError = true;
            _this.errorMessage = msg;

            if (!hideDelaySecond) return;

            setTimeout(() => {
                _this.isError = false;
                _this.errorMessage = "";
            }, hideDelaySecond * 1000)
        },

        invalidPassword(password) {
            const strongRegex = new RegExp("^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*()_+=])(?=.{8,20})");
            if (!strongRegex.test(password)) {
                return true;
            }
            return false;
        }
    }
};