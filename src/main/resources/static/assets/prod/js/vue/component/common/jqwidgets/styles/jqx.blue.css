
:root {
    --jqx-grid-row-height: 36px;
    --jqx-grid-column-height: 48px;
    --jqx-grid-show-column-lines: 0;
    --jqx-list-item-height: -1;
    --jqx-grid-filter-menu-items-height: 30px;
    --jqx-dropdown-animation: transform;
    --jqx-datetimeinput-dropdown-height: 280px;
    --jqx-datetimeinput-dropdown-width: 280px;
    --jqx-calendar-header-height: 40px;
    --jqx-calendar-title-height: 49px;
    --jqx-icon-calendar: '\e829'; /* Code of calendar icon */
    --jqx-icon-filter: '\f0b0'; /* Code of filter icon */
    --jqx-icon-menu: '\f0c9'; /* Code of menu icon */
    --jqx-icon-check: '\e908'; /* Code of check icon */
    --jqx-icon-first-page: '\e900'; /* Code of first page icon */
    --jqx-icon-arrow-down: '\e901'; /* Code of down arrow icon */
    --jqx-icon-arrow-left: '\e902'; /* Code of left arrow icon */
    --jqx-icon-arrow-right: '\e903'; /* Code of right arrow icon */
    --jqx-icon-arrow-up: '\e904'; /* Code of up arrow icon */
    --jqx-icon-arrow-down-filled: '\e812'; /* Code of filled down arrow icon */
    --jqx-icon-arrow-left-filled: '\e816'; /* Code of filled left arrow icon */
    --jqx-icon-arrow-right-filled: '\e81e'; /* Code of filled right arrow icon */
    --jqx-icon-arrow-up-filled: '\e815'; /* Code of filled up arrow icon */
    --jqx-icon-visibility: '\e90d'; /* Code of visibility icon */
    --jqx-icon-visibility-off: '\e90e'; /* Code of visibility off icon */
    --jqx-icon-last-page: '\e905'; /* Code of last page icon */
    --jqx-icon-close: '\e80d'; /* Code of close icon */
    --jqx-icon-search: '\e828'; /* Code of search icon */
    --jqx-primary-rgb: 0, 92, 153;
    --jqx-primary: rgb(var(--jqx-primary-rgb));
    --jqx-primary-color: #fff;
    --jqx-background: #fff;
    --jqx-background-color: rgba(0,0,0, .88);
    --jqx-background-hover-rgb: 225, 225, 225;
    --jqx-background-hover: rgb(var(--jqx-background-hover-rgb));
    --jqx-background-color-hover: rgba(0,0,0,.54);
    --jqx-surface-rgb: 155, 190, 65;
    --jqx-surface: rgb(var(--jqx-surface-rgb));
    --jqx-surface-color: white;
    --jqx-border: #E0E0E0;
    --jqx-border-radius: 4px;
    --jqx-font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
    --jqx-font-size: 14px;
    --jqx-action-button-size: 25px;
    --jqx-scrollbar-background: #f5f5f5;
    --jqx-scrollbar-border: #ddd;
    --jqx-scrollbar-thumb-background: #C1C1C1;
    --jqx-scrollbar-thumb-border: #b3b3b3;
    --jqx-scrollbar-thumb-background-hover: #e6e6e6;
    --jqx-scrollbar-thumb-border-hover: #b3b3b3;
    --jqx-scrollbar-thumb-background-pressed: #d9d9d9;
    --jqx-scrollbar-thumb-border-pressed: #b3b3b3;
    --jqx-scrollbar-button-color-hover: #333;
    --jqx-scrollbar-button-background-hover: #f5f5f5;
    --jqx-scrollbar-button-border-hover: #f5f5f5;
    --jqx-scrollbar-button-color-pressed: #333;
    --jqx-scrollbar-button-background-pressed: #f5f5f5;
    --jqx-scrollbar-button-border-pressed: #f5f5f5;


    --jqx-primary-rgb: 51, 173, 255;
    --jqx-primary: rgb(var(--jqx-primary-rgb));
    --jqx-background: #252526;
    --jqx-background-color: #E7E7E7;
    --jqx-surface:#3C3C3C;
    --jqx-surface-color: #969690;
    --jqx-border: #414141;
    --jqx-background-hover-rgb: 60, 60, 60;
    --jqx-background-hover: rgb(var(--jqx-background-hover-rgb));
    --jqx-background-color-hover: rgba(255,255,255,.54);
    --jqx-scrollbar-background: #3E3E42;
    --jqx-scrollbar-border: #1E1E1E;
    --jqx-scrollbar-thumb-background: #686868;
    --jqx-scrollbar-thumb-border: #3E3E42;
    --jqx-scrollbar-thumb-background-hover: #9E9E9E;
    --jqx-scrollbar-thumb-border-hover: #9E9E9E;
    --jqx-scrollbar-thumb-background-pressed: #ffffff;
    --jqx-scrollbar-thumb-border-pressed: #ffffff;

}

.jqx-icon-search-blue,
.jqx-icon-close-blue {
    background-image: none;
    font-family: jqx-icons;
}
.jqx-icon-close-blue:after {
    content: var(--jqx-icon-close);
}
.jqx-icon-search-blue:after {
    content: var(--jqx-icon-search);
    color:

}
.jqx-calendar-blue {
    width: 280px !important;
    height: 280px !important;
}
.jqx-fill-state-normal-blue {
    background: var(--jqx-background);
    color: var(--jqx-background-color);
    border-color: var(--jqx-border);
}
.jqx-fill-state-hover-blue {
    background: var(--jqx-background-hover);
    color: var(--jqx-background-color-hover);
    border-color: var(--jqx-background-hover);
}
.jqx-fill-state-pressed-blue {
    background: var(--jqx-primary);
    color: var(--jqx-primary-color);
    border-color: var(--jqx-primary);
}

@font-face {
    font-family: jqx-icons;
    src: local('./font/jqx-icons'), url('./font/jqx-icons.woff2') format('woff2'), url('./font/jqx-icons.woff') format('woff'), url('./font/jqx-icons.ttf') format('truetype'), url('./font/jqx-icons.eot') format('embedded-opentype');
    font-weight: normal;
    font-style: normal;
}
/*Rounded Corners*/
/*top-left rounded Corners*/
.jqx-rc-tl-blue {
    border-top-left-radius: var(--jqx-border-radius);
}
/*top-right rounded Corners*/
.jqx-rc-tr-blue {
    border-top-right-radius: var(--jqx-border-radius);
}
/*bottom-left rounded Corners*/
.jqx-rc-bl-blue {
    border-bottom-left-radius: var(--jqx-border-radius);
}
/*bottom-right rounded Corners*/
.jqx-rc-br-blue {
    border-bottom-right-radius: var(--jqx-border-radius);
}
/*top rounded Corners*/
.jqx-rc-t-blue {
    border-top-left-radius: var(--jqx-border-radius);
    border-top-right-radius: var(--jqx-border-radius);
}
/*bottom rounded Corners*/
.jqx-rc-b-blue {
    border-bottom-left-radius: var(--jqx-border-radius);
    border-bottom-right-radius:var(--jqx-border-radius);
}
/*right rounded Corners*/
.jqx-rc-r-blue {
    border-top-right-radius: var(--jqx-border-radius);
    border-bottom-right-radius: var(--jqx-border-radius);
}
/*left rounded Corners*/
.jqx-rc-l-blue {
    border-top-left-radius: var(--jqx-border-radius);
    border-bottom-left-radius: var(--jqx-border-radius);
}
/*all rounded Corners*/
.jqx-rc-all-blue {
    border-radius: var(--jqx-border-radius);
}

.jqx-widget-blue, .jqx-widget-header-blue, .jqx-fill-state-normal-blue,
.jqx-widget-content-blue, .jqx-fill-state-hover-blue, .jqx-fill-state-pressed-blue {
    font-family: var(--jqx-font-family);
    font-size: var(--jqx-font-size);
}

.jqx-widget-blue {
    font-family: var(--jqx-font-family);
    font-size: var(--jqx-font-size);
    color: inherit;
    border-color:var(--jqx-border);
}

.jqx-widget-content-blue {
    font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 14px;
    color: var(--jqx-background-color);
    background-color: var(--jqx-background);
    border-color: var(--jqx-border);
}
.jqx-grid-table-blue.jqx-grid-table-one-cell {
    border-right-color: var(--jqx-border);
}
.jqx-widget-header-blue {
    background-color: var(--jqx-surface);
    border-color: var(--jqx-border);
    font-weight: 500;
    font-family: "Roboto", "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 14px;
    background: var(--jqx-surface);
    color: var(--jqx-surface-color);
}

.jqx-calendar-title-header-blue {
    text-transform: uppercase;
}
.jqx-window-header-blue {
    padding: 10px;
    text-transform: uppercase;
    color: var(--jqx-surface-color);
    background: var(--jqx-surface);
}
.jqx-calendar tr {
    border-bottom-color: var(--jqx-border);
}


.jqx-widget-blue input::selection, input.jqx-input-widget-blue::selection, .jqx-widget-content-blue input::selection {
    background: var(--jqx-primary);
    color: var(--jqx-primary-color);
}
.jqx-toolbar-blue{
     border-color: var(--jqx-border);
}

.jqx-toolbar-blue {
    height: auto !important;
    display: flex;
    align-items: center;
}

.jqx-button-blue, .jqx-button-blue.jqx-fill-state-normal-blue {
    color: var(--jqx-background-color);
    background-color: var(--jqx-background);
    border-color: var(--jqx-border);
    text-transform: uppercase;
    outline: none;
    transition: box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 4px;
    box-shadow: 0 0 0 0 rgba(0,0,0,.2), 0 0 0 0 rgba(0,0,0,.14), 0 0 0 0 var(--jqx-border);
}
.jqx-button-blue.jqx-fill-state-hover-blue,
.jqx-button-blue.jqx-fill-state-pressed-blue {
    background-color: var(--jqx-background-hover);
    border-color: var(--jqx-border);
    color: var(--jqx-background-color-hover);
}

.jqx-button-blue.primary {
    color: var(--jqx-primary-color);
    background-color: var(--jqx-primary);
    border-color: var(--jqx-primary);
}
.jqx-button-blue.jqx-fill-state-hover-blue.primary,
.jqx-button-blue.jqx-fill-state-pressed-blue.primary  {
    color: var(--jqx-primary-color);
    background-color: var(--jqx-primary);
    border-color: var(--jqx-primary);
}
.jqx-button-blue.secondary,
.jqx-button-blue.jqx-toolbar-tool {
    background-color: var(--jqx-surface);
    border-color: var(--jqx-border);
    color: var(--jqx-surface-color) !important;
}
.jqx-button-blue.secondary:hover,
.jqx-button-blue.jqx-toolbar-tool:hover {
    background-color: var(--jqx-background-hover);
    border-color: var(--jqx-border);
    color: var(--jqx-background-color-hover) !important;
}
.jqx-button-blue.secondary:active,
.jqx-button-blue.jqx-toolbar-tool:active {
    background-color: var(--jqx-surface);
    border-color: var(--jqx-border);
    color: var(--jqx-surface-color) !important;
}
.jqx-scheduler-edit-dialog-field .jqx-button-blue {
    padding: 6px 16px;
    text-transform: uppercase;
}

.jqx-button-blue button, jqx-button-blue input {
    background: transparent;
    color: inherit;
    border:none;
    outline: none;
}
.jqx-group-button-normal-blue{
    box-shadow: none;
    background: var(--jqx-background);
    border-color: var(--jqx-border);
    color: var(--jqx-background-color) !important;
    border-radius:0px;
}
.jqx-group-button-normal.jqx-fill-state-hover {
  box-shadow: none !important;
 }
.jqx-group-button-normal.jqx-fill-state-pressed {
    box-shadow: none !important;
    background: var(--jqx-primary) !important;
    border-color: var(--jqx-primary) !important;
    color: var(--jqx-primary-color)!important;
    border-radius:0px;
}


.jqx-slider-button-blue {
    padding:3px;
    background: transparent;
    border:transparent;
}
    .jqx-button-blue.float {
        border-radius: 100%;
        min-height: 48px;
        min-width: 48px;
        width: 48px;
        height: 48px;
        max-height: 48px;
        max-width:48px;
    }

    .jqx-button-blue.outlined {
        background: transparent;
        color: var(--jqx-primary);
        border-width: 2px;
    }

    .jqx-button-blue.flat {
        background: transparent;
        color: var(--jqx-primary);
        border: none;
    }

.jqx-fill-state-hover-blue, .jqx-fill-state-focus-blue {
    text-decoration: none;
}

 .jqx-expander-header.jqx-fill-state-hover-blue,
 .jqx-expander-header.jqx-fill-state-normal-blue,
 .jqx-expander-header.jqx-fill-state-pressed-blue
 {
      background: var(--jqx-background-hover);
      border-color: var(--jqx-border);
      color:var(--jqx-background-color-hover);
}
.jqx-expander-header.jqx-fill-state-hover-blue {
    background: var(--jqx-background-hover);
}

.jqx-expander-header-blue {
    padding:10px;
}
.jqx-button-blue.jqx-fill-state-hover {
    opacity: 0.9;
    cursor: pointer;
    box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);
    *zoom: 1;
}

    .jqx-button-blue.jqx-fill-state-hover.outlined,
    .jqx-button-blue.jqx-fill-state-hover.flat {
        color: var(--jqx-primary);
        box-shadow: none;
    }

.jqx-button-blue.jqx-fill-state-pressed {
    cursor: pointer;
    box-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);
}

    .jqx-button-blue.jqx-fill-state-pressed.float {
        box-shadow: 0px 7px 8px -4px rgba(0, 0, 0, 0.2), 0px 12px 17px 2px rgba(0, 0, 0, 0.14), 0px 5px 22px 4px rgba(0, 0, 0, 0.12);
    }

    .jqx-slider-button-blue.jqx-fill-state-pressed-blue,
    .jqx-button-blue.jqx-fill-state-pressed.outlined,
    .jqx-button-blue.jqx-fill-state-pressed.flat {
        background: rgba(179,229,252,0.15);
        box-shadow: none;
        color: var(--jqx-primary);
    }

.jqx-button-blue.jqx-fill-state-focus {
    box-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);
}
  .jqx-slider-button-blue.jqx-fill-state-focus-blue {
      background: transparent;
      border-color: transparent;
      box-shadow:none;
  }

    .jqx-button-blue.jqx-fill-state-focus.outlined,
    .jqx-button-blue.jqx-fill-state-focus.flat {
        box-shadow: none;
        background: rgba(rgb(var(--jqx-primary-rgb)),0.15);
        color: var(--jqx-primary);
    }

    .jqx-dropdownlist-content-blue {
        display: flex;
        align-items: center;
        height: 100% !important;
        margin-top: 0px !important;
    }

    .jqx-dropdownlist-content-blue span {
        top: 0px !important;
    }
.jqx-dropdownlist-state-normal-blue, .jqx-dropdownlist-state-hover-blue, .jqx-dropdownlist-state-selected-blue,
.jqx-scrollbar-button-state-hover-blue, .jqx-scrollbar-button-state-normal-blue, .jqx-scrollbar-button-state-pressed-blue,
.jqx-scrollbar-thumb-state-normal-horizontal-blue, .jqx-scrollbar-thumb-state-hover-horizontal-blue, .jqx-scrollbar-thumb-state-pressed-horizontal-blue,
.jqx-scrollbar-thumb-state-normal-blue, .jqx-scrollbar-thumb-state-pressed-blue, .jqx-tree-item-hover-blue, .jqx-tree-item-selected-blue,
.jqx-tree-item-blue, .jqx-menu-item-blue, .jqx-menu-item-hover-blue, .jqx-menu-item-selected-blue, .jqx-menu-item-top-blue, .jqx-menu-item-top-hover-blue,
.jqx-menu-item-top-selected-blue, .jqx-slider-button-blue, .jqx-slider-slider-blue {
    -webkit-transition: background-color 100ms linear;
    -moz-transition: background-color 100ms linear;
    -o-transition: background-color 100ms linear;
    -ms-transition: background-color 100ms linear;
    transition: background-color 100ms linear;
}


.jqx-primary-blue.jqx-input-label-blue {
   color: var(--jqx-primary) !important;
}
.jqx-primary-blue.jqx-input-bar-blue:before {
   background: var(--jqx-primary) !important;
}
.jqx-success-blue.jqx-input-label-blue {
   color: #5cb85c !important;
}
.jqx-success-blue.jqx-input-bar-blue:before {
   background: #5cb85c !important;
}
.jqx-inverse-blue.jqx-input-label-blue {
   color: #666 !important;
}
.jqx-inverse-blue.jqx-input-bar-blue:before {
   background: #666 !important;
}
.jqx-danger-blue.jqx-input-label-blue {
   color: #d9534f !important;
}
.jqx-danger-blue.jqx-input-bar-blue:before {
   background: #d9534f !important;
}
.jqx-warning-blue.jqx-input-label-blue {
   color: #f0ad4e !important;
}
.jqx-warning-blue.jqx-input-bar-blue:before {
   background: #f0ad4e !important;
}
.jqx-info-blue.jqx-input-label-blue {
   color: #5bc0de !important;
}
.jqx-info-blue.jqx-input-bar-blue:before {
   background: #5bc0de !important;
}

.jqx-slider-tooltip-blue.jqx-primary-slider, .jqx-slider-tooltip-blue.jqx-primary-slider .jqx-fill-state-normal-blue {
    border-color: var(--jqx-primary);
    background: var(--jqx-primary);
}
.jqx-slider-tooltip-blue.jqx-success-slider, .jqx-slider-tooltip-blue.jqx-success-slider .jqx-fill-state-normal-blue {
    border-color: #5cb85c;
    background: #5cb85c;
}
.jqx-slider-tooltip-blue.jqx-inverse-slider, .jqx-slider-tooltip-blue.jqx-inverse-slider .jqx-fill-state-normal-blue {
    border-color: #666;
    background: #666;
}
.jqx-slider-tooltip-blue.jqx-danger-slider, .jqx-slider-tooltip-blue.jqx-danger-slider .jqx-fill-state-normal-blue {
    border-color: #d9534f;
    background: #d9534f;
}
.jqx-slider-tooltip-blue.jqx-warning-slider, .jqx-slider-tooltip-blue.jqx-warning-slider .jqx-fill-state-normal-blue {
    border-color: #f0ad4e;
    background: #f0ad4e;
}
.jqx-slider-tooltip-blue.jqx-info-slider, .jqx-slider-tooltip-blue.jqx-info-slider .jqx-fill-state-normal-blue {
    border-color: #5bc0de;
    background: #5bc0de;
}


.jqx-primary-blue {
    color: var(--jqx-primary) !important;
    background: #fff !important;
    border-color: var(--jqx-primary) !important;
    text-shadow: none !important;
}

    .jqx-primary-blue.jqx-dropdownlist-state-normal-blue,
    .jqx-primary-blue.jqx-slider-button-blue,
    .jqx-primary-blue.jqx-slider-slider-blue,
    .jqx-primary-blue.jqx-combobox-arrow-normal-blue,
    .jqx-primary-blue.jqx-combobox-arrow-hover-blue,
    .jqx-primary-blue.jqx-action-button-blue,
    .jqx-primary-blue:hover,
    .jqx-primary-blue:focus,
    .jqx-primary-blue:active,
    .jqx-primary-blue.active,
    .jqx-primary-blue.disabled,
    .jqx-primary-blue[disabled] {
        color: #fff !important;
        background: var(--jqx-primary) !important;
        border-color: var(--jqx-primary) !important;
        text-shadow: none !important;
    }

    .jqx-fill-state-pressed-blue.jqx-primary-blue,
    .jqx-primary-blue:active,
    .jqx-primary-blue.active {
        color: #fff !important;
        background-color: var(--jqx-primary) !important;
        border-color: var(--jqx-primary) !important;
        text-shadow: none !important;
    }

.jqx-success-blue {
    color: #5cb85c !important;
    background: #fff !important;
    border-color: #5cb85c !important;
    text-shadow: none !important;
}

    .jqx-success-blue.jqx-dropdownlist-state-normal-blue,
    .jqx-success-blue.jqx-slider-button-blue,
    .jqx-success-blue.jqx-slider-slider-blue,
    .jqx-success-blue.jqx-combobox-arrow-normal-blue,
    .jqx-success-blue.jqx-combobox-arrow-hover-blue,
    .jqx-success-blue.jqx-action-button-blue,
    .jqx-success-blue:hover,
    .jqx-success-blue:focus,
    .jqx-success-blue:active,
    .jqx-success-blue.active,
    .jqx-success-blue.disabled,
    .jqx-success-blue[disabled] {
        color: #fff !important;
        background: #5cb85c !important;
        border-color: #5cb85c !important;
        text-shadow: none !important;
    }

    .jqx-fill-state-pressed-blue.jqx-success-blue,
    .jqx-success-blue:active,
    .jqx-success-blue.active {
        text-shadow: none !important;
        color: #fff !important;
        background: #5cb85c !important;
        border-color: #5cb85c !important;
    }

.jqx-inverse-blue {
    text-shadow: none !important;
    color: #666 !important;
    background: #fff !important;
    border-color: #cccccc !important;
}

    .jqx-inverse-blue.jqx-dropdownlist-state-normal-blue,
    .jqx-inverse-blue.jqx-slider-button-blue,
    .jqx-inverse-blue.jqx-slider-slider-blue,
    .jqx-inverse-blue.jqx-combobox-arrow-hover-blue,
    .jqx-inverse-blue.jqx-combobox-arrow-normal-blue,
    .jqx-inverse-blue.jqx-action-button-blue,
    .jqx-inverse-blue:hover,
    .jqx-inverse-blue:focus,
    .jqx-inverse-blue:active,
    .jqx-inverse-blue.active,
    .jqx-inverse-blue.disabled,
    .jqx-inverse-blue[disabled] {
        text-shadow: none !important;
        color: #666 !important;
        background: #cccccc !important;
        border-color: #cccccc !important;
    }

    .jqx-fill-state-pressed-blue.jqx-inverse-blue,
    .jqx-inverse-blue:active,
    .jqx-inverse-blue.active {
        text-shadow: none !important;
        color: #666 !important;
        background: #cccccc !important;
        border-color: #cccccc !important;
    }


.jqx-danger-blue {
    text-shadow: none !important;
    color: #d9534f !important;
    background: #fff !important;
    border-color: #d9534f !important;
}

    .jqx-danger-blue.jqx-dropdownlist-state-normal-blue,
    .jqx-danger-blue.jqx-slider-button-blue,
    .jqx-danger-blue.jqx-slider-slider-blue,
    .jqx-danger-blue.jqx-combobox-arrow-hover-blue,
    .jqx-danger-blue.jqx-combobox-arrow-normal-blue,
    .jqx-danger-blue.jqx-action-button-blue,
    .jqx-danger-blue:hover,
    .jqx-danger-blue:focus,
    .jqx-danger-blue:active,
    .jqx-danger-blue.active,
    .jqx-danger-blue.disabled,
    .jqx-danger-blue[disabled] {
        text-shadow: none !important;
        color: #fff !important;
        background: #d9534f !important;
        border-color: #d9534f !important;
    }

    .jqx-fill-state-pressed-blue.jqx-danger-blue,
    .jqx-danger-blue:active,
    .jqx-danger-blue.active {
        text-shadow: none !important;
        color: #fff !important;
        background: #d9534f !important;
        border-color: #d9534f !important;
    }

.jqx-validator-error-label-blue {
    color: #d9534f !important;
}

.jqx-warning-blue {
    text-shadow: none !important;
    color: #f0ad4e !important;
    background: #fff !important;
    border-color: #f0ad4e !important;
}

    .jqx-warning-blue.jqx-dropdownlist-state-normal-blue,
    .jqx-warning-blue.jqx-slider-button-blue,
    .jqx-warning-blue.jqx-slider-slider-blue,
    .jqx-warning-blue.jqx-combobox-arrow-hover-blue,
    .jqx-warning-blue.jqx-combobox-arrow-normal-blue,
    .jqx-warning-blue.jqx-action-button-blue,
    .jqx-warning-blue:hover,
    .jqx-warning-blue:focus,
    .jqx-warning-blue:active,
    .jqx-warning-blue.active,
    .jqx-warning-blue.disabled,
    .jqx-warning-blue[disabled] {
        text-shadow: none !important;
        color: #fff !important;
        background: #f0ad4e !important;
        border-color: #f0ad4e !important;
    }

    .jqx-fill-state-pressed-blue.jqx-warning-blue,
    .jqx-warning-blue:active,
    .jqx-warning-blue.active {
        text-shadow: none !important;
        color: #fff !important;
        background: #f0ad4e !important;
        border-color: #f0ad4e !important;
    }


.jqx-info-blue {
    text-shadow: none !important;
    color: #5bc0de !important;
    background: #fff !important;
    border-color: #5bc0de !important;
}

    .jqx-info-blue.jqx-dropdownlist-state-normal-blue,
    .jqx-info-blue.jqx-slider-button-blue,
    .jqx-info-blue.jqx-slider-slider-blue,
    .jqx-info-blue.jqx-combobox-arrow-hover-blue,
    .jqx-info-blue.jqx-combobox-arrow-normal-blue,
    .jqx-info-blue.jqx-action-button-blue,
    .jqx-info-blue:hover,
    .jqx-info-blue:focus,
    .jqx-info-blue:active,
    .jqx-info-blue.active,
    .jqx-info-blue.disabled,
    .jqx-info-blue[disabled] {
        color: #fff !important;
        background: #5bc0de !important;
        border-color: #5bc0de !important;
        text-shadow: none !important;
    }

    .jqx-fill-state-pressed-blue.jqx-info-blue,
    .jqx-info-blue:active,
    .jqx-info-blue.active {
        text-shadow: none !important;
        color: #fff !important;
        background: #5bc0de !important;
        border-color: #5bc0de !important;
    }

.jqx-fill-state-pressed-blue {
    background-image: none;
    outline: 0;
}

.jqx-grid-group-column-blue {
    border-color: transparent;
}
.jqx-grid-column-menubutton-blue {
    border-width: 0px;
}
.jqx-grid-groups-row-blue > span {
    padding-left: 4px;
}
.jqx-grid-column-filterbutton-blue,
.jqx-grid-column-menubutton-blue{
    background-image: none;
    font-family: 'jqx-icons';
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 0px;
}

.jqx-grid-column-filterbutton-blue:after {
    content: var(--jqx-icon-filter);
    background: var(--jqx-surface);
    color: var(--jqx-surface-color);
}
.jqx-grid-column-menubutton-blue:after {
    content: var(--jqx-icon-menu) !important;
    background: var(--jqx-surface);
    color: var(--jqx-surface-color);
}
.jqx-datatable-blue .jqx-grid-column-header-blue {
    border-right: none !important;
}

.jqx-datatable-blue td.jqx-grid-cell-blue,
.jqx-treegrid-blue .jqx-grid-cell-blue{
    padding-top: 10px;
    padding-bottom: 9px;
    font-size: 14px;
    border-left: none !important;
}

.jqx-grid-cell-blue {
    background: var(--jqx-background);
    color: var(--jqx-background-color);
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.jqx-grid-pager-top-blue .jqx-button-blue,
.jqx-grid-pager-blue .jqx-button-blue {
    color: inherit !important;
    background-color: transparent !important;
    border-color: transparent !important;
    position: relative;
    top: 0px;
    border-radius: 4px;
    display: flex;
    font-size: 16px;
    justify-content: center;
    align-content: center;
}

.jqx-grid-pager-input-blue  {
    padding:0px !important;
}

.jqx-grid-pager-top-blue .jqx-button-blue > div,
.jqx-grid-pager-blue .jqx-button-blue > div {
    top: 0px;
    position: relative;
    left: 0px;
    display: flex;
    align-items: center;
    margin-left: 0px !important;
}

.jqx-grid-pager-top-blue .jqx-button-blue.jqx-fill-state-hover,
.jqx-grid-pager-blue .jqx-button-blue.jqx-fill-state-hover
{
    color: var(--jqx-background-color-hover);
    background: var(--jqx-background-hover);
    border-color: var(--jqx-background-hover);
    box-shadow: none;
}
.jqx-grid-pager-top-blue .jqx-button-blue.jqx-fill-state-pressed,
.jqx-grid-pager-blue .jqx-button-blue.jqx-fill-state-pressed
{
    background: var(--jqx-primary);
}
.jqx-grid-pager-blue .jqx-button-blue:hover:after,
.jqx-grid-pager-top-blue .jqx-button-blue:hover:after,
.jqx-grid-pager-top-blue .jqx-button-blue.jqx-fill-state-pressed-blue:after,
.jqx-grid-pager-blue .jqx-button-blue.jqx-fill-state-pressed-blue:after {
    content: '';
    position: absolute;
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    border: 2px solid var(--jqx-background);
    border-radius: 4px;
    left:0px;
    top:0px;
}
.jqx-grid-pager-top-blue .jqx-grid-pager-number-blue,
.jqx-grid-pager-blue .jqx-grid-pager-number-blue {
    background-color: transparent;
    border-color: transparent;
    color: inherit;
    font-size:14px;
    padding: 6px 10px;
    border-radius: 4px;

    position: relative;
}

.jqx-grid-pager-top-blue .jqx-grid-pager-number-blue:hover,
.jqx-grid-pager-blue .jqx-grid-pager-number-blue:hover {
    background: var(--jqx-background-hover);
    color:var(--jqx-background-color-hover) !important;
    font-size: var(--jqx-font-size);
}
.jqx-grid-pager-blue .jqx-grid-pager-number-blue:hover:after,
.jqx-grid-pager-top-blue .jqx-grid-pager-number-blue:hover:after,
.jqx-grid-pager-top-blue .jqx-grid-pager-number-blue.jqx-fill-state-pressed-blue:after,
.jqx-grid-pager-blue .jqx-grid-pager-number-blue.jqx-fill-state-pressed-blue:after {
    content: '';
    position: absolute;
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    border: 2px solid var(--jqx-background);
    border-radius: var(--jqx-border-radius);
    left:0px;
    top:0px;
}
.jqx-grid-pager-top-blue .jqx-grid-pager-number-blue.jqx-fill-state-pressed-blue ,
.jqx-grid-pager-blue .jqx-grid-pager-number-blue.jqx-fill-state-pressed-blue {
    font-weight: bold !important;
    background: var(--jqx-primary);
    color:var(--jqx-background) !important;
}

.jqx-grid-column-menubutton-blue {
    background-color: transparent;
    border-color: var(--jqx-border) !important;
}

.jqx-cell-blue {
    font-size: 13px;
}

.jqx-calendar-blue > div {
    padding: 0px;
    box-sizing: border-box;
}
.jqx-calendar-month-blue {
    width: 90%;
    position: relative;
    left: 5%;
}
.jqx-calendar-title-navigation-blue {
    font-size: 20px;
    padding: 0px 20px;
}
.jqx-calendar-row-header-blue, .jqx-calendar-top-left-header-blue {
    background-color: var(--jqx-background);
    border: 0px solid var(--jqx-background);
}

.jqx-calendar-column-header-blue {
    background-color: var(--jqx-background);
    border-top: 1px solid var(--jqx-background);
    border-bottom: 1px solid var(--jqx-border);
    font-size: 12px;
    color: var(--jqx-background-color);
}

.jqx-expander-header-blue {
    padding-top: 10px;
    padding-bottom: 10px;
}

.jqx-ribbon-header-vertical-blue, .jqx-widget-header-vertical-blue {
    background: var(--jqx-background);
}

.jqx-scrollbar-state-normal-blue {
    background-color: var(--jqx-scrollbar-background);
    border: 1px solid var(--jqx-scrollbar-background);
    border-left-color: var(--jqx-scrollbar-border);
}

.jqx-scrollbar-thumb-state-normal-blue, .jqx-scrollbar-thumb-state-normal-horizontal-blue {
    background: var(--jqx-scrollbar-thumb-background);
    border-color: var(--jqx-scrollbar-thumb-border);
    border-radius: 0px;
}

.jqx-scrollbar-thumb-state-hover-blue, .jqx-scrollbar-thumb-state-hover-horizontal-blue {
    background: var(--jqx-scrollbar-thumb-background-hover);
    border-color: var(--jqx-scrollbar-thumb-border-hover);
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
}

.jqx-progressbar-blue {
    background: var(--jqx-background) !important;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.jqx-progressbar-value-blue, .jqx-splitter-collapse-button-horizontal-blue {
    background: var(--jqx-primary);
}

.jqx-splitter-collapse-button-vertical-blue, .jqx-progressbar-value-vertical-blue {
    background: var(--jqx-primary);
}

.jqx-scrollbar-button-state-hover-blue {
    color: var(--jqx-scrollbar-button-color-hover);
    background: var(--jqx-scrollbar-button-background-hover);
    border-color: var(--jqx-scrollbar-button-border-hover);
}


.jqx-scrollbar-button-state-pressed-blue {
    color: var(--jqx-scrollbar-button-color-pressed);
    background: var(--jqx-scrollbar-button-background-pressed);
    border-color: var(--jqx-scrollbar-button-border-pressed);
}

.jqx-splitter-splitbar-vertical-blue,
.jqx-splitter-splitbar-horizontal-blue {
    background: var(--jqx-scrollbar-thumb-background);
    border-color: var(--jqx-scrollbar-thumb-border);
}

.jqx-scrollbar-thumb-state-pressed-blue,
.jqx-scrollbar-thumb-state-pressed-horizontal-blue,
.jqx-scrollbar-button-state-pressed-blue {
    background: var(--jqx-scrollbar-thumb-background-pressed);
    border-color: var(--jqx-scrollbar-thumb-border-pressed);
    box-shadow: none;
}

.jqx-grid-column-sortdescbutton-blue, jqx-grid-column-filterbutton-blue, .jqx-grid-column-sortascbutton-blue {
    background-color: transparent;
    border-style: solid;
    border-width: 0px 0px 0px 0px;
    border-color: var(--jqx-border);
}

.jqx-menu-vertical-blue {
    background: var(--jqx-background);
    filter: none;
}

.jqx-grid-bottomright-blue, .jqx-panel-bottomright-blue, .jqx-listbox-bottomright-blue {
    background-color: var(--jqx-scrollbar-background);
}

.jqx-window-blue, .jqx-tooltip-blue {
    box-shadow: 0 4px 23px 5px rgba(0, 0, 0, 0.2), 0 2px 6px rgba(0,0,0,0.15);
}
.jqx-tooltip-blue, .jqx-tooltip-blue.jqx-popup-blue, .jqx-tooltip-blue .jqx-fill-state-normal-blue {
    background: var(--jqx-primary);
    border-color: var(--jqx-primary);
    box-shadow:none;
    color: var(--jqx-primary-color);
}
.jqx-docking-blue .jqx-window-blue {
    box-shadow: none;
}

.jqx-docking-panel-blue .jqx-window-blue {
    box-shadow: none;
}

.jqx-checkbox-blue {
    line-height:20px;
    overflow: visible;
}
.jqx-radiobutton-blue {
    overflow: visible;
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    background-repeat: no-repeat;
    background: none;
    line-height:20px;
}

.jqx-radiobutton-blue-blue, .jqx-radiobutton-hover-blue {
    border-radius: 100%;
    background-repeat: no-repeat;
    transition: background-color ease-in .3s;
}

.jqx-radiobutton-check-checked-blue {
    filter: none;
    background: var(--jqx-background);
    background-repeat: no-repeat;
    border-radius: 100%;
}

.jqx-radiobutton-check-indeterminate-blue {
    filter: none;
    background: var(--jqx-background);
    border-radius: 100%;
}

.jqx-radiobutton-check-indeterminate-disabled-blue {
    filter: none;
    background: var(--jqx-background);
    opacity: 0.7;
    border-radius: 100%;
}

.jqx-checkbox-default-blue,
.jqx-radiobutton-default-blue
 {
    border-width: 1px;
    border-color: var(--jqx-border);
    background-color: var(--jqx-background);
    overflow: visible;
}

.jqx-tree-grid-expand-button-blue,
.jqx-tree-grid-collapse-button-blue {
    font-size: 16px;
}
.jqx-grid-table-blue .jqx-grid-cell:first-child {
    padding-left: 10px;
}
.jqx-tree-grid-title-blue {
    margin-left: 5px;
}
.jqx-tree-blue .jqx-checkbox-blue .jqx-checkbox-default-blue,
.jqx-checkbox-blue[checked] .jqx-checkbox-default-blue,
.jqx-tree-grid-checkbox[checked].jqx-checkbox-default-blue,
.jqx-radiobutton-blue[checked] .jqx-radiobutton-default-blue
 {
    background-color: var(--jqx-primary);
    border-color: var(--jqx-primary);
}

.jqx-checkbox-check-checked-blue {
    background: none;
    font-family: jqx-icons;
}
.jqx-checkbox-check-checked-blue:after {
    content: var(--jqx-icon-check);
}
.jqx-checkbox-check-indeterminate-blue {
    width:14px !important;
    height:14px !important;
    position:relative;
    top: 1px;
    left: 1px;
    background: var(--jqx-background);
}
.jqx-tree-blue .jqx-checkbox-check-indeterminate-blue {
    width:12px !important;
    height:12px !important;
    top: 2px;
    left:2px;
}

.jqx-checkbox-hover-blue,
.jqx-radiobutton-hover-blue {
    background-color: var(--jqx-primary);
    border-color: var(--jqx-primary);
}


.jqx-slider-slider-blue {
    transition: box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.5s ease;
}

.jqx-slider-slider-blue:active {
    transform: scale(1.2);
    box-shadow: rgba(0,0,0,0.3) 0 0 10px;
}
.jqx-slider-blue[discrete] .jqx-slider-slider-blue:active
 {
    transform: scaleX(0);

}
.jqx-slider-slider-horizontal-blue {
    background: var(--jqx-primary);
}
.jqx-slider-slider-vertical-blue {
    background: var(--jqx-primary);
}
.jqx-slider-tooltip-blue {
    width: 25px;
    height: 25px;
    transform-origin: 50% 100%;
    border-radius: 50%;
    transform: scale(0) rotate(45deg);
    padding:0px;
    background: transparent !important;
}
.jqx-slider-tooltip-blue.init {
     transform: scale(1) rotate(45deg);
}
.jqx-slider-tooltip-blue.hide {
     transition: transform 0.2s ease;
     transform-origin:50% 100%;
     transform: scale(0) rotate(45deg);
}
.jqx-slider-tooltip-blue.show {
     transition: transform 0.2s ease;
     transform: scale(1) rotate(45deg);
}


.jqx-slider-tooltip-blue .jqx-tooltip-arrow-t-b,
.jqx-slider-tooltip-blue .jqx-tooltip-arrow-l-r {
    display:none;
    visibility:hidden;
}

.jqx-slider-tooltip-blue, .jqx-slider-tooltip-blue .jqx-fill-state-normal-blue {
    border-radius: 15px 15px 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--jqx-primary);
    color: var(--jqx-primary-color);
    font-size:11px;
}
.jqx-slider-tooltip-blue.far, .jqx-slider-tooltip-blue.far .jqx-fill-state-normal-blue {
   border-radius: 0px 15px 15px 15px;
}
.jqx-slider-tooltip-blue.vertical, .jqx-slider-tooltip-blue.vertical .jqx-fill-state-normal-blue {
   border-radius: 15px 0px 15px 15px;
}
.jqx-slider-tooltip-blue.vertical.far, .jqx-slider-tooltip-blue.vertical.far .jqx-fill-state-normal-blue {
   border-radius: 15px 15px 15px 0px;
}
.jqx-slider-tooltip-blue {
    background:transparent;
    border:none !important;
    box-shadow:none;
}
.jqx-slider-tooltip-blue .jqx-tooltip-main-blue {
    top: -7px;
    right: 11px;
}
.jqx-slider-tooltip-blue.far .jqx-tooltip-main-blue {
    top: 3px;
    right: 4px;
}
.jqx-slider-tooltip-blue.vertical .jqx-tooltip-main-blue {
    top: -3px;
    right: 3px;
}
.jqx-slider-tooltip-blue .jqx-tooltip-text {
    background: transparent;
    border:none;
    padding: 0px;
    overflow:visible;
}
.jqx-slider-tooltip-blue .jqx-tooltip-text>span {
     transform: rotate(-45deg);
}
.jqx-slider-tooltip-blue.range {
    width: 35px;
    height:35px;
}

.jqx-slider-rangebar-blue {
    border-color: var(--jqx-primary);
    background: var(--jqx-primary);
}

.jqx-slider-track-horizontal-blue, .jqx-slider-track-vertical-blue {
    border-color: var(--jqx-border);
    background: var(--jqx-background);
}

.jqx-slider-button-blue {
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}
.jqx-slider-button-blue.jqx-fill-state-normal-blue,
.jqx-slider-button-blue.jqx-fill-state-hover-blue,
.jqx-slider-button-blue.jqx-fill-state-pressed-blue
{
    background: transparent !important;
}

.jqx-tree-item-blue,
.jqx-tree-item-selected {
    padding: 6px;
    border-radius: 4px;
}
.jqx-listitem-element-blue .jqx-checkbox-default-blue {
    border-radius: 0px;
}
.jqx-listitem-state-hover-blue,
.jqx-listitem-state-selected-blue,
.jqx-listitem-state-normal-blue {
    border-radius: 0;
    margin:0px;
}

.jqx-scheduler-edit-dialog-label-blue {
  font-size: 12px;
  text-transform: uppercase;
  padding-top: 6px;
  padding-bottom: 6px;

}
.jqx-scheduler-edit-dialog-field-blue {
  padding-top: 6px;
  padding-bottom: 6px;
}
.jqx-scheduler-edit-dialog-label-rtl-blue {
  line-height: 35px;
  padding-top: 6px;
  padding-bottom: 6px;
}
.jqx-scheduler-edit-dialog-field-rtl-blue {
  line-height: 35px;
  padding-top: 6px;
  padding-bottom: 6px;
}
.jqx-menu-horizontal-blue {
    height: auto !important;
}
.jqx-menu-horizontal-blue .jqx-menu-item-top-blue {
    padding: 8px;
}
.jqx-menu-item-top-blue,
.jqx-menu-item-blue {
    padding: 8px;
}
/*applied to a list item when the item is selected.*/
.jqx-listitem-state-hover-blue, .jqx-menu-item-hover-blue, .jqx-tree-item-hover-blue, .jqx-calendar-cell-hover-blue, .jqx-grid-cell-hover-blue,
.jqx-input-popup-blue .jqx-fill-state-hover-blue,
.jqx-input-popup-blue .jqx-fill-state-pressed-blue {
    color: var(--jqx-background-color-hover) !important;
    border-color: var(--jqx-background-hover);
    text-decoration: none;
    background-color: var(--jqx-background-hover);
    background-repeat: repeat-x;
    outline: 0;
    background: var(--jqx-background-hover);
    box-shadow: none;
    background-position: 0 0;
}

.jqx-scheduler-cell-hover-blue {
    border-color: var(--jqx-primary) !important;
    background: var(--jqx-primary) !important;
    color: var(--jqx-background) !important;
}

.jqx-listitem-state-selected-blue, .jqx-menu-item-selected-blue, .jqx-tree-item-selected-blue, .jqx-calendar-cell-selected-blue, .jqx-grid-cell-selected-blue,
.jqx-menu-item-top-selected-blue, .jqx-grid-selectionarea-blue, .jqx-input-button-header-blue, .jqx-input-button-innerHeader-blue {
    color: var(--jqx-primary-color) !important;
    border-color: var(--jqx-primary) !important;
    background: var(--jqx-primary) !important; /* Old browsers */
    box-shadow: none;
}

.jqx-grid-cell-blue .jqx-button-blue, .jqx-grid-cell-blue .jqx-button-blue.jqx-fill-state-hover-blue, .jqx-grid-cell-blue .jqx-button-blue.jqx-fill-state-pressed-blue {
    box-shadow: none;
    transition: none;
}

.jqx-menu-popup-blue{
    opacity: 0;
    transform-origin: top left;
    box-shadow: 0 8px 10px 1px rgba(0,0,0,.14),0 3px 14px 3px rgba(0,0,0,.12),0 4px 15px 0 rgba(0,0,0,.2);
    background: var(--jqx-background) !important;
}
.jqx-menu-popup-blue.top {
    transform: scaleY(0);
    transition: transform 0.2s ease-in-out, opacity 0.3s ease-in-out;
}

.jqx-menu-popup-blue.horizontal {
    transform: scaleX(0);
    transition: transform 0.2s ease-in-out, opacity 0.3s ease-in-out;
}

.jqx-menu-popup-blue.show {
    transform: scale(1);
    opacity: 1;
}
.jqx-popup-blue {
    border: 1px solid var(--jqx-border);
    background: var(--jqx-background);
    box-shadow: 0 8px 10px 1px rgba(0,0,0,.14),0 3px 14px 3px rgba(0,0,0,.12),0 4px 15px 0 rgba(0,0,0,.2);
}
.jqx-menu-popup-blue .jqx-popup-blue {
    box-shadow: none;
    border: none;
}

.jqx-grid-column-sortascbutton-blue,
.jqx-expander-arrow-bottom-blue,
.jqx-window-collapse-button-blue,
.jqx-menu-item-arrow-up-blue,
.jqx-menu-item-arrow-up-selected-blue,
.jqx-menu-item-arrow-top-up-blue,
.jqx-icon-arrow-up-blue,
.jqx-icon-arrow-up-hover-blue,
.jqx-icon-arrow-up-selected-blue {
    background-image: none;
}

.jqx-grid-column-sortascbutton-blue,
.jqx-expander-arrow-bottom-blue,
.jqx-window-collapse-button-blue,
.jqx-menu-item-arrow-up-blue,
.jqx-menu-item-arrow-up-selected-blue,
.jqx-menu-item-arrow-top-up-blue,
.jqx-icon-arrow-up-blue,
.jqx-icon-arrow-up-hover-blue,
.jqx-icon-arrow-up-selected-blue {
    background-image: none;
    font-family: jqx-icons;
}
.jqx-grid-column-sortascbutton-blue:after,
.jqx-expander-arrow-bottom-blue:after,
.jqx-window-collapse-button-blue:after,
.jqx-menu-item-arrow-up-blue:after,
.jqx-menu-item-arrow-up-selected-blue:after,
.jqx-menu-item-arrow-top-up-blue:after,
.jqx-icon-arrow-up-blue:after,
.jqx-icon-arrow-up-hover-blue:after,
.jqx-icon-arrow-up-selected-blue:after {
    content: var(--jqx-icon-arrow-up);
}

.jqx-widget-blue .jqx-grid-group-expand-blue,
.jqx-grid-group-expand-blue,
.jqx-grid-column-sortdescbutton-blue,
.jqx-expander-arrow-top-blue,
.jqx-window-collapse-button-collapsed-blue,
.jqx-menu-item-arrow-down-blue,
.jqx-menu-item-arrow-down-selected-blue,
.jqx-menu-item-arrow-down-blue,
.jqx-icon-arrow-down-blue,
.jqx-icon-arrow-down-hover-blue,
.jqx-icon-arrow-down-selected-blue {
    background-image: none;
    font-family: jqx-icons;
}
.jqx-widget-blue .jqx-grid-group-expand-blue:after,
.jqx-grid-group-expand-blue:after,
.jqx-grid-column-sortdescbutton-blue:after,
.jqx-expander-arrow-top-blue:after,
.jqx-window-collapse-button-collapsed-blue:after,
.jqx-menu-item-arrow-down-blue:after,
.jqx-menu-item-arrow-down-selected-blue:after,
.jqx-menu-item-arrow-down-blue:after,
.jqx-icon-arrow-down-blue:after,
.jqx-icon-arrow-down-hover-blue:after,
.jqx-icon-arrow-down-selected-blue:after {
    content: var(--jqx-icon-arrow-down);
}

.jqx-tabs-arrow-left-blue,
.jqx-menu-item-arrow-left-selected-blue,
.jqx-menu-item-arrow-top-left,
.jqx-icon-arrow-left-blue,
.jqx-icon-arrow-down-left-blue,
.jqx-icon-arrow-left-selected-blue {
    background-image: none;
    font-family: jqx-icons;
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-tabs-arrow-left-blue:after,
.jqx-menu-item-arrow-left-selected-blue:after,
.jqx-menu-item-arrow-top-left:after,
.jqx-icon-arrow-left-blue:after,
.jqx-icon-arrow-down-left-blue:after,
.jqx-icon-arrow-left-selected-blue:after {
    content: var(--jqx-icon-arrow-left);
}

.jqx-widget-blue .jqx-grid-group-collapse-blue,
.jqx-grid-group-collapse-blue,
.jqx-tabs-arrow-right-blue,
.jqx-menu-item-arrow-right-selected-blue,
.jqx-menu-item-arrow-top-right-blue,
.jqx-icon-arrow-right-blue,
.jqx-icon-arrow-right-hover-blue,
.jqx-icon-arrow-right-selected-blue {
    background-image: none;
    font-family: jqx-icons;
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-widget-blue .jqx-grid-group-collapse-blue:after,
.jqx-grid-group-collapse-blue:after,
.jqx-tabs-arrow-right-blue:after,
.jqx-menu-item-arrow-right-selected-blue:after,
.jqx-menu-item-arrow-top-right-blue:after,
.jqx-icon-arrow-right-blue:after,
.jqx-icon-arrow-right-hover-blue:after,
.jqx-icon-arrow-right-selected-blue:after {
    content: var(--jqx-icon-arrow-right);
}

.jqx-tree-item-arrow-collapse-rtl-blue,
.jqx-tree-item-arrow-collapse-hover-rtl-blue {
    background-image: none;
}

.jqx-tree-item-arrow-collapse-rtl-blue:after,
.jqx-tree-item-arrow-collapse-hover-rtl-blue:after {
    content: var(--jqx-icon-arrow-left);
}

.jqx-menu-item-arrow-left-selected-blue {
    background-image: none;
}

.jqx-menu-item-arrow-right-selected-blue {
    background-image: none;
}

.jqx-input-button-content-blue {
    font-size: 10px;
}

.jqx-widget .jqx-grid-column-header-cell-blue {
    padding-top: 8px;
    padding-bottom: 8px;
    height:30px;
}

.jqx-widget .jqx-grid-row-cell-blue {
    padding-top: 8px;
    padding-bottom: 8px;
    height:30px;
}

.jqx-listbox-container-blue,
.jqx-calendar-container-blue {
    margin-left: -10px;
}
.jqx-calendar-blue.jqx-popup,
.jqx-listbox-blue.jqx-popup {
    margin-left: 9px;
}

.jqx-dropdownbutton-popup,
.jqx-calendar-blue.jqx-popup,
.jqx-listbox-blue.jqx-popup,
.jqx-grid-menu.jqx-popup  {
    transition: transform 0.25s ease-in-out, opacity 0.35s ease-in-out;
    transform: scaleY(0);
    opacity: 0;
    transform-origin: top left;
    display: block !important;
}

.jqx-dropdownbutton-popup.jqx-popup-show,
.jqx-calendar-blue.jqx-popup-show,
.jqx-listbox-blue.jqx-popup-show,
.jqx-grid-menu.jqx-popup-show {
    transform: scaleY(1);
    opacity: 1;
}

.jqx-widget-blue .jqx-grid-cell {
    border-color: var(--jqx-border);
    color: var(--jqx-background-color);
}

.jqx-widget-blue .jqx-grid-column-header, .jqx-widget-blue .jqx-grid-group-cell {
    border-color: var(--jqx-border);
    color: var(--jqx-surface-color);
    background: var(--jqx-surface);
}

.jqx-widget-blue .jqx-grid-column-header-blue {
    border-color: var(--jqx-border);
    font-size: 12px;
    color: var(--jqx-surface-color);
    font-weight: 500;
}
.jqx-widget-blue .jqx-grid-cell-blue {
    border-color: var(--jqx-border);
}
.jqx-widgets-blue .jqx-scheduler-cell-selected span{
    color: var(--jqx-background) !important;
}
.jqx-scheduler-time-column-blue,
.jqx-scheduler-toolbar-blue {
    background: var(--jqx-surface) !important;
    color: var(--jqx-surface-color) !important;
    border-color: var(--jqx-border) !important;
}

.jqx-widget-blue.jqx-scheduler-blue .jqx-grid-cell-blue,
.jqx-widget-blue.jqx-scheduler-blue .jqx-grid-column-header-blue {
    border-color: transparent;
    border-bottom-color: var(--jqx-border);
}

.jqx-widget-blue.jqx-scheduler-blue td.jqx-grid-cell-blue span{
    font-size: 10px;
    color: var(--jqx-background-color);
}
.jqx-widget-blue.jqx-scheduler-blue td.jqx-grid-cell-blue.jqx-scheduler-cell-hover span,
.jqx-widget-blue.jqx-scheduler-blue td.jqx-grid-cell-blue.jqx-scheduler-cell-selected span{
    color:var(--jqx-primary-color);
}

.jqx-passwordinput-password-icon-blue,
.jqx-passwordinput-password-icon-rtl-blue {
    background-image: none;
    font-family: jqx-icons;
    color:var(--jqx-background-color);
}
.jqx-passwordinput-password-icon-blue:after,
.jqx-passwordinput-password-icon-rtl-blue:after {
    content: var(--jqx-icon-visibility);
}

.jqx-combobox-blue .jqx-icon-close-blue {
    background-image: none;
    font-family: jqx-icons;
}

.jqx-combobox-blue .jqx-icon-close-blue:after {
    content: var(--jqx-icon-close);
}
.jqx-combobox-blue, .jqx-input-blue {
    border-color: var(--jqx-border);
    color: var(--jqx-background-color);
    background-color: var(--jqx-background);
}

.jqx-combobox-content-blue,
.jqx-datetimeinput-content-blue
 {
    border-color: transparent;
}


.jqx-combobox-content-focus-blue,
.jqx-combobox-state-focus-blue,
.jqx-numberinput-focus-blue {
    outline: none;
}

.jqx-input-group-blue {
    position: relative;
    display: inline-block;
    overflow: visible;
    border: none;
    box-shadow: none;
}

    .jqx-input-group-blue input {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
    }
    .jqx-input-group-blue textarea {
        width: 100%;
        height: 100%;
        outline: none;
        resize: none;
        border-left: none;
        border-right: none;
        border-top: none;
        border-bottom-color: var(--jqx-border);
    }
.jqx-numberinput-blue,
.jqx-maskedinput-blue
 {
    position:relative;
}
.jqx-numberinput-blue input {
    height:100% !important;
}

.jqx-input-blue.jqx-validator-error-element {
    border-color: transparent !important;
    border-bottom: 1px solid #df2227 !important;
}
.jqx-input-blue input,
.jqx-dropdownlist-state-normal-blue,
.jqx-combobox-state-normal-blue,
.jqx-numberinput-blue,
.jqx-maskedinput-blue,
.jqx-datetimeinput-blue
 {
    background: var(--jqx-surface);
    border-color: var(--jqx-surface);
    border-radius: 0;
    color: var(--jqx-surface-color);
    box-shadow: none;
    border-bottom: 1px solid var(--jqx-border);
    outline: none;
}
.jqx-numberinput-blue .jqx-action-button-blue{
    border-radius: 0;
    font-size:12px;
}
.jqx-numberinput-blue .jqx-action-button-blue > div {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.jqx-maskedinput-blue,
.jqx-combobox-blue,
.jqx-datetimeinput-blue {
    background: var(--jqx-background);
    color: var(--jqx-background-color);
    border-color: var(--jqx-background);
    border-bottom: 1px solid var(--jqx-border);
}
.jqx-combobox-blue .jqx-combobox-arrow-normal-blue,
.jqx-datetimeinput-blue .jqx-action-button-blue,
.jqx-datetimeinput-blue .jqx-action-button-rtl-blue
 {
    background-color: var(--jqx-surface);
    border-color: var(--jqx-surface);
    color:var(--jqx-surface-color);
}
    .jqx-datetimeinput-blue, .jqx-datetimeinput-blue > div,
    .jqx-numberinput-blue, .jqx-numberinput-blue > div,
    .jqx-maskedinput-blue, .jqx-maskedinput-blue > div,
    .jqx-dropdownlist-state-normal-blue, .jqx-dropdownlist-state-normal-blue > div, .jqx-dropdownlist-state-normal-blue > div > div,
    .jqx-combobox-state-normal-blue, .jqx-combobox-state-normal-blue > div, .jqx-combobox-state-normal-blue > div > div {
        overflow: visible !important;
    }

    .jqx-input-blue input:focus {
        border-radius: 0;
        box-shadow: none;
    }

.jqx-input-blue input, input[type="text"].jqx-input-blue, input[type="password"].jqx-input-blue, input[type="text"].jqx-widget-content-blue, input[type="textarea"].jqx-widget-content-blue, textarea.jqx-input-blue {
    font-size: var(--jqx-font-size);
    font-family: var(--jqx-font-family);
    resize: none;
    background: var(--jqx-background);
    color: var(--jqx-background-color);
    border: none;
    border-radius: 0;
    box-sizing:border-box;
    box-shadow: none;
    border-bottom: 1px solid var(--jqx-border);
}

input[type="text"].jqx-widget-content-blue,
input[type="textarea"].jqx-widget-content-blue {
    height: 100%;
}


.jqx-input-label {
    visibility:inherit;
}
.jqx-input-bar{
    visibility:inherit;
}
input:focus ~ .jqx-input-label-blue,
textarea:focus ~ .jqx-input-label-blue,
.jqx-input-widget-blue[hint=true] .jqx-input-label,
.jqx-text-area-blue[hint=true] .jqx-input-label,
.jqx-dropdownlist-state-selected-blue .jqx-input-label,
.jqx-dropdownlist-state-normal-blue[hint=true] .jqx-input-label,
.jqx-combobox-state-normal-blue[hint=true] .jqx-input-label,
.jqx-combobox-blue .jqx-input-label.focused,
.jqx-dropdownlist-blue .jqx-input-label.focused,
.jqx-datetimeinput-blue[hint=true] .jqx-input-label,
.jqx-maskedinput-blue[hint=true] .jqx-input-label,
.jqx-numberinput-blue[hint=true] .jqx-input-label,
.jqx-formattedinput-blue[hint=true] .jqx-input-label
 {
    top: -15px;
    font-size: 12px;
    color: var(--jqx-primary);
    opacity: 1;
}
.jqx-dropdownlist-blue[default-placeholder="true"] .jqx-input-label {
    visibility: hidden;
}


input:focus ~ .jqx-input-bar:before,
textarea:focus ~ .jqx-input-bar:before,
.jqx-dropdownlist-state-selected-blue .jqx-input-bar:before,
.jqx-dropdownlist-blue .jqx-input-bar.focused:before,
.jqx-combobox-blue .jqx-input-bar.focused:before,
.jqx-complex-input-group-blue .jqx-input-bar.focused::before,
.jqx-combobox-state-selected-blue .jqx-input-bar:before {
    width: 100%;
}
.jqx-complex-input-group-blue .jqx-fill-state-normal-blue {
    border-color: var(--jqx-border);
}
input[type="password"] {
    letter-spacing: 0.3em;
}

.jqx-input-widget-blue.jqx-rtl > input {
    direction: rtl
}

.jqx-input-label-blue {
    color: var(--jqx-background-color);
    font-size: 14px;
    font-weight: normal;
    position: absolute;
    pointer-events: none;
    left: 2px;
    top:10px;
    opacity: 0;
    top: calc(50% - 7px);
    transition: 300ms ease all;
}
.jqx-input-label.initial {
    transition: none;
}
.jqx-input-bar-blue {
    position: relative;
    display: block;
    z-index:1;
}

    .jqx-input-bar-blue:before {
        content: '';
        height: 2px;
        width: 0;
        bottom: 0px;
        position: absolute;
        background: var(--jqx-primary);
        transition: 300ms ease all;
        left: 0%;
    }
.jqx-formatted-input-spin-button-blue, .jqx-input-group-addon-blue {
    border-color: var(--jqx-background);
    color: var(--jqx-background-color);
    background: var(--jqx-background);
}
.jqx-dropdownlist-state-selected-blue,
.jqx-combobox-state-selected-blue {
    color: var(--jqx-primary);
    background: var(--jqx-primary-color);
    border-color: var(--jqx-primary-color);
}


.jqx-dropdownlist-state-normal-blue .jqx-icon-arrow-down-blue,
.jqx-combobox-state-normal-blue .jqx-icon-arrow-down-blue,
.sorticon.descending .jqx-grid-column-sorticon-blue,
.jqx-tree-item-arrow-expand-blue,
 .jqx-expander-header-blue .jqx-icon-arrow-down
 {
    transform: rotate(0deg);
    display: flex;
    align-items: center;
    transition: transform 0.2s ease-out;
}
.jqx-expander-header-blue .jqx-icon-arrow-up {
   transform: rotate(180deg);
   transition: transform 0.2s ease-out;
   font-family: jqx-icons;
   background-image: none;
}
.jqx-expander-header-blue .jqx-icon-arrow-up:after {
    content: var(--jqx-icon-arrow-down);
}
.jqx-tree-item-arrow-expand-blue,
.jqx-tree-item-arrow-collapse-blue
 {
    font-size: 16px;
}
.jqx-tree-item-arrow-expand-blue {
    transform: rotate(180deg);
}

.jqx-tree-item-arrow-expand-blue:after {
    content: var(--jqx-icon-arrow-up);
    margin-left: 2px;
}
.jqx-tree-item-arrow-collapse-blue
{
    transform: rotate(0deg);
    background-image: none;
    background-repeat: no-repeat;
    background-position: center;
    transition: transform 0.2s ease-out;
}
.jqx-dropdownlist-state-selected-blue .jqx-icon-arrow-down-blue,
.jqx-combobox-state-selected-blue .jqx-icon-arrow-down-blue,
.sorticon.ascending .jqx-grid-column-sorticon-blue
 {
    display: flex;
    align-items: center;
    transform: rotate(180deg);
    transition: transform 0.2s ease-out;
    left: -1px;
}
.jqx-combobox-state-selected-blue .jqx-icon-arrow-down-blue{
    left:-1px;
}
.jqx-listbox-container {
    margin-top: 1px;
}

input[type="text"].jqx-input-blue:-moz-placeholder, input[type="text"].jqx-widget-content-blue:-moz-placeholder, input[type="textarea"].jqx-widget-content-blue:-moz-placeholder, textarea.jqx-input-blue:-moz-placeholder {
    color: #999999;
}

input[type="text"].jqx-input-blue:-webkit-input-placeholder, input[type="text"].jqx-widget-content-blue:-webkit-input-placeholder, input[type="textarea"].jqx-widget-content-blue:-webkit-input-placeholder, textarea.jqx-input-blue:-webkit-input-placeholder {
    color: #999999;
}

input[type="text"].jqx-input-blue:-ms-input-placeholder, input[type="text"].jqx-widget-content-blue:-ms-input-placeholder, input[type="textarea"].jqx-widget-content-blue:-ms-input-placeholder, textarea.jqx-input-blue:-ms-input-placeholder {
    color: #999999;
}

.jqx-combobox-content-focus-blue, .jqx-combobox-state-focus-blue, .jqx-fill-state-focus-blue,
.jqx-numberinput-focus-blue {
    outline: none;
}

.jqx-popup-blue.jqx-fill-state-focus-blue {
    outline: none;
    border-color: var(--jqx-border) !important;
}

.jqx-datetimeinput-content, .jqx-datetimeinput-container {
    overflow: visible !important;
}
.jqx-text-area-blue, .jqx-text-area-blue > div {
    overflow:visible !important;
}
.jqx-text-area-element-blue {
   box-sizing: border-box;
}
.jqx-pivotgrid-content-wrapper.jqx-fill-state-normal-blue {
    border-color: var(--jqx-border);
}

.jqx-grid-cell-blue.jqx-grid-cell-selected-blue > .jqx-grid-group-expand-blue {
    background-image: none;
}

.jqx-grid-cell-blue.jqx-grid-cell-selected-blue > .jqx-grid-group-collapse-blue {
    background-image: none;
}

.jqx-grid-cell-blue.jqx-grid-cell-selected-blue > .jqx-grid-group-collapse-rtl-blue {
    background-image: none;
}

.jqx-grid-cell-blue.jqx-grid-cell-selected-blue > .jqx-grid-group-expand-rtl-blue {
    background-image: none;
}
.jqx-tabs-title-selected-top-blue, .jqx-tabs-selection-tracker-top-blue {
    border-color: transparent;
    filter: none;
    background: inherit;
    color: inherit;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
.jqx-grid-cell-filter-row-blue {
    background-color: var(--jqx-surface);
}

.jqx-tabs-title-blue, .jqx-ribbon-item-blue {
    color: inherit;
}
.jqx-ribbon-item-selected-blue {
    background: inherit;
}
.jqx-tabs-title-selected-bottom-blue,
.jqx-tabs-title-selected-top-blue
 {
    color: var(--jqx-primary);
    font-weight:500;
    padding-top:5px;
    padding-bottom:5px;
}
.jqx-tabs-title.jqx-fill-state-hover-blue {
    border-color: transparent;
}
.jqx-ribbon-item-blue {
    cursor: pointer;
}
.jqx-ribbon-item-selected-blue {
    color: var(--jqx-primary);
    font-weight:500;
    border-color: transparent;
}

.jqx-ribbon-item-hover-blue {
    background: transparent;
}

.jqx-ribbon-header-top-blue {
    border-color: transparent;
    border-bottom-color: var(--jqx-border);
}

.jqx-ribbon-header-bottom-blue {
    border-color: transparent;
    border-top-color: var(--jqx-border);
}

.jqx-ribbon-header-right-blue {
    border-color: transparent;
    border-left-color:var(--jqx-border);
}

.jqx-ribbon-header-left-blue {
    border-color: transparent;
    border-right-color:var(--jqx-border);
}

.jqx-tabs-title-selected-bottom-blue, .jqx-tabs-selection-tracker-bottom-blue {
    border-color: transparent;
    border-top: 1px solid var(--jqx-background);
    filter: none;
    background: var(--jqx-background);
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.jqx-tabs-blue, .jqx-ribbon-blue {
    border-color: transparent;
}

.jqx-tabs-header-blue {
    background: transparent;
}
.jqx-ribbon-header-blue {
    background: var(--jqx-surface);
    color: var(--jqx-surface-color);
}
.jqx-tabs-position-bottom .jqx-tabs-header-blue {
    border-color: transparent;
}
.jqx-layout-blue .jqx-tabs-header-blue, .jqx-layout-blue .jqx-ribbon-header-blue {
    background: var(--jqx-background);
    border-color: var(--jqx-border);
}
.jqx-tabs-title-bottom {
    border-color: transparent;
}
.jqx-tabs-title-hover-top-blue, .jqx-tabs-title-hover-bottom-blue, .jqx-tabs-header-blue {
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
    box-shadow: none !important;
    background: var(--jqx-surface);
    color: var(--jqx-surface-color);
}

.jqx-tabs-content-blue {
    box-sizing: border-box;
    border: 1px solid var(--jqx-border);
    border-top-color: transparent;
    padding:5px;
}
.jqx-tabs-bar-blue {
    position: absolute;
    bottom: 0;
    background: var(--jqx-primary);
    height: 2px;
    z-index:10;
    transition: .5s cubic-bezier(.35,0,.25,1);
}
.jqx-tabs-bar-blue.vertical {
    width: 2px;
}
.jqx-tabs-position-bottom .jqx-tabs-bar-blue {
    top: 0;
}


.jqx-layout-blue {
    background-color: var(--jqx-background);
}

.jqx-kanban-column-header-collapsed-blue {
    background: var(--jqx-surface);
    color: var(--jqx-surface-color);
}

.jqx-calendar-cell-blue {
    border-radius: 0px;
    font-size:12px;
}
.jqx-calendar-cell-blue.jqx-fill-state-pressed-blue {
    outline: 2px var(--jqx-primary);
    overflow: hidden;
    position: relative;
}
.jqx-calendar-cell-blue.jqx-fill-state-pressed-blue:after {
    content: '';
    width:  calc(100% - 4px);
    position: absolute;
    left: 0px;
    top: 0px;
    height: calc(100% - 4px);
    border: 2px solid var(--jqx-background);
}
.jqx-calendar-cell-year-blue,
.jqx-calendar-cell-decade-blue {
    border-radius: 25%;
}
.jqx-calendar-title-content-blue {
    font-weight:bold;
}
.jqx-calendar-column-cell-blue {
    color: var(--jqx-background-color);
    font-size:12px;
}

.jqx-icon-time-blue,
.jqx-icon-time-hover-blue,
.jqx-icon-time-pressed-blue {
    background-image: none;
    font-family: 'jqx-icons';
    display: flex;
    font-family: 'jqx-icons';
    font-size: 16px;
    align-content: center;
    justify-content: center;
    left: initial !important;
    margin-top: 0px;
    top: 0px;
    left: 0px;
    margin: 0;
    align-items: center;
    width: 100%;
    height: 100%;
}

.jqx-icon-time-blue:after,
.jqx-icon-time-hover-blue:after,
.jqx-icon-time-pressed-blue:after {
    content: var(--jqx-icon-arrow-down);
}

.jqx-icon-calendar-blue,
.jqx-icon-calendar-hover-blue,
.jqx-icon-calendar-pressed-blue {
	background-image: none;
    font-family: 'jqx-icons';
    left: 0;
    top: 0 !important;
    margin: 0 !important;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100% !important;
    height: 100% !important;
}
.jqx-icon-calendar-blue:after,
.jqx-icon-calendar-hover-blue:after,
.jqx-icon-calendar-pressed-blue:after {
    content: var(--jqx-icon-calendar);
}

.jqx-tabs-close-button-blue,
.jqx-tabs-close-button-selected-blue,
.jqx-tabs-close-button-hover-blue {
    background-image: none;
}

.jqx-tabs-close-button-blue:after {
    content: var(--jqx-icon-close);
}

.jqx-scrollbar-button-state-pressed-blue .jqx-icon-arrow-up-selected-blue {
    background-image: none;
}

.jqx-scrollbar-button-state-pressed-blue .jqx-icon-arrow-down-selected-blue {
    background-image: none;
}

.jqx-scrollbar-button-state-pressed-blue .jqx-icon-arrow-left-selected-blue {
    background-image: none;
}

.jqx-scrollbar-button-state-pressed-blue .jqx-icon-arrow-right-selected-blue {
    background-image: none;
}

.jqx-grid-cell-blue.jqx-grid-cell-selected-blue > .jqx-grid-group-expand-blue {
    background-image: none;
}

.jqx-grid-cell-blue.jqx-grid-cell-selected-blue > .jqx-grid-group-collapse-blue {
    background-image: none;
}

.jqx-grid-cell-blue.jqx-grid-cell-selected-blue > .jqx-grid-group-collapse-rtl-blue {
    background-image: none;
}

.jqx-grid-cell-blue.jqx-grid-cell-selected-blue > .jqx-grid-group-expand-rtl-blue {
    background-image: none;
}

.jqx-grid-group-collapse-blue {
    background-image: none;
}

.jqx-grid-group-collapse-rtl-blue {
    background-image: none;
}

.jqx-grid-group-expand-blue, .jqx-grid-group-expand-rtl-blue {
    background-image: none;
}

.jqx-icon-arrow-first-blue,
.jqx-icon-arrow-last-blue  {
    background-image: none;
    font-family: jqx-icons;
}
.jqx-icon-arrow-first-blue:after {
    content: var(--jqx-icon-first-page);
}
.jqx-icon-arrow-last-blue:after {
    content: var(--jqx-icon-last-page);
}

/* Ripple effect */
.ripple {
    position: relative;
    transform: translate3d(0, 0, 0);
    overflow:hidden;
}

.ink {
    display: block;
    position: absolute;
    pointer-events: none;
    border-radius: 0%;
    transform: scaleX(0);
    background: rgba(var(--jqx-primary-rgb),0.5);
    opacity: 0.25;
}


.outlined .ink, .flat .ink {
    background: rgba(var(--jqx-primary-rgb),0.5);
    overflow:hidden;
}

.ink.animate {
    animation: ripple .7s ease;
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.jqx-tree-blue .jqx-checkbox-blue {
    margin-top: 6px !important;
    border-radius: 0px !important;
}
.jqx-tree-item-arrow-expand-blue,
.jqx-tree-item-arrow-collapse-blue {
    margin-top: 6px !important;
}

.jqx-checkbox-blue .ripple,
.jqx-radiobutton-blue .ripple
 {
    overflow:visible;
}
.jqx-checkbox-blue .ink,
.jqx-radiobutton-blue .ink
 {
    transform: scale(0);
    background: var(--jqx-primary);
    border-radius: 50%;
}
.jqx-checkbox-blue .ink.animate,
.jqx-radiobutton-blue .ink.animate
 {
    animation: checkRipple 0.3s ease;
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.jqx-checkbox-blue .ink.active,
.jqx-radiobutton-blue .ink.active
 {
    opacity: 0.2;
    transform: scale(2);
}
.jqx-checkbox-default-blue.active .ink,
.jqx-radiobutton-default-blue.active .ink
 {
    opacity: 0.2;
    transform: scale(2);
}

/* Ripple effect */
/* Ripple effect */
.buttonRipple {
    background-position: center;
    transition: background 0.8s;
  }
  .buttonRipple.jqx-button-blue.jqx-fill-state-hover {
    color: var(--jqx-background-color-hover);
    background: var(--jqx-background-hover) radial-gradient(circle, transparent 1%, var(--jqx-background-hover) 1%) center/15000%;
  }
  .buttonRipple.jqx-button-blue.jqx-fill-state-pressed {
    color: var(--jqx-background-color-hover);
    background-color: rgba(var(--jqx-background-hover-rgb), 0.5);
    background-size: 100%;
    transition: background 0s;
  }
  .buttonRipple.jqx-button-blue.jqx-fill-state-hover.primary {
    color: var(--jqx-primary-color);
    background: var(--jqx-primary) radial-gradient(circle, transparent 1%, var(--jqx-primary) 1%) center/15000%;
  }
  .buttonRipple.jqx-button-blue.jqx-fill-state-pressed.primary {
    color: var(--jqx-primary-color);
    background-color: rgba(var(--jqx-primary-rgb), 0.8);
    background-size: 100%;
    transition: background 0s;
  }

@keyframes ripple {
    100% {
        opacity: 0;
        transform: scale(5);
        animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    }
}
@keyframes checkRipple {
    100% {
        opacity: 0.2;
        transform: scale(2);
        animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    }
}

.jqx-fill-state-pressed-blue .jqx-icon-delete-blue
{
    background-image: url('images/icon-delete-white.png');
}
.jqx-fill-state-pressed-blue .jqx-icon-edit-blue
{
    background-image: url('images/icon-edit-white.png');
}
.jqx-fill-state-pressed-blue .jqx-icon-save-blue
{
    background-image: url('images/icon-save-white.png');
}
.jqx-fill-state-pressed-blue .jqx-icon-cancel-blue
{
    background-image: url('images/icon-cancel-white.png');
}
.jqx-fill-state-pressed-blue .jqx-icon-search-blue
{
    background-image: url('images/search_white.png');
}
.jqx-fill-state-pressed-blue .jqx-icon-plus-blue
{
    background-image: url('images/plus_white.png');
}
.jqx-fill-state-pressed-blue .jqx-menu-minimized-button-blue {
   background-image: url('images/icon-menu-minimized-white.png');
}


.jqx-editor-toolbar-icon-blue {
    background: url('images/html_editor_white.png') no-repeat;
}

.jqx-fill-state-hover-blue .jqx-editor-toolbar-icon-fontsize-blue,
.jqx-fill-state-pressed-blue .jqx-editor-toolbar-icon-fontsize-blue,
.jqx-fill-state-hover-blue .jqx-editor-toolbar-icon-forecolor-blue,
.jqx-fill-state-pressed-blue .jqx-editor-toolbar-icon-forecolor-blue
{
        background: url('images/html_editor.png') no-repeat;
}

.jqx-editor-toolbar-button-blue{
    border-color: var(--jqx-border);
    box-shadow: none !important;
	color: var(--jqx-background-color);
}

.jqx-time-picker .jqx-main-container {
    background: var(--jqx-background);
}

/*applied to the timepicker*/
.jqx-needle-central-circle-blue {
	fill: var(--jqx-primary);
}
.jqx-time-picker-blue .jqx-label-blue {
    fill: var(--jqx-background-color);
}
.jqx-needle-blue {
	fill: var(--jqx-primary);
}
.jqx-time-picker .jqx-header .jqx-selected-blue:focus {
    outline: 2px solid var(--jqx-primary);
	box-shadow: 0px 0px 4px 2px rgba(0, 119, 190, 0.125);
}
.jqx-svg-picker-blue:focus {
	border: 1px solid var(--jqx-primary) !important;
}
.jqx-validator-hint-blue {
    background: #D94F43;
    border-color: #D94F43;
    padding: 10px;
}
.jqx-validator-hint-blue img {
    display: none;
}