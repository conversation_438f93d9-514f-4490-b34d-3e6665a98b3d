var moceanvehiclelist = {
    template: `
        <div id="manage_list_body">    
            <div class="project_manage1">
            <dv class="vr"></dv>
                <div class="project_title">
                    <div class="product_title_tool">
                        <button class="project_edit_exited" @click="moceanVehicleListPopupClosed"> {{ $t("나가기") }}</button>
                    </div>                    
                </div>
                <div class="mocean_title">
                    <label> {{ $t("차량 운행 현황") }}</label>
                </div>
                <br>
                <div class="mocean_tree_grid">
                    <jqx-grid ref="moceanVehicleGrid"  
                            @cellclick="moceanVehicleGridCellclick($event)"
                            :width="width"
                            :height="height"
                            :source="dataAdapter"
                            :columns="columns"
                            :columnsresize="true"
                            :theme="'aloagridtheme'"
                            >
                    </jpx-grid>
                 </div>
            </div>
        </div>
    `,
    props: {
    },

    data: function () {
        return {
            width: "90%",
            height : "80%",
            columns: [
                {text: Util.gridTitleStyle(14,"차량번호"), align: 'center', dataField: 'licensePlate', cellsAlign: 'center', width: 70},
                {text: Util.gridTitleStyle(14,"기사이름"), align: 'center', dataField: 'ridername', cellsAlign: 'center', width: 70},
                {text: Util.gridTitleStyle(14,"차대번호"), align: 'center', dataField: 'vinnumber', cellsAlign: 'center', width: 120},
                // {text: 'ev', align: 'center', dataField: 'ev', cellsAlign: 'center', width: 50},
                {text: Util.gridTitleStyle(14,"경도"), align: 'center', dataField: 'longitude', cellsAlign: 'center', width: 70},
                {text: Util.gridTitleStyle(14,"위도"), align: 'center', dataField: 'latitude', cellsAlign: 'center', width: 70},
                {text: Util.gridTitleStyle(14,"주소"), align: 'center', dataField: 'address', cellsAlign: 'center', width: 250},
                {text: Util.gridTitleStyle(14,"속도(km/h)"), align: 'center', dataField: 'speed', cellsAlign: 'center', width: 80},
                {text: Util.gridTitleStyle(14,"누적주행거리(km)"), align: 'center', dataField: 'odometer', cellsAlign: 'center', width: 120},
                {text: Util.gridTitleStyle(14,"연료잔량(%)"), align: 'center', dataField: 'fuelTankLoad', cellsAlign: 'center', width: 80},
                {text: Util.gridTitleStyle(14,"주행가능거리(km)"), align: 'center', dataField: 'evDrivingDistance', cellsAlign: 'center', width: 120},
                {text: Util.gridTitleStyle(14,"엔진회전수(rpm)"), align: 'center', dataField: 'rpm', cellsAlign: 'center', width: 110},
                {text: Util.gridTitleStyle(14,"냉장온도(℃)"), align: 'center', dataField: 'temperatureFirst', cellsAlign: 'center', width: 80},
                {text: Util.gridTitleStyle(14,"냉동온도(℃)"), align: 'center', dataField: 'temperatureSecond', cellsAlign: 'center', width: 80},
                {text: Util.gridTitleStyle(14,"엔진상태"), align: 'center', dataField: 'engine', cellsAlign: 'center', width: 70},
                {text: Util.gridTitleStyle(14,"충전상태"), align: 'center', dataField: 'charging', cellsAlign: 'center', width: 70},
                {text: Util.gridTitleStyle(14,"배터리전압(v)"), align: 'center', dataField: 'batVol', cellsAlign: 'center', minwidth: 90},
            ],

            dataAdapter: null,
            ridernameList: null,
        }
    },

    created: function () {
        this.moceanVehicleInitForm(true);
    },

    mounted() {
        // this.$refs.treeGrid.sortable = true;
        // this.$refs.treeGrid.checkboxes = true;
    },

    beforeCreate:function(){

    },

    watch: {
    },

    computed: {

    },

    methods: {
        moceanVehicleListPopupClosed: function(){
            window.parent.app.$emit(EVENT.MAIN.CLOSE_MOCEAN_VEHICLE_POPUP);
        },

        moceanVehicleInitForm: function(isPopup){
            let _this = this;

            if(isPopup)
                PopupUtil.showLoadingPopup("로딩 중...", "잠시만 기다려 주세요.");

            console.log("3초 마다 실행 되는지 확인이 필요함.");

            let localdata = [];
            let vehicleNumberList = [];
            RIDER_API.getMoceanLastVehicleStatuses(0, 10, null, null, {
                onSuccess: (response) => {
                    const moceanLastVehicleStatuses = response.data.content;
                    PopupUtil.dismissLoadingPopup();
                    moceanLastVehicleStatuses.forEach((vehicleStatus) => {
                        localdata.push({
                            licensePlate : vehicleStatus.licensePlate,
                            ridername : null,
                            vinnumber : vehicleStatus.vin,
                            // ev : vehicleStatus.isEv,
                            longitude : vehicleStatus.longitude,
                            latitude : vehicleStatus.latitude,
                            address : vehicleStatus.address,
                            speed : vehicleStatus.speed,
                            odometer : vehicleStatus.odometer,
                            fuelTankLoad : vehicleStatus.fuelTankLoad,
                            evDrivingDistance : vehicleStatus.evDrivingDistance,
                            rpm : vehicleStatus.rpm,
                            temperatureFirst : vehicleStatus.temperatureFirst == null ? "-" : vehicleStatus.temperatureFirst,
                            temperatureSecond : vehicleStatus.temperatureSecond == null ? "-" : vehicleStatus.temperatureSecond,
                            engine : Util.getTextFromMoceanEngineValue(vehicleStatus.engine),
                            charging : Util.getTextFromMoceanChargingValue(vehicleStatus.charging),
                            batVol : vehicleStatus.batVol,
                        });
                        vehicleNumberList.push(vehicleStatus.licensePlate);
                    });

                    if(isPopup) {
                        this.locationSource = {
                            dataType: 'json',
                            dataFields: [
                                {name: 'licensePlate', type: 'string'},
                                {name: 'ridername', type: 'string'},
                                {name: 'vinnumber', type: 'string'},
                                // { name: 'ev', type: 'string' },
                                {name: 'longitude', type: 'string'},
                                {name: 'latitude', type: 'string'},
                                {name: 'address', type: 'string'},
                                {name: 'speed', type: 'number'},
                                {name: 'odometer', type: 'number'},
                                {name: 'fuelTankLoad', type: 'string'},
                                {name: 'evDrivingDistance', type: 'string'},
                                {name: 'rpm', type: 'number'},
                                {name: 'temperatureFirst', type: 'string'},
                                {name: 'temperatureSecond', type: 'string'},
                                {name: 'engine', type: 'string'},
                                {name: 'charging', type: 'string'},
                                {name: 'batVol', type: 'string'},
                            ],
                            localdata: localdata
                        };

                        this.dataAdapter = new $.jqx.dataAdapter(this.locationSource);

                        this.$refs.moceanVehicleGrid.source = this.dataAdapter;

                        this.$refs.moceanVehicleGrid.refresh();

                        this.moceanVehicleRidersName(vehicleNumberList);
                    }else {
                        var indexof = 0;
                        localdata.forEach(rowdata => {
                            rowdata.ridername = _this.ridernameList[indexof];
                            this.$refs.moceanVehicleGrid.updaterow(indexof, rowdata);
                            indexof = indexof + 1;
                        });
                    }

                    this.updateRefresh();

                },
                onError: (error) => {
                    PopupUtil.dismissLoadingPopup();
                }
            });
        },

        moceanVehicleRidersName:function(vehicleNumberList){
            let _this = this;
            RIDER_API.getRidersNameList(vehicleNumberList, {
                onSuccess: (response) =>{
                    console.log(response);
                    _this.ridernameList = response.data;
                    var indexof = 0;
                    _this.ridernameList.forEach(name => {
                        this.$refs.moceanVehicleGrid.setcellvalue(indexof,'ridername',name);
                        indexof = indexof + 1;
                    });
                },onError: (error) => {
                }
            });
        },

        moceanVehicleGridCellclick: function (event) {

            if (event.args.columnindex === 0) {
                const value = event.args.row.bounddata;
                console.log(value.vinnumber);

                const aloaMap = document.getElementById('iframe-map').contentWindow.app.$refs.aloaMap;
                aloaMap.$emit(EVENT.MAP.SHOW_MOVEAN_TODAY_DRIVING_DAILY_VIEW, [{vinnumber: value.vinnumber}, {ridername:value.ridername}]);
            }

            //온도 cell 클릭시 클릭된 차량의 온도를 표시하는 페이지로 이동함.
            if (event.args.columnindex === 11 || event.args.columnindex === 12) {
                const value = event.args.row.bounddata;
                console.log(value.vinnumber);

                const aloaMap = document.getElementById('iframe-map').contentWindow.app.$refs.aloaMap;
                aloaMap.$emit(EVENT.MAP.SHOW_MOCEAN_REAL_TIME_TEMPERATURE_VIEW, {vinnumber: value.vinnumber});
            }
        },

        updateRefresh: function (){
            let _this = this;
            setTimeout(() => {
                _this.moceanVehicleInitForm(false);
            },  60000);
        }
    },
}