@charset "UTF-8";
@import url("/assets/common/css/font.css");


/** {	*/
/*    margin: 0px;*/
/*    padding: 0px;*/
/*    font-family: Noto Sans KR;*/
/*    font-weight: 500;*/
/*!*    border: 1px solid red;*!*/
/*}*/

#report_driver_list_body {
	position: absolute;
	top: 41px;
	width: 100%;
	height: calc(100% - 41px);
}

#report_driver_list_body .or02_window_close {
	position: absolute;
	top: 0px;
	right: 30px;
	width: 30px;
	height: 30px;
	border: 0px solid;
	outline: 0px;
	background-color: #0B3138;
	background-image: url("/assets/image/popup/bt_popup_close_n.svg");
}

#report_driver_list_body .bg{
	position: absolute;
	width: 	100%;
	height: 100%;
	background-color: #0B3138;
}



/*--------------------프로젝트 관리-------------------------*/


#report_driver_list_body .report_page_2{
	position: absolute;
	top: 0px;
	bottom: 0px;
	left: 0px;
	width: 100%;
	min-width: 1280px;
	height: 100%;
	background-color: #0B3138 ;
	z-index: 80;
	transform: translateY(0px);
    transition: transform 1s cubic-bezier(0.165, 0.84, 0.44, 1);
/*   border: 1px solid blue;*/
/*	display: none;*/
}




#report_driver_list_body .report_info_2{
	position: absolute;
	top:0px;
	left: 0px;
	width: 100%;
	height: 80px;
	background-color: #113E46;
}


#report_driver_list_body .report_data_2{
	position: relative;
	display: flex;
	width: 390px;
	height: 34px;
	margin-top: 23px;
	margin-left: 30px;
/*	border: 1px solid;*/
}




#report_driver_list_body .report_data_label_1_2{
	position: relative;
	margin-left: 10px;
	top:5px;
/*	width: 90px;*/
	height: 20px;
	color: #fff;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 14px;
/*	border: 1px solid;*/
}

#report_driver_list_body .report_data_label_2_2{
	position: relative;
	top:5px;
/*	left: 100px;*/
/*	width: 50px;*/	
	margin-left: 10px;
	height: 20px;
	color: #fff;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 14px;
/*	border: 1px solid;*/
}

#report_driver_list_body .report_data_search_2{
	position: absolute;
	top:22px;
	right: 190px;
	width: 250px;
	height: 36px;
	padding-left: 15px;
	outline: none;	
	background-color: #0B3138;
	border-radius: 3px;
	border: 1px solid #113E46;
	color: #FFFFFF;

}

#report_driver_list_body .report_data_search_2::placeholder{
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 14px;
	color: #889FA3;
}

#report_driver_list_body .report_data_search_2_icon{
	position: absolute;
	top: 25px;
	right: 195px;
	width: 30px;
	height: 30px;
	border: 0px solid;
	outline: 0px;
	background-color: #0B3138;
	background-image: url("/assets/image/report/ic_project_input_search.svg");
}

#report_driver_list_body .report_data_num_2{
	position: relative;
	left: 10px;
	width: 98px;
	height: 30px;
	padding-top:3px;
	padding-left: 12px;
	color: #6DEBAE;
	background-color: #0B3138;
	border: 1px solid #113E46;
	border-radius: 2px;
}

#report_driver_list_body .report_data_num_3{
	position: relative;
	left: 15px;
	width: 100px;
	height: 35px;
	padding-left: 10px;
	background-color: #0B3138;
	border: 1px solid #113E46;
	outline: none;
	color: #fff;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 14px;
	border-radius: 2px;
}

#report_driver_list_body .report_data_line_2{
	position: absolute;
	top: 10px;
	right: 168px;
	width: 1px;
	height: 60px;
	background-color: #0B3138;
}

#report_driver_list_body .report_data_view_area_2{
	position: absolute;
	top: 30px;
	right: 30px;
	width: 122px;
	height: 20px;
	color: #fff;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 14px;
	border:0px solid;
	outline: none;
	background-color: rgb(0,0,0);
}

#report_driver_list_body .report_data_view_icon_2{
	position: absolute;
	top:1px;
	right: 0px;
	width: 19px;
	height: 19px;
	background-image: url("/assets/image/report/ic_projectmenu.svg");
	background-repeat: no-repeat;
}


#report_driver_list_body .report_chart_left_text_2{
	position: absolute;
	top: 108px;
	left: 50px;
	width: 160px;
	height: 50px;
	color: #fff;
/*	border: 1px solid red;*/
}



#report_driver_list_body .report_chart_left_text_2_2{
	position: relative;
	display: flex;
	margin-left:10px;
	width: 50px;
	height: 18px;
	color: #6DEBAE;
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 14px;
/*	border: 1px solid red;*/
}


#report_driver_list_body .report_chart_left_icon_2{
	position: relative;
	top:6px;
	width: 10px;
	height: 10px;
	margin-right: 5px;
	background-image: url("/assets/image/report/ic_all_drivers.svg");
	background-repeat: no-repeat;
}

#report_driver_list_body .report_chart_left_text_3_2{
 	position: relative;
	top: 0px;
	left: 0px;
	width: 30px;
	height: 25px;
	margin-left: 10px;
	color: #6DEBAE; 
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 14px;
}


#report_driver_list_body .report_circle_1{
	position: relative;
	top: 8px;
	left: -5px;
	width: 6px;
	height: 6px;
	background-color: #CAF419;
	border-radius: 3px;
}

#report_driver_list_body .report_circle_2{
	position: relative;
	top: 8px;
	left: -5px;
	width: 6px;
	height: 6px;
	background-color: #FFA346;
	border-radius: 3px;
}

#report_driver_list_body .report_circle_3{
	position: relative;
	top: 8px;
	left: -5px;
	width: 6px;
	height: 6px;
	background-color: #F95F7B;
	border-radius: 3px;
}

#report_driver_list_body .report_d_chart_text_area_2{
	position: relative;
	width: 128px;
	height: 26px;
	color : #fff;
	margin-top: 2px;
	text-align: center;
	font-weight: 700;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 25px;
/*	border: 1px solid;*/

}


#report_driver_list_body .report_bar_chart_area_1_2{
	position: relative; /*absolute;*/
	bottom: 0px;
	/*top: 80px;*/
	width: 100%;
	margin-top: 2px;
	/*height: calc(100% - 80px);*/
	background-color: #0B3138;
/*	display: none;*/
/*	border: 1px solid;*/
}


#report_driver_list_body .report_bar_chart_area_1_2 li{
	position: relative;
	display: block;
/*	margin-top: 2px;*/
	width: 100%;
	min-height: 110px;
	background-color: #113E46;
	z-index: 50;	
}


#report_driver_list_body .report_bar_chart_area_u_2{
	position: relative;
	display: flex;
/*	border: 1px solid;*/
}




#report_driver_list_body .report_li_title{
	position: absolute;
	left: 0px;
	width: 460px;
	height: 110px;
/*	background-color: red;*/
	background-color: #113E46;
/*	border: 1px solid;*/
}





#report_driver_list_body .report_li_contents_area{
	position: absolute;
	left: 460px;
	width: calc(100% - 460px);
	height: 110px;
	background-color: #113E46;
/*	border: 1px solid;*/
}

#report_driver_list_body .report_li_group_name_contents_area{
	position: absolute;
	left: 460px;
	width: calc(100% - 460px);
	min-width: 950px;
	height: 110px;
	background-color: #113E46;
	/*	border: 1px solid;*/
}

#report_driver_list_body .report_li_contents{
	position: absolute;
	top:27px;
	left: 20px;
	width: calc(100% - 70px);
	height: 52px;
/*	border: 1px solid;*/
}

#report_driver_list_body .report_li_icon_1{
	position: absolute;
	top:35px;
	left: 40px;
	width: 34px;
	height: 34px;
	box-shadow: 4px 3px 10px 2px #222;
	background-image: url("/assets/image/report/ic_report_icon_1.svg");
	background-repeat: no-repeat;
	border: 0px;
	border-radius: 3px;
}

#report_driver_list_body .report_li_title_1{
	position: absolute;
	top: 25px;
	left: 95px;
	width: 250px;
	height: 25px;
	color: #6DEBAE;
	font-weight: 500;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 18px;
}

#report_driver_list_body .report_li_title_2{
	position: absolute;
	top: 56px;
	left: 95px;
	width: 260px;
	height: 25px;
	color: #fff;
	font-weight: 500;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 14px;
}



#report_driver_list_body .report_li_title_3{
	position: absolute;
	top: 25px;
	left: 145px;
	width: 250px;
	height: 25px;
	color: #fff;
	font-weight: 500;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 18px;
}

#report_driver_list_body .report_li_title_4{
	position: absolute;
	top: 56px;
	left: 145px;
	width: 260px;
	height: 25px;
	color: #83999D;
	font-weight: 500;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 14px;
}

#report_driver_list_body .report_li_title_5{
	position: absolute;
	top: 40px;
	left: 100px;
	width: 260px;
	height: 25px;
	color: #fff;
	font-weight: 500;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 18px;
}

#report_driver_list_body .report_li_title_pic{
	position: absolute;
	top:32px;
	left: 94px;
	width: 40px;
	height: 40px;
	background-image: url("/assets/image/report/bg_account_photo.svg");
	background-repeat: no-repeat;
/*	border: 1px solid;*/
}



#report_driver_list_body .report_li_c_label_1{
	position: absolute;
	display: flex;
	top: 0px;
	left: 0px;
	height: 23px;
	color: #fff;
	font-weight: 500;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 14px;
/*	border: 1px solid;*/
}

#report_driver_list_body .report_li_c_label_1_1{
	position: relative;
/*	display: flex;*/
	top: 0px;
	right: 0px;
	height: 23px;
	text-align: right;
	color: #6DEBAE;
	font-weight: 500;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 14px;
/*	border: 1px solid;*/
}

#report_driver_list_body .report_li_c_icon_1{
	position: relative;
	top:6px;
	width: 10px;
	height: 10px;
	margin-right: 5px;
	background-image: url("/assets/image/report/ic_all_drivers.svg");
	background-repeat: no-repeat;
}

#report_driver_list_body .report_li_c_icon_2{
	position: relative;
	top:4px;
	right: 0px;
	width: 17px;
	height: 17px;
	background-image: url("/assets/image/report/ic_report_place.svg");
	background-repeat: no-repeat;
}

#report_driver_list_body .report_li_c_icon_3{
	position: relative;
	top: 2px;
	right: 0px;
	width: 17px;
	height: 17px;
	background-image: url("/assets/image/report/ic_report_amount.svg");
	background-repeat: no-repeat;
}


#report_driver_list_body .report_li_c_label_2{
	position:absolute;
 	display: flex;
	top:0px;
	right: 50px;
	width: 400px;
	height: 23px;
	color: #aaa;
/*	border: 1px solid;*/
}

#report_driver_list_body .report_li_c_text_1_1{
 	position: relative;
 	display: flex;
	top: 2px;
	left: 0px;
	width: 120px;
	height: 23px;
	color: #889FA3; 
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	padding-left: 15px;
/*	border: 1px solid;*/
}

#report_driver_list_body .report_li_c_text_1_2{
 	position: relative;
	top: 1px;
	left: 0px;
	height: 23px;
	color: #fff; 
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
}

#report_driver_list_body .report_li_c_text_1_3{
 	position: relative;
	top: 0px;
	left: 0px;
	height: 25px;
	margin-left: 10px;
	color: #6DEBAE; 
	font-weight: 400;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 14px;
}

#report_driver_list_body .report_li_c_button{


}

#report_driver_list_body .report_li_c_input{
	position: absolute;
	display: none;
	top: 30px;
	right: 0px;


}

#report_driver_list_body .report_li_c_label{
	position: absolute;
	right: 0px;
	width: 18px;
	height: 18px;
	margin-left: 10px;
	background-image: url("/assets/image/report/bt_top_account_p.svg");
	background-repeat: no-repeat;
}

#report_driver_list_body .report_li_c_input:checked ~ .report_li_c_label{
	background-image: url("/assets/image/report/bt_top_account_n.svg");
}





#report_driver_list_body .report_li_c_chart{
	position: relative;
	display: flex;
	top: 30px;
	left: 2px;
	width: calc(100% - 22px);
	height: 18px;
	overflow: hidden;
/*	border: 1px solid;*/
}



#report_driver_list_body .report_li_c_contents_1{
	position: relative;
	top: 0px;
	width: 100%;
	margin-top: 110px;
	background-color: #0B3138;
/*	border: 1px solid ;*/
	z-index: 10;
}





#report_driver_list_body .report_li_c_tab_area{
	position: relative;
	display: flex;
	top: 40px;
	left: 70px;
	width: calc(100% - 140px);
/*	height: 23px;*/
/*	border: 1px solid red;*/
}

#report_driver_list_body .report_li_c_tab_area_1{
	position: relative;
	display: flex;
	margin-top: 55px;
	left: 70px;
	overflow-x: hidden;
	width: calc(100% - 140px);
	max-height: 500px;
/*	height: 23px;*/
/*	border: 1px solid red;*/
}

#report_driver_list_body .report_li_c_tab_area_1_1{
	position: relative;
	display: flex;
	left: 70px;
	overflow-x: hidden;
	margin-top: 5px;
	margin-bottom: 50px;
	width: calc(100% - 140px);
	max-height: 500px;
/*	height: 23px;*/
/*	border: 1px solid red;*/
}





#report_driver_list_body .report_li_c_tab_area_1_1::-webkit-scrollbar { width: 2px; height: 2px; }



#report_driver_list_body .report_li_c_tab_area_2{
	position: relative;
	display: flex;
	margin-top: 55px;
	left: 70px;
	overflow-x: hidden;
	width: calc(100% - 140px);
	max-height: 500px;
/*	height: 23px;*/
/*	border: 1px solid red;*/
}



#report_driver_list_body .report_li_c_tab_area_1 table{
	width: 100%;
	border-collapse: collapse;
}

#report_driver_list_body .report_li_c_tab_area_1_1 table{
	width: 100%;
	border-collapse: collapse;
}

#report_driver_list_body .report_li_c_tab_area_1 tr{
	height: 25px;
	color: #fff;
	font-weight: 300;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 10px;

}

#report_driver_list_body .report_li_c_tab_area_1_1 tr{
	height: 30px;
	color: #fff;
	font-weight: 300;	
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 12px;
}

#report_driver_list_body .report_li_c_tab_area_1 table thead tr td{
	background-color: #1E4148;
	font-size: 10px;
}


#report_driver_list_body .report_li_c_tab_area_1_1 table tbody tr td{
	background-color: #153940;
	font-size: 12px;
	font-weight: 400;

}




#report_driver_list_body .report_li_c_td_1{
	position: relative;
	width: 12%;
	padding-left: 30px;
}

#report_driver_list_body .report_li_c_td_2{
	position: relative;
	width: 32%;
	padding-left: 30px;
	border-left: 1px solid #0B3138;
}

#report_driver_list_body .report_li_c_td_3{
	position: relative;
	width: 9%;
	padding-left: 30px;
	border-left: 1px solid #0B3138;
}

#report_driver_list_body .report_li_c_td_4{
	position: relative;
	width: 12%;
	padding-left: 30px;

}

#report_driver_list_body .report_li_c_td_5{
	position: relative;
	width: 32%;
	padding-left: 30px;
}

#report_driver_list_body .report_li_c_td_7{
	position: relative;
	width: 20%;
	padding-left: 30px;
	border-left: 1px solid #0B3138;
}
#report_driver_list_body .report_li_c_td_8{
	position: relative;
	width: 12%;
	padding-left: 30px;
	border-left: 1px solid #0B3138;
}

#report_driver_list_body .report_li_c_td_9{
	position: relative;
	width: 20%;
	padding-left: 30px;
}
#report_driver_list_body .report_li_c_td_10{
	position: relative;
	width: 12%;
	padding-left: 30px;
}

#report_driver_list_body .report_li_c_td_d_1{
	position: relative;
	width: 7%;
	padding-left: 30px;
}

#report_driver_list_body .report_li_c_td_d_2{
	position: relative;
	width: 12%;
	padding-left: 30px;
}


#report_driver_list_body .report_li_c_td_d_3{
	position: relative;
	width: 17%;
	padding-left: 30px;
}

#report_driver_list_body .report_li_c_td_d_4{
	position: relative;
	width: 10%;
	padding-left: 30px;
}









#report_driver_list_body .report_li_c_td_5 div{
	font-size: 12px;
}

#report_driver_list_body .report_li_c_td_5 .report_li_c_label_2{
	left: 25px;
	top: 3px;
/*	border: 1px solid;*/
}

#report_driver_list_body .report_li_c_td_6{
	position: relative;
	width: 9%;
	color: #fff;
	font-weight: 300;
	padding-left: 30px;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 12px;
}


#report_driver_list_body .report_li_c_tab_1{
	position: relative;
	top: 4px;
	left: 0px;
	/*width: 66px;*/
	height: 23px;
	color: #fff; 
	font-weight: 400;
	padding-left: 20px;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
/*	border: 1px solid blue;*/
}

#report_driver_list_body .report_li_c_tab_２{
	position: absolute;
	top: 4px;
	right: 0px;
	/*width: 66px;*/
	width: 200px;
	height: 23px;
	color: #83999D;
	font-weight: 400;
	padding-left: 10px;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	/*	border: 1px solid blue;*/
}

#report_driver_list_body .report_li_c_tab_num{
	position: relative;
	top: 4px;
	left: 0px;
	/*width: 66px;*/
	height: 23px;
	color: #6DEBAE; 
	font-weight: 400;
	padding-left: 7px;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
/*	border: 1px solid blue;*/
}

#report_driver_list_body .report_li_c_tab_icon_1{
	position: absolute;
	top:1px;
	width: 17px;
	height: 17px;
	left: 0px;
	background-image: url("/assets/image/report/ic_all_time.svg");
	background-repeat: no-repeat;
}

#report_driver_list_body .report_li_c_tab_icon_2{
	position: absolute;
	top:1px;
	width: 17px;
	height: 17px;
	left: 0px;
	background-image: url("/assets/image/report/ic_all_distance.svg");
	background-repeat: no-repeat;
}

#report_driver_list_body .report_li_c_tab_icon_3{
	position: absolute;
	top:1px;
	width: 17px;
	height: 17px;
	left: 0px;
	background-image: url("/assets/image/report/ic_all_plan.svg");
	background-repeat: no-repeat;
}

#report_driver_list_body .report_li_c_tab_icon_4{
	position: absolute;
	top:-5px;
	width: 34px;
	height: 34px;
	left: 175px;
	box-shadow: 4px 3px 10px 2px #222;
	background-image: url("/assets/image/report/bt_report_result_n_noshadow.svg");
	background-repeat: no-repeat;
	border:0px;
	outline:0px;
	cursor: pointer;
	background-color: #1E4148;
}

#report_driver_list_body .chart_bar_1{
	position: absolute;
	top:-5px;
	left: -148px;
	width: 500px;
	height: 500px;
	border: 1px solid;	
	z-index: 50;
}

#report_driver_list_body .chart_bar_2{
	position: absolute;
	width: 50%;
	height: 300px;
	border: 1px solid;	
	z-index: 50;
}


#report_driver_list_body .chart-container{
 	position: relative;
 	left: 0px;
 	width: calc(100% - 20px); 
	height:14px;

}


#report_driver_list_body .chart-container canvas{
	position: relative;
	top:-5px;
	left:-15px;
	width: 100%;
	height:30px;
	background-color: #4A6B71;
}


#report_driver_list_body .project_manage_page_area {
	position: relative;
	top: 0px;
	margin: 0 auto;
	width: fit-content;
	height: 45px;
	padding-bottom: 6px;
	font-weight: 500;
	font-family: 'Noto Sans KR', sans-serif;
	font-size: 13px;
	color: #083338;
	border: 0px solid;
}

#report_driver_list_body .project_manage_page_first {
	position: relative;
	top: 10px;
	left: 0px;
	width: 15px;
	height: 45px;
	outline: 0px;
	background-color: #113E46;
	background-image: url("/assets/image/report/bt_report_page_first.svg");
	border: 0px solid;
}

#report_driver_list_body .project_manage_page_end {
	position: relative;
	top: 10px;
	left: 0px;
	width: 15px;
	height: 45px;
	outline: 0px;
	background-color: #113E46;
	background-image: url("/assets/image/report/bt_report_page_end.svg");
	border: 0px solid;
}

#report_driver_list_body .project_manage_page_pre {
	position: relative;
	top: 10px;
	left: 0px;
	width: 15px;
	height: 45px;
	outline: 0px;
	background-color: #113E46;
	background-image: url("/assets/image/leftpanel/bt_leftlist_close.svg");
	border: 0px solid;
}

#report_driver_list_body .st_search_list_pageNum {
	position: relative;
	top: -8px;
	width: -moz-fit-content;
	width: fit-content;
	padding: 5px;
	height: 17px;
	font-size: 13px;
	font-weight: 500;
	outline: none;
	background-color: rgba(0, 0, 0, 0);
	font-family: 'Noto Sans KR', sans-serif;
	color: #fff;
	border: 0px solid;
}

#report_driver_list_body .st_search_list_pageNum.pick {
	color: #6DEBAE;
}

#report_driver_list_body .project_manage_page_next {
	position: relative;
	top: 10px;
	left: 0px;
	width: 15px;
	height: 45px;
	outline: 0px;
	background-color: #113E46;
	background-image: url("/assets/image/leftpanel/bt_leftlist_open.svg");
	border: 0px solid;
}

#report_driver_list_body .project_page_number {
	position: relative;
	top: -9px;
	left: 0px;
	width: 110px;
	height: 45px;
	outline: 0px;
	background-color: #083338;
	border: 0px solid;
}

#report_driver_list_body .menu_font {
	color: #fff;
	font-weight: 600;
}