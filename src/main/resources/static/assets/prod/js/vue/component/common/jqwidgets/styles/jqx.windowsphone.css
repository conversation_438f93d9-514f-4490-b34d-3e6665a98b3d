.jqx-rc-tl-windowsphone
{
    -moz-border-radius-topleft: 0px;
    -webkit-border-top-left-radius: 0px;
    border-top-left-radius: 0px;
}
.jqx-rc-tr-windowsphone
{
    -moz-border-radius-topright: 0px;
    -webkit-border-top-right-radius: 0px;
    border-top-right-radius: 0px;
}
.jqx-rc-bl-windowsphone
{
    -moz-border-radius-bottomleft: 0px;
    -webkit-border-bottom-left-radius: 0px;
    border-bottom-left-radius: 0px;
}
.jqx-rc-br-windowsphone
{
    -moz-border-radius-bottomright: 0px;
    -webkit-border-bottom-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
/*top rounded Corners*/
.jqx-rc-t-windowsphone
{
    -moz-border-radius-topleft: 0px;
    -webkit-border-top-left-radius: 0px;
    border-top-left-radius: 0px;
    -moz-border-radius-topright: 0px;
    -webkit-border-top-right-radius: 0px;
    border-top-right-radius: 0px;
}
/*bottom rounded Corners*/
.jqx-rc-b-windowsphone
{
    -moz-border-radius-bottomleft: 0px;
    -webkit-border-bottom-left-radius: 0px;
    border-bottom-left-radius: 0px;
    -moz-border-radius-bottomright: 0px;
    -webkit-border-bottom-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
/*right rounded Corners*/
.jqx-rc-r-windowsphone
{
    -moz-border-radius-topright: 0px;
    -webkit-border-top-right-radius: 0px;
    border-top-right-radius: 0px;
    -moz-border-radius-bottomright: 0px;
    -webkit-border-bottom-right-radius: 0px;
    border-bottom-right-radius: 0px;
}
/*left rounded Corners*/
.jqx-rc-l-windowsphone
{
    -moz-border-radius-topleft: 0px;
    -webkit-border-top-left-radius: 0px;
    border-top-left-radius: 0px;
    -moz-border-radius-bottomleft: 0px;
    -webkit-border-bottom-left-radius: 0px;
    border-bottom-left-radius: 0px;
}
/*all rounded Corners*/
.jqx-rc-all-windowsphone
{
    -moz-border-radius: 0px;
    -webkit-border-radius: 0px;
    border-radius: 0px;
}
.jqx-widget-windowsphone {
    font-size: 16px; 
    font-family: 'Segoe WP', 'segoe ui', arial, sans-serif;
    color: inherit;
}
.jqx-widget-content-windowsphone{font-size: 16px; font-family: 'Segoe WP', 'segoe ui', arial, sans-serif; border-color: #2F2F2F; color: #ffffff; background-color: #000;}
input[type=text].jqx-input-windowsphone, input[type=password].jqx-input-windowsphone, .jqx-input-group-addon-windowsphone{
   font-size: 16px; 
   font-family: 'Segoe WP', 'segoe ui', arial, sans-serif;
   background: #BEBEBE;
   border-color: #D2D2D2;
   color: #000;
 }
.jqx-widget-header-windowsphone{font-size: 16px;  font-family: 'Segoe WP', 'segoe ui', arial, sans-serif; color: #ffffff; border-color:#35353A; background-color:#3E3E42;}
.jqx-fill-state-normal-windowsphone, .jqx-fill-state-hover-windowsphone{font-size: 16px; font-family: 'Segoe WP', 'segoe ui', arial, sans-serif; border-color: #35353A; color: #ffffff; background: #3E3E42;}
.jqx-combobox-arrow-normal-windowsphone {
  background: #BEBEBE;
  border-color: #BEBEBE;
}
.jqx-dropdownlist-state-normal-windowsphone {
   background: #BEBEBE;
   border-color: #D2D2D2;
   color: #000;
}
.jqx-listbox-windowsphone, .jqx-listmenu-windowsphone, .jqx-listmenu-item-windowsphone, .jqx-calendar-windowsphone .jqx-widget-content-windowsphone {
    background: #000;
}

.jqx-fill-state-focus-windowsphone { border-color: #2F2F2F; }
.jqx-button-windowsphone {
   padding: 10px 7px;
   border: 2px solid #fff; 
   background-color: #000;
}
.jqx-buttongroup-windowsphone .jqx-button-windowsphone {
   border: 1px solid #fff; 
}
.jqx-listmenu-header-windowsphone .jqx-button-windowsphone, .jqx-grid-pager-windowsphone .jqx-button-windowsphone {
    background-color:#3E3E42;
    border: 1px solid #35353A;
}
.jqx-fill-state-pressed-windowsphone{border-color:#008a00; color: #fff; background-color:#008a00;}
.jqx-listmenu-header-windowsphone .jqx-button-windowsphone.jqx-fill-state-pressed-windowsphone {
border-color:#008a00; color: #fff; background-color:#008a00;
}

.jqx-fill-state-disabled-windowsphone {
    color: #898989;
}
.jqx-combobox-windowsphone {
   border-color: #D2D2D2;
 }
.jqx-input-windowsphone, .jqx-datetimeinput-content-windowsphone, .jqx-input-content-windowsphone, .jqx-combobox-content-windowsphone, .jqx-combobox-input-windowsphone{
   font-size: 16px; 
   font-family: 'Segoe WP', 'segoe ui', arial, sans-serif;
   background: #BEBEBE;
   border-color: #D2D2D2;
   color: #000;
}
.jqx-combobox-content-windowsphone {
    border-right-color: #BEBEBE;
}
.jqx-combobox-content-rtl-windowsphone {
    border-left-color: #BEBEBE;
}

.jqx-dropdownlist-state-normal-windowsphone .jqx-icon-arrow-down-windowsphone {
    background-image: url('images/metro-icon-down.png');
}
.jqx-dropdownlist-state-selected-windowsphone .jqx-icon-arrow-down-windowsphone {
    background-image: url('images/metro-icon-down-white.png');
}
.jqx-combobox-state-focus-windowsphone .jqx-combobox-arrow-normal-windowsphone .jqx-icon-arrow-down-windowsphone {
    background-image: url('images/metro-icon-down-white.png');
}
.jqx-combobox-state-focus-windowsphone, .jqx-combobox-state-focus-windowsphone .jqx-combobox-content-windowsphone, .jqx-combobox-state-focus-windowsphone .jqx-combobox-input-windowsphone,
.jqx-combobox-state-focus-windowsphone .jqx-combobox-arrow-normal-windowsphone
{
    background-color:#008a00;
    border-color: #008a00;
    color: #fff;
}
.jqx-combobox-multi-item-windowsphone {
    background-color:#008a00;
    padding: 7px;
    border-color: #008a00;
}
.jqx-combobox-multi-item-windowsphone a:link {
    margin-top: -2px;
}
.jqx-numberinput-windowsphone {
    border-color: #000;
}
.jqx-scrollbar-state-normal-windowsphone, .jqx-grid-bottomright-windowsphone, .jqx-panel-bottomright-windowsphone, .jqx-listbox-bottomright-windowsphone{background-color:#3E3E42;}
.jqx-widget-windowsphone .jqx-grid-column-header-windowsphone, .jqx-grid-cell-windowsphone, .jqx-widget-windowsphone .jqx-grid-cell-windowsphone, .jqx-widget-windowsphone .jqx-grid-group-cell-windowsphone, .jqx-grid-group-cell-windowsphone{font-size: 16px;  font-family: 'segoe ui', arial, sans-serif; border-color: #1C1C1E; background-color: #000; color: #fff;}
.jqx-tabs-title-selected-bottom-windowsphone, .jqx-tabs-selection-tracker-bottom-windowsphone, .jqx-tabs-title-selected-top-windowsphone, .jqx-tabs-selection-tracker-top-windowsphone{color: #ffffff; border-color:#35353A; border-bottom:1px solid #252526; background:#008a00}
.jqx-widget-windowsphone .jqx-grid-cell-alt-windowsphone, .jqx-widget-windowsphone .jqx-grid-cell-sort-windowsphone, .jqx-widget-windowsphone .jqx-grid-cell-pinned-windowsphone, .jqx-widget-windowsphone .jqx-grid-cell-filter-windowsphone, .jqx-grid-cell-sort-alt-windowsphone, .jqx-grid-cell-filter-alt-windowsphone, .jqx-grid-cell-pinned-windowsphone, .jqx-grid-cell-alt-windowsphone, .jqx-grid-cell-sort-windowsphone{ background-color:#3E3E42; color: #fff;}
.jqx-menu-vertical-windowsphone{}
.jqx-widget-windowsphone .jqx-grid-cell-windowsphone, .jqx-widget-windowsphone .jqx-grid-column-header-windowsphone, .jqx-widget-windowsphone .jqx-grid-group-cell-windowsphone { border-color: #35353A;}

.jqx-widget-windowsphone .jqx-grid-column-menubutton-windowsphone, .jqx-widget-windowsphone .jqx-grid-column-sortascbutton-windowsphone, .jqx-widget-windowsphone .jqx-grid-column-sortdescbutton-windowsphone, .jqx-widget-windowsphone .jqx-grid-column-filterbutton-windowsphone {
    background-color: transparent;
    border-color: #35353A;
}
.jqx-window-header-windowsphone, .jqx-input-button-header-windowsphone, .jqx-calendar-title-header-windowsphone, .jqx-grid-windowsphone .jqx-widget-header-windowsphone, .jqx-grid-header-windowsphone, .jqx-grid-column-header-windowsphone {font-size: 16px; font-family: 'Segoe WP', 'segoe ui', arial, sans-serif; border-color: #35353A; color: #ffffff; background: #3E3E42;}
.jqx-grid-column-menubutton-windowsphone {
    background-image: url('images/metro-icon-down-white.png');
 }
.jqx-widget-windowsphone .jqx-grid-cell-selected-windowsphone, .jqx-grid-cell-selected-windowsphone{ background-color:#008a00 !important; border-color: #008a00 !important; font-size: 16px;  color:#fff !important}
.jqx-widget-windowsphone .jqx-grid-cell-hover-windowsphone, .jqx-grid-cell-hover-windowsphone{ background-color:#3E3E42;}
 /*applied to the column's sort button when the sort order is ascending.*/
 .jqx-grid-column-sortascbutton-windowsphone {
    background-image: url('images/metro-icon-up-white.png');
 }
.jqx-grid-column-sortdescbutton-windowsphone {
    background-image: url('images/metro-icon-down-white.png');
}
.jqx-checkbox-default-windowsphone {
    background: black;
    border-color: white;
}
.jqx-checkbox-check-checked-windowsphone{background:transparent url(images/wp_check_white.png) center center no-repeat}
.jqx-checkbox-check-indeterminate-windowsphone{background:transparent url(images/wp_check_indeterminate_white.png) center center no-repeat}
.jqx-checkbox-hover-windowsphone, .jqx-radiobutton-hover-windowsphone {
    background-color: #000;
    border-color: #fff;
}
.jqx-radiobutton-default-windowsphone {
    background: #000;
    border-color: #fff;
}
.jqx-radiobutton-check-checked-windowsphone {
    background: #fff;
    border-color: #fff;
}
.jqx-window-header-windowsphone {
   background: #1f1f1f;
   border-bottom-color: #1f1f1f !important;
}
.jqx-window-content-windowsphone {
    background: #1f1f1f;
 }
.jqx-window-windowsphone {
   border-color: #0c0c0c;
}
.jqx-scrollbar-thumb-state-normal-horizontal-windowsphone, .jqx-scrollbar-thumb-state-normal-windowsphone {
    background: #686868; border-color: #686868;
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    border-radius: 10px;
}
.jqx-scrollbar-thumb-state-hover-horizontal-windowsphone, .jqx-scrollbar-thumb-state-hover-windowsphone {
    background: #9E9E9E; border-color: #9E9E9E;
}
.jqx-scrollbar-thumb-state-pressed-horizontal-windowsphone, .jqx-scrollbar-thumb-state-pressed-windowsphone {
    background: #ffffff; border-color: #ffffff;
}
.jqx-scrollbar-button-state-normal-windowsphone
{
    border: 1px solid #3E3E42; 
    background: #3E3E42;
}
/*applied to the scrollbar buttons in hovered state.*/
.jqx-scrollbar-button-state-hover-windowsphone
{
    border: 1px solid #3E3E42;
    background: #3E3E42;
}
/*applied to the scrollbar buttons in pressed state.*/
.jqx-scrollbar-button-state-pressed-windowsphone
{
    border: 1px solid #3E3E42;
    background: #3E3E42;
}

/*icons*/
.jqx-window-collapse-button-windowsphone
{
    background-image: url(images/metro-icon-up-white.png);
}
.jqx-window-collapse-button-collapsed-windowsphone {
  background-image: url(images/metro-icon-down-white.png);
}
.jqx-icon-arrow-up-windowsphone, .jqx-expander-arrow-bottom-windowsphone, .jqx-menu-item-arrow-up-windowsphone
{
    background-image: url('images/metro-icon-up-white.png');
}
.jqx-icon-arrow-down-windowsphone, .jqx-expander-arrow-top-windowsphone, .jqx-tree-item-arrow-expand-windowsphone, .jqx-tree-item-arrow-expand-hover-windowsphone, .jqx-menu-item-arrow-down-windowsphone
{
    background-image: url('images/metro-icon-down-white.png');
}
.jqx-icon-arrow-left-windowsphone, .jqx-menu-item-arrow-left-windowsphone
{
    background-image: url('images/metro-icon-left-white.png');
}
.jqx-icon-arrow-right-windowsphone, .jqx-menu-item-arrow-right-windowsphone, .jqx-tree-item-arrow-collapse-windowsphone, .jqx-tree-item-arrow-collapse-hover-windowsphone
{
    background-image: url('images/metro-icon-right-white.png') !important;
}
.jqx-tabs-arrow-left-windowsphone, .jqx-tree-item-arrow-collapse-rtl-windowsphone, .jqx-tree-item-arrow-collapse-hover-rtl-windowsphone
{
    background-image: url('images/metro-icon-left-white.png');
}
.jqx-tabs-arrow-right-windowsphone
{
    background-image: url('images/metro-icon-right-white.png');
}
.jqx-menu-item-arrow-up-selected-windowsphone, .jqx-icon-arrow-up-selected-windowsphone{background-image:url('images/metro-icon-up-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-down-selected-windowsphone, .jqx-icon-arrow-down-selected-windowsphone{background-image:url('images/metro-icon-down-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-left-selected-windowsphone, .jqx-icon-arrow-left-selected-windowsphone{background-image:url('images/metro-icon-left-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-menu-item-arrow-right-selected-windowsphone, .jqx-icon-arrow-right-selected-windowsphone{background-image:url('images/metro-icon-right-white.png');background-repeat:no-repeat;background-position:center;}
.jqx-window-close-button-windowsphone, .jqx-icon-close-windowsphone, .jqx-tabs-close-button-windowsphone, .jqx-tabs-close-button-hover-windowsphone, .jqx-tabs-close-button-selected-windowsphone{background-image:url(images/close_white.png);  background-repeat:no-repeat;  background-position:center}
.jqx-listbox-feedback-windowsphone {
    border-top: 1px dashed #fff;
}
.jqx-listitem-state-normal-touch-windowsphone {
    padding-top: 5px;
    padding-bottom: 5px;
}
.jqx-scrollbar-windowsphone .jqx-icon-arrow-up-selected-windowsphone{background-image:url('images/metro-icon-up-white.png'); background-repeat:no-repeat; background-position:center;}
.jqx-scrollbar-windowsphone .jqx-icon-arrow-down-selected-windowsphone{background-image:url('images/metro-icon-down-white.png'); background-repeat:no-repeat; background-position:center;}
.jqx-scrollbar-windowsphone .jqx-icon-arrow-left-selected-windowsphone{background-image:url('images/metro-icon-left-white.png'); background-repeat:no-repeat; background-position:center;}
.jqx-scrollbar-windowsphone .jqx-icon-arrow-right-selected-windowsphone{background-image:url('images/metro-icon-right-white.png');background-repeat:no-repeat; background-position:center;}
.jqx-combobox-arrow-normal-windowsphone .jqx-icon-arrow-down-windowsphone {
    background-image: url('images/metro-icon-down.png');
}
.jqx-combobox-arrow-selected-windowsphone .jqx-icon-arrow-down-windowsphone {
    background-image: url('images/metro-icon-down-white.png');
}

.jqx-input-button-content-windowsphone
{  
    font-size: 13px;
    background: #000;
    border-color: #2F2F2F;
}
.jqx-input-button-header-windowsphone {
    padding-top: 2px !important;
    padding-bottom: 2px !important;
    background: #3E3E42;
    border-color: #2F2F2F;
}
.jqx-slider-button-windowsphone
{
    -moz-border-radius: 0px;
    -webkit-border-radius: 0px;
    border-radius: 0px;
    padding: 4px !important;
}
.jqx-slider-slider-windowsphone {
    width: 6px;
    -moz-border-radius: 0px;
    -webkit-border-radius: 0px;
    border-radius: 0px;
    background: #fff;
    border-color: #D5D5D5;
}
.jqx-listmenu-header-label-windowsphone {
    font-size: 24px;
    margin-left: -5px;
}
.jqx-listmenu-separator-windowsphone {
    font-size: 16px; 
    font-family: 'Segoe WP', 'segoe ui', arial, sans-serif;
}
.jqx-listmenu-item-label-windowsphone {
    display: inline-block;
    width: 100%;
    height: 100%;
}
.jqx-listmenu-item-windowsphone a:link, .jqx-listmenu-item-windowsphone a:visited {
    display: inline-block;
    text-decoration: none;
    color: inherit;
    font-size: 16px;
    color: #fff;
    width: 100%;
    height: 100%;
    padding: 15px;
}
.jqx-listmenu-item-windowsphone {
    padding: 0px;
}
.jqx-dropdownlist-state-normal-windowsphone, .jqx-dropdownlist-state-hover-windowsphone, .jqx-dropdownlist-state-selected-windowsphone,
.jqx-scrollbar-button-state-hover-windowsphone, .jqx-scrollbar-button-state-normal-windowsphone, .jqx-scrollbar-button-state-pressed-windowsphone,
.jqx-scrollbar-thumb-state-normal-horizontal-windowsphone, .jqx-scrollbar-thumb-state-hover-horizontal-windowsphone, .jqx-scrollbar-thumb-state-pressed-horizontal-windowsphone,
.jqx-scrollbar-thumb-state-normal-windowsphone, .jqx-scrollbar-thumb-state-pressed-windowsphone, .jqx-button-windowsphone, .jqx-tree-item-hover-windowsphone, .jqx-tree-item-selected-windowsphone,
.jqx-tree-item-windowsphone, .jqx-menu-item-windowsphone, .jqx-menu-item-hover-windowsphone, .jqx-menu-item-selected-windowsphone, .jqx-menu-item-top-windowsphone, .jqx-menu-item-top-hover-windowsphone, 
.jqx-menu-item-top-selected-windowsphone, .jqx-slider-button-windowsphone, .jqx-slider-slider-windowsphone
 {
    -webkit-transition: background-color 100ms linear;
     -moz-transition: background-color 100ms linear;
     -o-transition: background-color 100ms linear;
     -ms-transition: background-color 100ms linear;
     transition: background-color 100ms linear;
}
.jqx-switchbutton-windowsphone {
    -moz-border-radius: 0px; 
    -webkit-border-radius: 0px; 
    border-radius: 0px;
    border: 2px solid #FFFFFF;
}
.jqx-switchbutton-thumb-windowsphone {
    width: 16px;
    background: #fff;
    border: 2px solid #000;
}
.jqx-switchbutton-label-on-windowsphone {
    background: #008a00;
    color: #008a00;
    border-color: #000;
}
.jqx-switchbutton-label-off-windowsphone {
    background: #000;
    color: #000;
}

.jqx-switchbutton-wrapper-windowsphone {
}
.jqx-icon-arrow-first-windowsphone
{
    background-image: url('images/icon-first-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-arrow-last-windowsphone
{
    background-image: url('images/icon-last-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-progressbar-text-windowsphone {
    font-size: 16px;
}
.jqx-grid-group-collapse-windowsphone {
    background-image: url(images/metro-icon-right-white.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-grid-group-collapse-rtl-windowsphone
{
    padding-right: 0px;
    background-image: url(images/metro-icon-left-white.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-grid-group-expand-windowsphone, .jqx-grid-group-expand-rtl-windowsphone
{
    padding-right: 0px;
    background-image: url(images/metro-icon-down-white.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
}
.jqx-grid-cell-windowsphone.jqx-grid-cell-selected-windowsphone>.jqx-grid-group-expand-windowsphone,
.jqx-grid-cell-windowsphone.jqx-grid-cell-hover-windowsphone>.jqx-grid-group-expand-windowsphone {
    background-image: url('images/metro-icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-windowsphone.jqx-grid-cell-selected-windowsphone>.jqx-grid-group-collapse-windowsphone,
.jqx-grid-cell-windowsphone.jqx-grid-cell-hover-windowsphone>.jqx-grid-group-collapse-windowsphone {
    background-image: url('images/metro-icon-right-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-windowsphone.jqx-grid-cell-selected-windowsphone>.jqx-grid-group-collapse-rtl-windowsphone,
.jqx-grid-cell-windowsphone.jqx-grid-cell-hover-windowsphone>.jqx-grid-group-collapse-rtl-windowsphone {
    background-image: url('images/metro-icon-left-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-grid-cell-windowsphone.jqx-grid-cell-selected-windowsphone>.jqx-grid-group-expand-rtl-windowsphone,
.jqx-grid-cell-windowsphone.jqx-grid-cell-hover-windowsphone>.jqx-grid-group-expand-rtl-windowsphone {
    background-image: url('images/metro-icon-down-white.png');
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-tree-grid-expand-button-windowsphone {
    margin-top: 0px;
}
.jqx-tree-grid-checkbox-windowsphone, .jqx-tree-grid-collapse-button-windowsphone {
    margin-top: 3px;
}
.jqx-grid-table-windowsphone {
    border-color: #000;
}
.jqx-icon-search-windowsphone
{
    background-image: url(images/search_white.png);
    background-repeat: no-repeat;
    background-position: center;
}
.jqx-icon-calendar-windowsphone, .jqx-icon-calendar-hover-windowsphone, .jqx-icon-calendar-pressed-windowsphone {
    background-image: url('images/icon-calendar-white.png');
}
.jqx-icon-time-windowsphone, .jqx-icon-time-hover-windowsphone, .jqx-icon-time-pressed-windowsphone {
    background-image: url('images/icon-time-white.png');
}
.jqx-calendar-cell-today-windowsphone {
    color: black;
}
.jqx-menu-minimized-button-windowsphone {
   background-image: url('images/icon-menu-minimized-white.png');
}
.jqx-editor-toolbar-icon-windowsphone {
    background: url('images/html_editor_white.png') no-repeat;
}
.jqx-file-upload-file-name-windowsphone{padding:3px;}
.jqx-file-upload-file-row-windowsphone{ height: 40px;}
.jqx-layout-windowsphone
{
    background-color: #35353A;
}
.jqx-layout-pseudo-window-pin-icon-windowsphone
{
    background-image: url("images/pin-white.png");
}
.jqx-layout-pseudo-window-pinned-icon-windowsphone
{
    background-image: url("images/pinned-white.png");
}
.jqx-scheduler-windowsphone, .jqx-scheduler-toolbar-windowsphone, .jqx-scheduler-time-column-windowsphone, .jqx-scheduler-toolbar-details-windowsphone {
    color: white !important;
    background: black !important;
}
/*applied to the timepicker*/
.jqx-time-picker .jqx-header .jqx-hour-container-windowsphone:hover {
	background-color: rgb(0, 138, 0);
}
.jqx-time-picker .jqx-header .jqx-minute-container-windowsphone:hover {
	background-color: rgb(0, 138, 0);
}
.jqx-time-picker .jqx-header .jqx-am-container-windowsphone:hover {
	background-color: rgb(0, 138, 0);
}
.jqx-time-picker .jqx-header .jqx-pm-container-windowsphone:hover {
	background-color: rgb(0, 138, 0);
}
.jqx-svg-picker-windowsphone:focus {
	border: 1px solid rgb(77, 119, 193) !important;
}
.jqx-label-windowsphone {
	fill: rgb(158, 158, 158);
}
.jqx-time-picker .jqx-header .jqx-selected-windowsphone:focus {
    outline: 2px solid rgb(158, 158, 158);
}
.jqx-svg-picker-windowsphone:focus {
	border: 1px solid rgb(73, 68, 55) !important;
}
.jqx-main-container-windowsphone {
	background: none;
}