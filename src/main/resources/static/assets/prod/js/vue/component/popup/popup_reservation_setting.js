var reservationTimeEditPopup = {
    template: `
<!-- =================상단 타이틀================ --> 
        <div class="modal-mask">
           <div class="modal-wrapper">
              <div class="reservation_setting">
                 <div class="title">{{address}}</div>
                 <!-- <div class="title_line"></div> -->
                 <button class="detail_button" @click="clickShowInfo">{{$t("상세정보")}}</button>
                 <button class="window_close" @click="closePopup"></button>
                 <div class="input_section">
                    <div class="left_list_section">
                       <div class="left_list_section_title">{{$t("물품명")}}</div>
                       <input type="text" name=""  :placeholder="$t('물품명')" class="left_list_text" v-model="form.itemName" >
                       <!-- <div class="left_list_text1_line"></div> -->
                    </div>
                    <div class="left_list_section">
                       <div class="left_list_section_title">{{$t("예약시간")}}</div>
                       <input type="datetime-local" class="left_list_text" ref="inputReservationTime"  :placeholder="$t(('예약시간')" v-model="form.inputReservationTime" @change="changedReservationTime" >
<!--                        <div class="left_list_text1_line"></div>   -->
<!--                       <button class="sequence_button"></button>  날짜 입력 하고 버튼을 연결을 못시키겠다 -->
                    </div>
                    <div class="left_list_section">
                       <div class="left_list_section_title"> {{ deliveryOrder.mode = Constant.RESERVATION_MODE.NORMAL ? '방문허용시간'  : ( this.deliveryOrder.isPickup ? '준비소요시간' : '전달소요시간' ) }}</div>
                        <select class="left_list_select" name="watingTime" v-model="form.inputLeadTimeDuration" >                            
					        <option v-for ="leadTime in leadTimeDurationOptions"  v-bind:value="leadTime.value" :class=" leadTime.value == 0 ? 'option1':'options2' " > {{leadTime.text}}</option>
				        </select>
                    </div>
                 </div>
                 <button class="submit_button" @click="clickSave" >{{$t("저장")}}</button>
              </div>
           </div>
        </div>
    `,
    props: {
        deliveryOrder: {
            type: Object,
            required: true
        },
        popupCallback: {
            type: Function,
            required: true
        },
        address :{
            type: String,
        },
    },
    computed: {
        form: function() {//computed객체를 v-model로 연결하는게 꺼림직 하지만 제대로 동작하니 놔두자
            return {
                itemName : this.deliveryOrder.itemName ? this.deliveryOrder.itemName : "",
                inputReservationTime  : this.deliveryOrder.inputReservationTime ? this.deliveryOrder.inputReservationTime : TimeUtil.getSimulationStartDateTime(),
                inputLeadTimeDuration : this.deliveryOrder.inputLeadTimeDuration ? this.deliveryOrder.inputLeadTimeDuration : 0,
            };
        }
    },

    data: function() {
        return {
            leadTimeDurationOptions: [
                {text: '시간선택(분)', value: 0 },
                {text: '60분', value: 60},
                {text: '50분', value: 50},
                {text: '40분', value: 40},
                {text: '30분', value: 30},
                {text: '20분', value: 20},
                {text: '10분', value: 10},
            ],
            isChangedReservationTime : false,
        }
    },

    methods: {
        changedReservationTime(){
            this.isChangedReservationTime = true;
        },
        
        clickSave () {
            //삭제 - 저장하면 무조건 inputReservationTime 은 바뀐다.
            // if( !this.isChangedReservationTime ){//시간이 바뀌지 않으면 현재 시간으로 잘못 설정되므로 다시 이전 값을 넣어준다
            //     this.form.inputReservationTime = this.deliveryOrder.inputReservationTime;
            // }

            if( this.popupCallback ){
                this.popupCallback( "save", this.form );
            }

            this.$emit('dismiss' );
        },

        clickShowInfo(){
            if( this.popupCallback ){
                this.popupCallback( "dest_info");
            }
            this.$emit('dismiss' );
        },

        closePopup () {
            if( this.popupCallback ) {
                this.popupCallback("close");
            }
            this.$emit('dismiss');
        },

    }
};