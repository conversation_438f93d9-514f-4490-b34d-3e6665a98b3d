@charset "UTF-8";
@import url("/assets/common/css/font.css");
*{
	font-family: Noto Sans KR;
	padding: 0px;
	margin: 0px; 
}

.search_popup_guidanceSetting{
	position:absolute;
	top:50%;
	left:50%;
	margin:-143px 0 0 -250px;

	display: block;
	width: 500px;
	height: 287px;
	border-radius: 5px;
	box-shadow: 5px 2px 5px #00000040;
	background-color: #FFFFFF;
/*	border:1px solid;*/
}


.search_popup_gs_name_area{
	position: relative;
	top:5px;
	display: flex;
	width: 500px;

}

.search_popup_gs_name_icon{
	position: relative;
	top: 3px;
	left: 20px;
	width: 30px;
	height: 30px;
	background-image: url("/assets/image/popup/icon_setting_option.svg");
}

.search_popup_gs_name_text{
	position: relative;
	top: 7px;
	left: 20px;
	width: 100px;
	height: 21px;
	font-size: 14px;
	overflow: hidden;
	font-family: Noto Sans KR;
	font-weight:700;
	color: #1D3244;
}

.search_popup_gs_name_close_icon{
	position: relative;
	right: -330px;
	float: right;
	width: 30px;
	height: 30px;
	background-image: url("/assets/image/popup/bt_map_popup_close_W.svg");
}

.search_popup_gs_name_area{
	position: relative;
	top:5px;
	display: flex;
	width: 500px;

}


.search_popup_gs_setting1{
display: flex;
border:1px solid;
}


.search_popup_gs_menu_text{

	font-family: Noto Sans KR;
	font-weight:600;
	font-size: 12px; 
	color: #1D3244;
}

.search_popup_gs_menu_text_disabled{

	font-family: Noto Sans KR;
	font-weight:600;
	font-size: 12px;
	color: gray;
}

.search_popup_address_sepLine{
	position: absolute;
	top:146px;
	left: 25px;
	width: 450px;
	height: 1px;
	background-color: #E6E6E6;
}






.search_popup_gs_menu1{
	position: absolute;
	top:80px;
	left:140px;
	width: 300px;
	height: 30px;
}
.search_popup_gs_menu1_text{
	position: absolute;
	top:85px;
	left:30px;
	width: 100px;
	height: 30px;	
	font-family: Noto Sans KR;
	font-weight:600;
	font-size: 12px; 
	color: #1D3244;
}


.search_popup_gs_menu2{
	position: absolute;
	top:108px;
	left:140px;
	width: 300px;
	height: 30px;
}

.search_popup_gs_menu2_text{
	position: absolute;
	top:113px;
	left:30px;
	width: 100px;
	height: 30px;
	font-family: Noto Sans KR;
	font-weight:600;
	font-size: 12px; 
	color: #1D3244;
}

.search_popup_gs_menu3{
	position: absolute;
	top:167px;
	left:140px;
	width: 300px;
	height: 30px;
}

.search_popup_gs_menu3_text{
	position: absolute;
	top:172px;
	left:30px;
	width: 100px;
	height: 30px;
	font-family: Noto Sans KR;
	font-weight:600;
	font-size: 12px; 
	color: #1D3244;
}




.search_popup_gs_button{
	position: absolute;
	top:225px;
	left:170px;	
	width: 160px;
	height: 30px;
	font-size: 14px;
	font-weight: 700px;
	color:#fff;
	text-align: center;
	border :0px solid;
	padding-top: 1px;
	background-color: #FF5A00;
	border-radius: 3px
}




.search_popup_gs label {
  display: block;
  cursor: pointer;
  line-height: 2.5;
  font-size: 1.5em;
}

.search_popup_gs .rbt2{
	position: absolute;
	left: 100px;
	color : gray;
}
/*

[type="radio"] {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px; margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}

[type="radio"] + span:before {
  content: '';
  display: inline-block;
  width: 1em;
  height: 1em;
  vertical-align: -0.25em;
  border-radius: 1em;
  border: 0.125em solid #fff;
  margin-right: 0.75em;
  transition: 0.5s ease all;
  box-shadow: 0 0 0 0.15em #aaa;
}

[type="radio"]:checked + span:before {
  background: #1CEFCE;
  box-shadow: 0 0 0 0.25em #aaa;
}

.rbt1{
	position: absolute;
	left: 10px;
}
.rbt2{
	position: absolute;
	left: 100px;
}

.rbt3{
	position: absolute;
	left: 190px;
}

*/
