// const URL_GET_MPP = "/api/management/mpp";

self.importScripts('../../../../../../prod/js/misc/util.js');
self.importScripts('../../../../../../prod/js/misc/map_util.js');
self.importScripts('../../../../../../prod/js/misc/demo_util.js');
self.importScripts('../../../../constant/constant.js');
self.importScripts('../../../../../../common/js/manager/mpp_manager.js');

self.onmessage = function (event) {
	updateSimulationInfo(event.data.country, event.data.riders, event.data.timeUnit, event.data.reqUrl, event.data.simulationTime );
};

function updateSimulationInfo(country, riders, timeUnit, reqUrl, simulationTime ) {
	riders.forEach(async (rider) => {


		let destination = rider.drivingDest;
		if (  ( rider.drivingDest && destination.deliveryStatus != Constant.DELIVERY_STATUS.COMPLETED )  ) { //EV_Demo 조건 추가 시속 0km로 표시되게 하기 위해서
			// totalUnit = destination.estimatedSeconds;
			postMessage({
				type: "MOVE_RIDER_DEST",
				riderId: rider.riderId,
				timeUnit: timeUnit,//EV_Demo
				simulationTime: simulationTime,
			});
		}
		// MPP 경로에서 움직이는 경우
		else {
			if (!rider.mpp || rider.mpp.paths.length == 0 ) {
				return;
			}

			postMessage({
				type: "MOVE_RIDER_MPP",
				riderId: rider.riderId,
				timeUnit: timeUnit,//EV_Demo
				simulationTime: simulationTime,
			});
		}

	});

	postMessage({
		type: "COMPLETED"
	});
}


function transferFailed(event) {
	postMessage({
		type: "FAILED",
		event: event
	});
}

function transferCanceled(event) {
	postMessage({
		type: "CANCELED",
		event: event
	});
}

function transferComplete(event) {
	console.log("The transfer is complete.");
}