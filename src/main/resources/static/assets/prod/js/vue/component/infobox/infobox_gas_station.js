var gasStationInfoBox = {
    template: `
          <div class="gs_info_popup" >
        
                <button class="bt_close"  @click="$emit('close')" ></button>
                <div class="station_pic"></div>
                <div class="station_name">{{stationName}}</div>
                <div class="station_address">{{station.detail}}</div>        
        
        <!-- 주유소일떄 
                <div class="price_area">
                  <div class="fual_type">휘발유</div>
                  <div class="fual_price">1200원</div>
                  <div class="section_bar"></div>
                  <div class="fual_type">경유</div>
                  <div class="fual_price">1000원</div>
                </div>        
        -->
        <!-- 전기충전소일떄 -->
                <div class="price_area">
<!--                  <div class="gs_info_popup_text_3">kWh</div>-->
<!--                  <div class="gs_info_popup_text_4">200원</div>-->
                  <div class="fual_type">kWh</div>
                  <div class="fual_price">220원</div>
                </div>
                 
        
                <div class="gs_info_popup_etc">
                  <div class="coordinate">{{station.x}}</div>
                  ,                  
                  <div class="coordinate">{{station.y}}</div>
                  <div class="section_bar"></div>
                  <div class="phone_num">02-1224-1234</div>
                </div>
        
                <button v-bind:class= " param.buttonEnabled ? 'bt_add' : 'bt_add_disabled' "  @click="$emit('clicked')" >{{ param.selectMode ? '경유지 추가' : '경유지 삭제'}}</button>
          </div>

    `,
    props: {
        station:{
            type : Object,
            required : true
        },

        param:{
            type : Object,
            required : true
        }
    },

    computed:{
        stationName : function() {
            return DemoUtil.getShortWord( this.station.name, 12 );
        },
    },

};