spring:
  profiles: dev

  datasource:
    url: *************************************
    username: logisteq_dev
    password: logisteq9090

  redis:
    session:
      port: 6379
    tracks:
      port: 6379

  jpa:
    show-sql: false
    generate-ddl: true
    hibernate:
      ddl-auto: update

  auth:
    kep:
      ocean-api-url: http://laas-skillbuilder.sandbox.onkakao.net/api/openapi/solution/v0/custom/foodist/dispatch/register
      ocean-api-token: eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJqdW5nYW5nLW1ucC1kZXYiLCJPQ0VBTl9BUElfVVNFUiI6IntcImFwaUlkXCI6XCJqdW5nYW5nLW1ucC1kZXZcIixcImFjdGl2ZVwiOlwibGFhc2RldlwiLFwiY29tcGFueUlkXCI6MTgyMCxcImF1dGhvcml0eVR5cGVcIjpcIkxTUFwifSIsImlhdCI6MTY3NTc3NDYzMiwiZXhwIjoxOTkxMTM0NjMyfQ.xhYoUa1xoOszX7OElVKCmDId5VlFQr7BkcC32h6ev5A
      company-code: "foodist-dev"
      shipper-code: "FOODIST_TEST"

  web:
    version: v1.0.66(2022-12-06)

aloa:
  arch:
    proxy-server:
      url: https://aloa-dev.logisteq.com
  system:
    privacy:
      vendor: aloa-dev
      external:
        url: http://aloa-dev.logisteq.com:3001
