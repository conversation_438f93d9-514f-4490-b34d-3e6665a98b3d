spring:
  profiles: local-to-qa

  datasource:
    url: ************************************************
    username: logisteq_qa
    password: logisteq9090
    hikari:
      maximumPoolSize: 10
    initialization-mode: never

  jpa:
    show-sql: false
    generate-ddl: false
    hibernate:
      ddl-auto: none

  redis:
    session:
      host: aloa-qa.logisteq.com
      port: 31088
    tracks:
      host: aloa-qa.logisteq.com
      port: 31087

  auth:
    kep:
      ocean-api-url: https://laas-skillbuilder.cbt.kakaoi.io/api/openapi/solution/v0/custom/foodist/dispatch/register
      ocean-api-token: eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJkZWxpYm94IiwiT0NFQU5fQVBJX1VTRVIiOiJ7XCJhcGlJZFwiOlwiZGVsaWJveFwiLFwiYWN0aXZlXCI6XCJsYWFzY2J0XCIsXCJjb21wYW55SWRcIjo5NjIsXCJhdXRob3JpdHlUeXBlXCI6XCJMU1BcIn0iLCJpYXQiOjE2Nzg4NTQ3MTQsImV4cCI6MTk5NDIxNDcxNH0.s7Ly4fQjhp6zqgyTbeiIR6nmvUo8oXtGlYGNf5mAoao
      company-code: "foodist-qa"
      shipper-code: "FOODIST_TEST"

eureka:
  instance:
    appname: tms-local-service
  client:
    enabled: false  # not use eureka
    serviceUrl:
      defaultZone: http://admin:<EMAIL>:8763/eureka/

feign:
  client:
    config:
      track-service:
        url: http://aloa-qa.logisteq.com:31075
      common-service:
        url: http://aloa-qa.logisteq.com:31082
      auth-service:
        url: http://aloa-qa.logisteq.com:31086
      geofence-service:
        url: http://aloa-qa.logisteq.com:6976
      tms-stat-service:
        url: http://aloa-qa.logisteq.com:31084

server:
  port: 5800

# https://techblog.woowahan.com/9232/
management:
  endpoints:
    enabled-by-default: false
    jmx:
      exposure:
        exclude:
        include: health,mappings
  endpoint:
    health:
      enabled: true
      show-details: always
    mappings:
      enabled: true

mqtt:
  host: ws://aloa-qa.logisteq.com
  port: 31179
  external-endpoint: ${mqtt.host}:${mqtt.port}${mqtt.path}

aws:
  root-folder-name: qa

logging:
  level:
    org.hibernate.type.descriptor.sql.BasicBinder: trace

log-history:
  user-access:
    enabled: false
  location-offer:
    enabled: false

scheduler:
  joins-delificate:
    enabled: false
  rider-attendance:
    enabled: false
  project-auto-create:
    enabled: false
  department-project-auto-create:
    enabled: false
  notice:
    enabled: false
  privacy-disposed:
    enabled: false
  get-24h-order:
    enabled: false

event-listener:
  joins-delificate:
    enabled: false
  hmg:
    enabled: false
  glovis:
    enabled: false
  efoodist:
    enabled: false
  oliveyoung:
    enabled: false
  thehyundai:
    enabled: false

aloa:
  system:
    privacy:
      record-enabled: false
  arch:
    proxy-server:
      url: https://aloa-qa.logisteq.com
  storage:
    upload:
      excel: false
  fcm:
    topic-prefix-of-server: qa  # [a-zA-Z0-9-]로 구성해야 합니다.
